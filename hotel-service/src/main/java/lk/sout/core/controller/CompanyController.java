package lk.sout.core.controller;

import lk.sout.core.entity.Company;
import lk.sout.core.service.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/company")
public class CompanyController {

    @Autowired
    CompanyService companyService;

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    private ResponseEntity<?> save(@RequestBody Company company) {
        try {
            return ResponseEntity.ok(companyService.save(company));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/saveCompanyLogo", method = RequestMethod.POST)
    private ResponseEntity<?> saveCompanyLogo(@RequestParam("logo") String image) {
        try {
            return ResponseEntity.ok(companyService.saveLogo(image));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findCompany", method = RequestMethod.GET)
    private ResponseEntity<?> findCompany() {
        try {
            return ResponseEntity.ok(companyService.findInfo());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


}
