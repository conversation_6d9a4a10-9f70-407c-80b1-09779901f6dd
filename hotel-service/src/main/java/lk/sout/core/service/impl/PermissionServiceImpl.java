package lk.sout.core.service.impl;

import lk.sout.core.entity.Permission;
import lk.sout.core.entity.Module;
import lk.sout.core.repository.PermissionRepository;
import lk.sout.core.service.ModuleService;
import lk.sout.core.service.PermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PermissionServiceImpl implements PermissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionServiceImpl.class);

    @Autowired
    PermissionRepository permissionRepository;

    @Autowired
    ModuleService moduleService;

    @Override
    public boolean save(Permission permission) {
        try {
            permissionRepository.save(permission);
            LOGGER.info(permission.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Save Metadata failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public void saveIfUnavailable(String name, String route,String moduleName,String icon) {
        try {
            if (searchPermission(route) == null) {
                Module module = moduleService.searchModule(moduleName);
                if (module != null) {
                    Permission permission = new Permission(name, route, true, icon, module);
                    permissionRepository.save(permission);
                    LOGGER.info("Saved permission: " + name);
                } else {
                    LOGGER.warn("Module not found for permission: " + name);
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Save permission failed: " + ex.getMessage());
        }
    }


    @Override
    public Permission searchPermission(String route) {
        try {
            return permissionRepository.findByRoute(route);
        } catch (Exception ex) {
            LOGGER.error("searchMetaData failed: " + ex.getMessage());
            return null;
        }

    }

    @Override
    public List<Permission> findAll() {
        return permissionRepository.findAll();
    }

}
