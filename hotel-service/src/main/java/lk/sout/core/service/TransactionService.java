package lk.sout.core.service;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface TransactionService {

    boolean save(Transaction transactions);

    /**
     * Create and save a transaction with the given parameters
     *
     * @param amount The transaction amount
     * @param operator The operator ("+" for income, "-" for expense)
     * @param refNo The reference number (e.g., invoice number, expense ID)
     * @param refType The reference type (e.g., "Sales Invoice Payment", "Expense")
     * @param thirdParty The third party involved (e.g., customer name, supplier name)
     * @param typeCategory The category of the transaction type (e.g., "Income", "Expense")
     * @param typeValue The value of the transaction type (e.g., "SalesInvoice", "Other")
     * @param paymentMethod The payment method (optional)
     * @param remark Additional remarks (optional)
     * @param date The transaction date (defaults to current time if null)
     * @return true if the transaction was saved successfully, false otherwise
     */
    boolean createTransaction(double amount, String operator, String refNo, String refType,
                             String thirdParty, String typeCategory, String typeValue,
                             String paymentMethod, String remark, LocalDateTime date);

    Iterable<Transaction> findAll(org.springframework.data.domain.Pageable pageable);

    List<Transaction> findByType(MetaData type);

    List<Transaction> findByThirdParty(String thirdParty);

    List<Transaction> findByRangeFilterAndOperator(String rangeId, String operator);

    List<Transaction> findByDateRangeAndOperator(LocalDate startDate, LocalDate endDate, String operator);

    List<Transaction> findAllByDateBetween(LocalDate startDate, LocalDate endDate);

    Transaction findByRefNoAndRefType(String refNo, String refType);

    List<Transaction> findByRefNo(String refNo);

}
