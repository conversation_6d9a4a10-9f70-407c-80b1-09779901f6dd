
package lk.sout.core.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;
import lk.sout.core.repository.TransactionRepository;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TransactionServiceImpl implements TransactionService {

    final static Logger LOGGER = LoggerFactory.getLogger(TransactionServiceImpl.class);

    @Autowired
    TransactionRepository transactionRepository;

    @Autowired
    MetaDataService metaDataService;

    @Override
    public boolean save(Transaction transaction) {
        try {
            transactionRepository.save(transaction);
            return true;
        } catch (Exception e) {
            LOGGER.error("Error saving transaction: " + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean createTransaction(double amount, String operator, String refNo, String refType,
                                    String thirdParty, String typeCategory, String typeValue,
                                    String paymentMethod, String remark, LocalDateTime date) {
        try {
            // Find or create the transaction type metadata
            MetaData transactionType = metaDataService.searchMetaData(typeValue, typeCategory);
            if (transactionType == null) {
                LOGGER.error("Transaction type not found: " + typeValue + " in category: " + typeCategory);
                return false;
            }

            // Create the transaction
            Transaction transaction = new Transaction();
            transaction.setAmount(amount);
            transaction.setOperator(operator);
            transaction.setRefNo(refNo);
            transaction.setRefType(refType);
            transaction.setThirdParty(thirdParty);
            transaction.setType(transactionType);

            // Set optional fields if provided
            if (paymentMethod != null && !paymentMethod.isEmpty()) {
                transaction.setPaymentMethod(paymentMethod);
            }

            if (remark != null && !remark.isEmpty()) {
                transaction.setRemark(remark);
            }

            // Set date (use current time if not provided)
            transaction.setDate(date != null ? date : LocalDateTime.now());

            // Save the transaction
            LOGGER.info("Creating transaction: amount=" + amount + ", operator=" + operator + ", refType=" + refType + ", thirdParty=" + thirdParty);
            return save(transaction);
        } catch (Exception e) {
            LOGGER.error("Error creating transaction: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Iterable<Transaction> findAll(Pageable pageable) {
        try {
            return transactionRepository.findAll(pageable);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByType(MetaData type) {
        try {
            return transactionRepository.findAllByType(type);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByThirdParty(String thirdParty) {
        try {
            return transactionRepository.findAllByThirdParty(thirdParty);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findAllByDateBetween(LocalDate startDate, LocalDate endDate) {
        try {
            return transactionRepository.findAllByDateBetween(startDate.atStartOfDay(),
                    endDate.atStartOfDay());
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByDateRangeAndOperator(LocalDate startDate, LocalDate endDate, String operator) {
        try {
            // If operator is 'all', don't filter by operator
            if (operator.equals("all")) {
                LOGGER.info("Finding all transactions between " + startDate + " and " + endDate);
                return transactionRepository.findAllByDateBetween(startDate.atStartOfDay(), endDate.atStartOfDay());
            } else {
                // Otherwise, filter by operator
                if (operator.equals("plus")) {
                    operator = "+";
                } else {
                    operator = "-";
                }
                LOGGER.info("Finding transactions with operator " + operator + " between " + startDate + " and " + endDate);
                List<Transaction> records = transactionRepository.findByOperatorAndDateBetween(operator, startDate.atStartOfDay(),
                        endDate.atStartOfDay());
                return records;
            }
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by date range: " + e.getMessage());
            return null;
        }
    }

    public Transaction findByRefNoAndRefType(String ref, String refType) {
        try {
            return transactionRepository.findByRefNoAndRefType(ref, refType);
        } catch (Exception e) {
            return null;
        }
    }

    public List<Transaction> findByRefNo(String ref) {
        try {
            return transactionRepository.findByRefNoOrderByCreatedDate(ref);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByRangeFilterAndOperator(String rangeId, String operator) {
        try {
            MetaData range = metaDataService.findById(rangeId);
            LocalDate sDate = LocalDate.now();
            LocalDate eDate = LocalDate.now();
            LocalDate today = LocalDate.now();

            // Set date range based on filter
            if (range.getValue().equals("Today")) {
                sDate = today;
                eDate = today.plusDays(1);
            }
            if (range.getValue().equals("This Week")) {
                sDate = today.with((DayOfWeek.MONDAY));
                eDate = today.with((DayOfWeek.SUNDAY));
            }
            if (range.getValue().equals("This Month")) {
                sDate = today.withDayOfMonth(1);
                eDate = today.withDayOfMonth(today.lengthOfMonth());
            }
            if (range.getValue().equals("This Year")) {
                sDate = today.withDayOfYear(1);
                eDate = today.withDayOfYear(today.lengthOfYear());
            }

            // If operator is 'all', don't filter by operator
            if (operator.equals("all")) {
                LOGGER.info("Finding all transactions between " + sDate + " and " + eDate);
                return transactionRepository.findAllByDateBetween(sDate.atStartOfDay(), eDate.atStartOfDay());
            } else {
                // Otherwise, filter by operator
                if (operator.equals("plus")) {
                    operator = "+";
                } else {
                    operator = "-";
                }
                LOGGER.info("Finding transactions with operator " + operator + " between " + sDate + " and " + eDate);
                return transactionRepository.findByOperatorAndDateBetween(operator, sDate.atStartOfDay(), eDate.atStartOfDay());
            }
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by range filter: " + e.getMessage());
            return null;
        }
    }

}
