package lk.sout.core.service;

import lk.sout.core.entity.Response;
import lk.sout.core.entity.Settings;

import java.util.List;

/**
 * Service for managing general application settings
 * These settings apply to all users of the application
 */
public interface GeneralSettingsService {

    /**
     * Save a setting
     * @param settings The setting to save
     * @return Response with the saved setting
     */
    Response save(Settings settings);

    /**
     * Find all settings
     * @return List of all settings
     */
    List<Settings> findAll();

    /**
     * Find settings by category
     * @param category The category to filter by
     * @return List of settings in the category
     */
    List<Settings> findByCategory(String category);

    /**
     * Find a setting by key
     * @param key The key to search for
     * @return The setting if found, null otherwise
     */
    Settings findByKey(String key);

    /**
     * Find a setting by ID
     * @param id The ID to search for
     * @return The setting if found, null otherwise
     */
    Settings findById(String id);

    /**
     * Delete a setting
     * @param id The ID of the setting to delete
     * @return Response with the result
     */
    Response delete(String id);

    /**
     * Save a setting if it doesn't already exist
     * @param key The setting key
     * @param value The setting value
     * @param description The setting description
     * @param category The setting category
     * @return true if saved, false if already exists or error
     */
    boolean saveIfUnavailable(String key, String value, String description, String category);

    /**
     * Initialize default settings
     * This method should be called during application startup
     */
    void initializeDefaultSettings();

    /**
     * Update user settings in localStorage when general settings change
     * @param setting The setting that was changed
     */
    void updateUserSettings(Settings setting);
}
