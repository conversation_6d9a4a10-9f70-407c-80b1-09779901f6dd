package lk.sout.core.service.impl;

import lk.sout.core.entity.Company;
import lk.sout.core.repository.CompanyRepository;
import lk.sout.core.service.CompanyService;
import lk.sout.util.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CompanyServiceImpl implements CompanyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompanyServiceImpl.class);

    @Autowired
    CompanyRepository companyRepository;

    @Autowired
    FileUtils fileUtils;

    @Override
    public boolean save(Company company) {
        try {
            String companyId = "";
            if (companyRepository.count() < 1) {
                company.setId(null);
            } else {
                companyId = companyRepository.findAll().get(0).getId();
                company.setId(companyId);
            }

            companyRepository.save(company);
            LOGGER.info("company info saved");
            return true;

        } catch (Exception ex) {
            LOGGER.error("company saved" + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean saveLogo(String logo) {
        try {
            Company company = companyRepository.findAll().get(0);
            company.setLogo(logo);
            companyRepository.save(company);
            LOGGER.info("company info saved");
            return true;
        } catch (Exception ex) {
            LOGGER.error("company saved" + ex.getMessage());
            return false;
        }

    }

    @Override
    public Company findInfo() {
        Company company = new Company();
        if (companyRepository.findAll().size() > 0) {
            company = companyRepository.findAll().get(0);
        }
        return company;
    }
}
