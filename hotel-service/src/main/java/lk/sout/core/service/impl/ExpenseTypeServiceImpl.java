package lk.sout.core.service.impl;

import lk.sout.core.entity.ExpenseType;
import lk.sout.core.repository.ExpenseTypeRepository;
import lk.sout.core.service.ExpenseTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExpenseTypeServiceImpl implements ExpenseTypeService {

    final static Logger LOGGER = LoggerFactory.getLogger(ExpenseTypeServiceImpl.class);

    @Autowired
    ExpenseTypeRepository expenseTypeRepository;

    @Override
    public boolean save(ExpenseType expenseType) {
        try {
            expenseTypeRepository.save(expenseType);
            return true;
        } catch (Exception ex) {
            LOGGER.error("create expense type failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<ExpenseType> findAll(Integer page, Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("id").descending());
            return expenseTypeRepository.findAll(pageable);
        } catch (Exception e) {
            LOGGER.info("Get All Invoices Failed! - " + e.getMessage());
            return null;
        }
    }

    @Override
    public ExpenseType findById(String id) {
        try {
            return expenseTypeRepository.findById(id).get();
        } catch (Exception e) {
            LOGGER.info("Find expense type by Id failed - " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<ExpenseType> findByExpenseTypeNameLike(String s) {
        try {
            return expenseTypeRepository.findAllByNameLikeIgnoreCase(s);
        } catch (Exception e) {
            LOGGER.info("Get All Invoice Failed! - " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<ExpenseType> findByExpenseCategory(String id) {
        try {
            return expenseTypeRepository.findAllByCategory(id);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Rent failed: " + ex.getMessage());
            return null;
        }
    }
}



