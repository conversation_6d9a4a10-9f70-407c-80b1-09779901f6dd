package lk.sout.core.service;

import lk.sout.core.entity.Module;
import lk.sout.core.entity.Permission;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.User;

import java.util.List;
import java.util.Optional;

public interface UserService {

    boolean save(User user);

    List<User> findAll();

    User getCurrentUser();

    boolean isAdmin();

    boolean isManager();

    String delete(String id);

    User findOne(String username);

    Optional<User> findById(String id);

    User search(String username);

    List<Permission> findAvailablePermissions(String userName);

    List<Permission> findDesktopPermissions(String userName);

    boolean saveDesktopPerms(String username, List<Permission> permission);

    List<Module> getEnabledModules();

    Module findModule(String moduleId);

    Permission findPermission(String permissionId);

    List<Permission> findPermissionsByModule(String moduleId);

    Boolean checkName(String username);

    Response update(User user);

    User searchByUsername(String username);

    Permission findPermissionsByName(String permName);

    User findUser();

    /**
     * Find all users who have the CASHIER role
     * @return List of users with cashier role
     */
    List<User> findUsersWithCashierRole();
}
