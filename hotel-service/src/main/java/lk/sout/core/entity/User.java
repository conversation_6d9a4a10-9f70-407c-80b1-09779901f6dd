package lk.sout.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lk.sout.business.trade.entity.CashDrawer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

@Document
public class User {

    @Id
    private String id;

    @Indexed(unique = true)
    private String username;

    @DBRef
    private List<Permission> permissions;

    @DBRef
    private List<Permission> desktopPermissions;

    //    @JsonIgnore
    private String password;

    private int warehouseCode;

    private String cashDrawerNo;

    private boolean active;

    private String firstName;

    private String lastName;

    private String email;

    private Boolean enabled;

    @JsonIgnore
    private Date lastPasswordResetDate;

    private List<UserRole> userRoles;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getLastPasswordResetDate() {
        return lastPasswordResetDate;
    }

    public void setLastPasswordResetDate(Date lastPasswordResetDate) {
        this.lastPasswordResetDate = lastPasswordResetDate;
    }

    public int getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(int warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public List<UserRole> getUserRoles() {
        return userRoles;
    }

    public void setUserRoles(List<UserRole> userRoles) {
        this.userRoles = userRoles;
    }

    public String getCashDrawerNo() {
		return cashDrawerNo;
	}

    public void setCashDrawerNo(String cashDrawerNo) {
		this.cashDrawerNo = cashDrawerNo;
	}

    public List<Permission> getDesktopPermissions() {
        return desktopPermissions;
    }

    public void setDesktopPermissions(List<Permission> desktoppermissions) {
        this.desktopPermissions = desktoppermissions;
    }

    public List<Permission> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<Permission> permissions) {
        this.permissions = permissions;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

}
