package lk.sout.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

@Document
@Component
public class ExpenseType {

    @Id
    private String id;

    @Indexed(unique = true)
    private String name;

    @DBRef
    private MetaData category;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public MetaData getCategory() {
        return category;
    }

    public void setCategory(MetaData category) {
        this.category = category;
    }

}
