package lk.sout.core.entity;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity to store daily business statistics
 */
@Document
@Component
public class DailyStatistics {

    @Id
    private String id;

    @Indexed(unique = true)
    private LocalDate date;

    // Stock statistics
    private double totalStockValue; // Total value of all stock (selling price)
    private double totalStockCost; // Total cost of all stock
    private int totalStockItems; // Total number of stock items
    private double totalStockQuantity; // Total quantity of all stock items
    private double estimatedProfit; // Estimated profit (totalStockValue - totalStockCost)

    // Sales statistics
    private double dailySales; // Total sales for the day
    private int dailyInvoiceCount; // Number of invoices for the day
    private double dailyProfit; // Profit for the day
    private double averageInvoiceValue; // Average invoice value for the day

    // Pending bills statistics
    private double totalPendingBills; // Total amount of pending bills
    private int pendingBillCount; // Number of pending bills
    private double oldestPendingBillAge; // Age of oldest pending bill in days

    // Cheque statistics
    private double totalPendingCheques; // Total amount of pending cheques
    private int pendingChequeCount; // Number of pending cheques
    private double totalDepositedCheques; // Total amount of deposited cheques
    private int depositedChequeCount; // Number of deposited cheques

    // Transaction statistics
    private double totalIncome; // Total income for the day
    private double totalExpense; // Total expense for the day
    private double netCashFlow; // Net cash flow for the day (totalIncome - totalExpense)

    // Additional statistics as key-value pairs
    private Map<String, Double> additionalStats = new HashMap<>();

    // Audit fields
    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public DailyStatistics() {
        this.date = LocalDate.now();
    }

    public DailyStatistics(LocalDate date) {
        this.date = date;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public double getTotalStockValue() {
        return totalStockValue;
    }

    public void setTotalStockValue(double totalStockValue) {
        this.totalStockValue = totalStockValue;
    }

    public double getTotalStockCost() {
        return totalStockCost;
    }

    public void setTotalStockCost(double totalStockCost) {
        this.totalStockCost = totalStockCost;
    }

    public int getTotalStockItems() {
        return totalStockItems;
    }

    public void setTotalStockItems(int totalStockItems) {
        this.totalStockItems = totalStockItems;
    }

    public double getTotalStockQuantity() {
        return totalStockQuantity;
    }

    public void setTotalStockQuantity(double totalStockQuantity) {
        this.totalStockQuantity = totalStockQuantity;
    }

    public double getEstimatedProfit() {
        return estimatedProfit;
    }

    public void setEstimatedProfit(double estimatedProfit) {
        this.estimatedProfit = estimatedProfit;
    }

    public double getDailySales() {
        return dailySales;
    }

    public void setDailySales(double dailySales) {
        this.dailySales = dailySales;
    }

    public int getDailyInvoiceCount() {
        return dailyInvoiceCount;
    }

    public void setDailyInvoiceCount(int dailyInvoiceCount) {
        this.dailyInvoiceCount = dailyInvoiceCount;
    }

    public double getDailyProfit() {
        return dailyProfit;
    }

    public void setDailyProfit(double dailyProfit) {
        this.dailyProfit = dailyProfit;
    }

    public double getAverageInvoiceValue() {
        return averageInvoiceValue;
    }

    public void setAverageInvoiceValue(double averageInvoiceValue) {
        this.averageInvoiceValue = averageInvoiceValue;
    }

    public double getTotalPendingBills() {
        return totalPendingBills;
    }

    public void setTotalPendingBills(double totalPendingBills) {
        this.totalPendingBills = totalPendingBills;
    }

    public int getPendingBillCount() {
        return pendingBillCount;
    }

    public void setPendingBillCount(int pendingBillCount) {
        this.pendingBillCount = pendingBillCount;
    }

    public double getOldestPendingBillAge() {
        return oldestPendingBillAge;
    }

    public void setOldestPendingBillAge(double oldestPendingBillAge) {
        this.oldestPendingBillAge = oldestPendingBillAge;
    }

    public double getTotalPendingCheques() {
        return totalPendingCheques;
    }

    public void setTotalPendingCheques(double totalPendingCheques) {
        this.totalPendingCheques = totalPendingCheques;
    }

    public int getPendingChequeCount() {
        return pendingChequeCount;
    }

    public void setPendingChequeCount(int pendingChequeCount) {
        this.pendingChequeCount = pendingChequeCount;
    }

    public double getTotalDepositedCheques() {
        return totalDepositedCheques;
    }

    public void setTotalDepositedCheques(double totalDepositedCheques) {
        this.totalDepositedCheques = totalDepositedCheques;
    }

    public int getDepositedChequeCount() {
        return depositedChequeCount;
    }

    public void setDepositedChequeCount(int depositedChequeCount) {
        this.depositedChequeCount = depositedChequeCount;
    }

    public double getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(double totalIncome) {
        this.totalIncome = totalIncome;
    }

    public double getTotalExpense() {
        return totalExpense;
    }

    public void setTotalExpense(double totalExpense) {
        this.totalExpense = totalExpense;
    }

    public double getNetCashFlow() {
        return netCashFlow;
    }

    public void setNetCashFlow(double netCashFlow) {
        this.netCashFlow = netCashFlow;
    }

    public Map<String, Double> getAdditionalStats() {
        return additionalStats;
    }

    public void setAdditionalStats(Map<String, Double> additionalStats) {
        this.additionalStats = additionalStats;
    }

    public void addAdditionalStat(String key, Double value) {
        this.additionalStats.put(key, value);
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
}
