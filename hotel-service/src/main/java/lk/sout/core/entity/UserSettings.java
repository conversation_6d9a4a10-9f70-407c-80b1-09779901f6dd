package lk.sout.core.entity;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity to store user-specific application settings
 */
@Document
@Component
public class UserSettings {

    @Id
    private String id;

    @DBRef
    private User user;

    // Store settings as key-value pairs
    private Map<String, SettingValue> settings = new HashMap<>();

    // Audit fields
    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public UserSettings() {
    }

    public UserSettings(User user) {
        this.user = user;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Map<String, SettingValue> getSettings() {
        return settings;
    }

    public void setSettings(Map<String, SettingValue> settings) {
        this.settings = settings;
    }

    /**
     * Add or update a setting
     * @param key Setting key
     * @param value Setting value
     * @param enabled Whether the setting is enabled
     */
    public void addSetting(String key, String value, boolean enabled) {
        settings.put(key, new SettingValue(value, enabled));
    }

    /**
     * Get a setting value
     * @param key Setting key
     * @return Setting value or null if not found
     */
    public String getSettingValue(String key) {
        SettingValue settingValue = settings.get(key);
        return (settingValue != null && settingValue.isEnabled()) ? settingValue.getValue() : null;
    }

    /**
     * Check if a setting is enabled
     * @param key Setting key
     * @return true if setting exists and is enabled, false otherwise
     */
    public boolean isSettingEnabled(String key) {
        SettingValue settingValue = settings.get(key);
        return settingValue != null && settingValue.isEnabled();
    }

    /**
     * Enable or disable a setting
     * @param key Setting key
     * @param enabled Whether the setting should be enabled
     * @return true if setting exists and was updated, false otherwise
     */
    public boolean setSettingEnabled(String key, boolean enabled) {
        SettingValue settingValue = settings.get(key);
        if (settingValue != null) {
            settingValue.setEnabled(enabled);
            return true;
        }
        return false;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    /**
     * Inner class to store setting value and enabled status
     */
    public static class SettingValue {
        private String value;
        private boolean enabled;

        public SettingValue() {
        }

        public SettingValue(String value, boolean enabled) {
            this.value = value;
            this.enabled = enabled;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }
}
