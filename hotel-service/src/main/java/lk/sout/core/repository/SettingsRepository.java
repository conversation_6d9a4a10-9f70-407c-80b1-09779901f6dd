package lk.sout.core.repository;

import lk.sout.core.entity.Settings;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SettingsRepository extends MongoRepository<Settings, String> {
    
    /**
     * Find a setting by its key
     * @param key The key to search for
     * @return Optional containing the setting if found
     */
    @Query("{ 'key' : ?0 }")
    Optional<Settings> findByKey(String key);
    
    /**
     * Find all settings in a category
     * @param category The category to filter by
     * @return List of settings in the category
     */
    @Query("{ 'category' : ?0 }")
    List<Settings> findByCategory(String category);
    
    /**
     * Find a setting by key and category
     * @param key The key to search for
     * @param category The category to filter by
     * @return Optional containing the setting if found
     */
    @Query("{ 'key' : ?0, 'category' : ?1 }")
    Optional<Settings> findByKeyAndCategory(String key, String category);
}
