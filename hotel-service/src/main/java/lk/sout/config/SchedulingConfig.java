package lk.sout.config;

import lk.sout.core.service.StatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Configuration for scheduled tasks
 */
@Configuration
@EnableScheduling
public class SchedulingConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulingConfig.class);

    @Autowired
    private StatisticsService statisticsService;

    /**
     * Generate daily statistics at midnight (00:00)
     * This task runs every day at midnight to generate statistics for the previous day
     */
    @Scheduled(cron = "0 0 0 * * ?") // Run at midnight every day
    public void generateDailyStatistics() {
        LOGGER.info("Starting scheduled daily statistics generation at {}", LocalDateTime.now());
        
        try {
            // Generate statistics for yesterday
            LocalDate yesterday = LocalDate.now().minusDays(1);
            statisticsService.generateStatisticsForDate(yesterday);
            LOGGER.info("Completed scheduled daily statistics generation for {}", yesterday);
        } catch (Exception e) {
            LOGGER.error("Error in scheduled daily statistics generation: {}", e.getMessage(), e);
        }
    }

    /**
     * Generate statistics for the current day every hour
     * This task runs every hour to update statistics for the current day
     */
    @Scheduled(cron = "0 0 * * * ?") // Run at the top of every hour
    public void updateCurrentDayStatistics() {
        // Skip midnight run as it's handled by generateDailyStatistics
        if (LocalDateTime.now().getHour() == 0) {
            return;
        }
        
        LOGGER.info("Starting hourly statistics update at {}", LocalDateTime.now());
        
        try {
            // Generate statistics for today
            statisticsService.generateDailyStatistics();
            LOGGER.info("Completed hourly statistics update");
        } catch (Exception e) {
            LOGGER.error("Error in hourly statistics update: {}", e.getMessage(), e);
        }
    }
}
