package lk.sout.util;

import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Component
public class FileUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtils.class);

    public boolean createDirectory(String path) {
        Path path1 = Paths.get(path);
        try {
            if (!Files.exists(path1)) {
                Files.createDirectories(path1);
                LOGGER.info(":: Directory Created :: createDirectory ::");
            }
            return true;
        } catch (IOException ex) {
            ex.printStackTrace();
            LOGGER.error(":: Exception Occurred ::: GeneralServicesImpl - createDirectory ::: " + ex.getMessage());
        }
        return false;
    }

    public boolean fileUploadService(MultipartFile multipartFile, String uploadDirectory) {
        String fileExtension = FilenameUtils.getExtension(multipartFile.getOriginalFilename());
        try {
            FileCopyUtils.copy(multipartFile.getBytes(), new File(uploadDirectory));
            LOGGER.info(":: File Upload Success :: fileUploadService ::");
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            LOGGER.error("Exception in fileUploadService " + e.getMessage());
        }
        return false;
    }

  /*  public ResponseEntity<byte[]> fileDownloadService(String downloadDirectory) {
        try {
            File file = new File(downloadDirectory);
            byte[] bytes = Files.readAllBytes(file.toPath());
            return ResponseEntity.ok().contentType(MediaType.valueOf(FileTypeMap.getDefaultFileTypeMap().getContentType(file))).body(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            e.printStackTrace();
            LOGGER.error("Exception in fileDownloadService " + e.getMessage());
            return null;
        }
    }*/

}
