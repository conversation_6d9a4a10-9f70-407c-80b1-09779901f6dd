package lk.sout.business.restaurent.repository.template;

import lk.sout.business.restaurent.entity.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 3/9/2020
 */

@Repository
public class OrderRepositoryTemplate {

    @Autowired
    MongoTemplate mongoTemplate;

    public List<Order> findAll() {
        Query query = new Query();
        query.fields().include("table").include("orderNo");
        return mongoTemplate.find(query, Order.class);
    }

}