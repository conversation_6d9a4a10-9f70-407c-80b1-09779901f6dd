package lk.sout.business.restaurent.repository;

import lk.sout.business.restaurent.entity.Table;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TableRepository extends MongoRepository<Table, String> {

    List<Table> findAllByTableNoLikeIgnoreCase(String no);

    List<Table> findAll();
}