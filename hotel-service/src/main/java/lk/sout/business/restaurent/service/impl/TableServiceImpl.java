package lk.sout.business.restaurent.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.business.restaurent.entity.Table;
import lk.sout.business.restaurent.repository.TableRepository;
import lk.sout.business.restaurent.service.TableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TableServiceImpl implements TableService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TableServiceImpl.class);

    @Autowired
    TableRepository tableRepository;

    @Autowired
    Response response;

    @Override
    public Response save(Table table) {
        try {
            tableRepository.save(table);
            response.setCode(200);
            response.setMessage("Table Created Successfully");
        } catch (Exception ex) {
            LOGGER.error("Creating Showroom Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Table Failed");
            response.setData(ex.getMessage());
        }
        return response;
    }

    @Override
    public List<Table> findByTableNoLike(String no) {
        try {
            return tableRepository.findAllByTableNoLikeIgnoreCase(no);
        } catch (Exception ex) {
            LOGGER.error("Creating Showroom Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Table> findAll(Pageable pageable) {
        try {
            return tableRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All ShowRooms Pageable Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Table> findAllTable() {
        try {
            return tableRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All ShowRooms Pageable Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Table findByTableNo(String tableNo) {
        return null;
    }


}