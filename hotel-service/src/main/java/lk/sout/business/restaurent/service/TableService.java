package lk.sout.business.restaurent.service;

import lk.sout.core.entity.Response;
import lk.sout.business.restaurent.entity.Table;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface TableService {

    Response save(Table table);

    List<Table> findByTableNoLike (String no);

    Iterable<Table> findAll(Pageable pageable);

    List<Table> findAllTable();

    Table findByTableNo(String tableNo);
}