package lk.sout.business.reservation.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.business.reservation.entity.BoardType;
import lk.sout.business.reservation.repository.BoardTypeRepository;
import lk.sout.business.reservation.service.BoardTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BoardTypeServiceImpl implements BoardTypeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BoardTypeServiceImpl.class);

    @Autowired
    Response response;

    @Autowired
    BoardTypeRepository boardTypeRepository;

    @Override
    public Response save(BoardType boardType) {
        try {
            boardTypeRepository.save(boardType);
            response.setCode(200);
            response.setMessage("Board Type Successfully Saved");
            return response;
        }catch (Exception e){
            LOGGER.error("Board Type Save Failed");
            response.setCode(501);
            response.setMessage("Board Type Save Failed");
            return response;
        }
    }

    @Override
    public List<BoardType> findByNameLike(String boardType) {
        try {
            List<BoardType> boardTypeList = boardTypeRepository.findByNameLikeIgnoreCase(boardType);
            return boardTypeList;
        }catch (Exception e){
            LOGGER.error("Board Type Searching Failed");
            return null;
        }
    }

    @Override
    public Page findAllPageable(Pageable pageable) {
        try {
            return boardTypeRepository.findAll(pageable);
        }catch (Exception e){
            LOGGER.error("Board Type Retrieving Failed");
            return null;
        }
    }

    @Override
    public BoardType findById(String id) {
        try {
            return boardTypeRepository.findBoardTypeById(id);
        }catch (Exception e){
            LOGGER.error("Board Type Retrieving Failed");
            return null;
        }
    }

    @Override
    public Iterable findAll() {
        try {
            return boardTypeRepository.findAll();
        }catch (Exception e){
            LOGGER.error("Board Type Retrieving Failed");
            return null;
        }
    }
}