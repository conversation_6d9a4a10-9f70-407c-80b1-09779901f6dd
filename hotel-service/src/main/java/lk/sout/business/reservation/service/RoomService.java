package lk.sout.business.reservation.service;

import lk.sout.core.entity.Response;
import lk.sout.business.reservation.entity.Room;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface RoomService {

    Response save(Room room);

    Iterable<Room> findAll(Pageable pageable);

    List<Room> findByName (String name);

    List<Room> searchByRoomNoLike(String roomNo);

    boolean checkRoomNo(String roomNo);
}