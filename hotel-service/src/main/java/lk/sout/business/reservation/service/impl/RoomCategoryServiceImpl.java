package lk.sout.business.reservation.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.business.reservation.entity.RoomCategory;
import lk.sout.business.reservation.repository.RoomCategoryRepository;
import lk.sout.business.reservation.service.RoomCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RoomCategoryServiceImpl implements RoomCategoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RoomServiceImpl.class);

    @Autowired
    RoomCategoryRepository roomCategoryRepository;

    @Autowired
    Response response;

    @Override
    public Response save(RoomCategory roomCategory) {
        try {
            roomCategoryRepository.save(roomCategory);
            response.setCode(200);
            response.setMessage("Room Category Created Successfully");
        }catch (Exception e) {
            LOGGER.error("Created Room Category Failed", e.getMessage());
            response.setCode(501);
            response.setMessage("Created Room Category Failed");
            response.setData(e.getMessage());
        }
        return response;
    }

    @Override
    public Iterable<RoomCategory> findAll(Pageable pageable) {
        try {
            return roomCategoryRepository.findAll(pageable);
        } catch (Exception e) {
            LOGGER.error("Find All Room Category Failed", e.getMessage());
            return null;
        }
    }

    @Override
    public List<RoomCategory> findByName(String name) {
        try {
            List<RoomCategory> r = roomCategoryRepository.findByNameLikeIgnoreCase(name);
            return r;
        } catch (Exception e) {
            LOGGER.error("Find By Room Category Name Failed");
            return null;
        }
    }
}