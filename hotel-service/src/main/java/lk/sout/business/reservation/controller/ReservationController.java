package lk.sout.business.reservation.controller;

import lk.sout.business.reservation.entity.Reservation;
import lk.sout.business.reservation.service.ReservationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/reservation")
public class ReservationController {
    @Autowired
    ReservationService reservationService;

    @RequestMapping(value = "/saveReservation", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> saveReservation (@RequestBody Reservation reservation) {
        try {
            return ResponseEntity.ok(reservationService.saveReservation(reservation));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/saveInvoice", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> saveInvoice (@RequestBody Reservation reservationInvoice) {
        try {
            return ResponseEntity.ok(reservationService.saveInvoice(reservationInvoice));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(reservationService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getAllCount", method = RequestMethod.GET)
    private ResponseEntity<?> getAllCount() {
        try {
            return ResponseEntity.ok(reservationService.getAllCount());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/checkFromDate", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> checkFromDateAvailability(@RequestParam("roomId") String roomId, @RequestParam("from") String from) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(reservationService.checkFromDate(roomId, LocalDate.parse(from, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllBookedRoom", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllBookedRoom(@RequestParam("today") String today) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(reservationService.findAllBookedRoom(LocalDate.parse(today, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/find_byId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(reservationService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOneById", method = RequestMethod.GET)
    private ResponseEntity<?> findOneById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(reservationService.findOne(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByStatus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByStatus(@RequestParam("booked") String booked) {
        try {
            return ResponseEntity.ok(reservationService.findAllByStatusLikeIgnoreCase(booked));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findReservationByRoomNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findReservationByRoomNo(@RequestParam("id") String id, @RequestParam("status") String status) {
        try {
            return ResponseEntity.ok(reservationService.findReservationByRoomNo(id, status));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findReservationById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findReservationById(@RequestParam("reservationId") String reservationId, @RequestParam("roomId") String roomId) {
        try {
            return ResponseEntity.ok(reservationService.findByIdAndRoomId(reservationId,roomId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPendingReservations", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPendingReservations(){
        try {
            return ResponseEntity.ok(reservationService.findAllPendingReservations());
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findReservationInvoiceByInvNo", method = RequestMethod.GET)
    private ResponseEntity<?> findReservationInvoiceByInvNo(@RequestParam String invoiceNo){
        try {
            return ResponseEntity.ok(reservationService.findReservationInvoiceByInvNo(invoiceNo));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}