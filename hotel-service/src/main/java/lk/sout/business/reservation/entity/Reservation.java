package lk.sout.business.reservation.entity;

import lk.sout.config.CascadeSave;
import lk.sout.core.entity.MetaData;
import lk.sout.business.trade.entity.Customer;
import lk.sout.business.trade.entity.SalesInvoiceRecord;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Document
@Component
public class Reservation {
    @Id
    private String id;

    private String invoiceNo;

    private String reservationCode;

    @DBRef
    private Room room;

    @DBRef
    private Customer customer;

    @DBRef
    @CascadeSave
    private List<SalesInvoiceRecord> salesInvoiceRecords;

    private LocalDate from;

    private LocalDate to;

    private MetaData boardType;

    private boolean roomBook;

    private boolean todayBook;

    private boolean active;

    public MetaData reservationStatus;

    public MetaData invoiceStatus;

    public Double roomCharge;

    private Double subTotal;

    private Double totalAmount;

    private Double price;

    private Double totalDiscount;

    private Double payment;

    private Double cashBalance;

    private Double serviceCharge;

    private String counterNo;

    private LocalDateTime dueDate;

    private LocalDateTime reservationDate;

    private LocalDateTime invoiceDate;

    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getReservationCode() {
        return reservationCode;
    }

    public void setReservationCode(String reservationCode) {
        this.reservationCode = reservationCode;
    }

    public Room getRoom() {
        return room;
    }

    public void setRoom(Room room) {
        this.room = room;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public List<SalesInvoiceRecord> getSalesInvoiceRecords() {
        return salesInvoiceRecords;
    }

    public void setSalesInvoiceRecords(List<SalesInvoiceRecord> salesInvoiceRecords) {
        this.salesInvoiceRecords = salesInvoiceRecords;
    }

    public LocalDate getFrom() {
        return from;
    }

    public void setFrom(LocalDate from) {
        this.from = from;
    }

    public LocalDate getTo() {
        return to;
    }

    public void setTo(LocalDate to) {
        this.to = to;
    }

    public MetaData getBoardType() {
        return boardType;
    }

    public void setBoardType(MetaData boardType) {
        this.boardType = boardType;
    }

    public boolean isRoomBook() {
        return roomBook;
    }

    public void setRoomBook(boolean roomBook) {
        this.roomBook = roomBook;
    }

    public boolean isTodayBook() {
        return todayBook;
    }

    public void setTodayBook(boolean todayBook) {
        this.todayBook = todayBook;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public MetaData getReservationStatus() {
        return reservationStatus;
    }

    public void setReservationStatus(MetaData reservationStatus) {
        this.reservationStatus = reservationStatus;
    }

    public MetaData getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(MetaData invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public Double getRoomCharge() {
        return roomCharge;
    }

    public void setRoomCharge(Double roomCharge) {
        this.roomCharge = roomCharge;
    }

    public Double getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(Double subTotal) {
        this.subTotal = subTotal;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(Double totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public Double getPayment() {
        return payment;
    }

    public void setPayment(Double payment) {
        this.payment = payment;
    }

    public Double getCashBalance() {
        return cashBalance;
    }

    public void setCashBalance(Double cashBalance) {
        this.cashBalance = cashBalance;
    }

    public Double getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(Double serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public String getCounterNo() {
        return counterNo;
    }

    public void setCounterNo(String counterNo) {
        this.counterNo = counterNo;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getReservationDate() {
        return reservationDate;
    }

    public void setReservationDate(LocalDateTime reservationDate) {
        this.reservationDate = reservationDate;
    }

    public LocalDateTime getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDateTime invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
}