package lk.sout.business.reservation.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.business.reservation.entity.Room;
import lk.sout.business.reservation.repository.RoomRepository;
import lk.sout.business.reservation.service.RoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Pageable;
import java.util.List;

@Service
public class RoomServiceImpl implements RoomService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RoomServiceImpl.class);

    @Autowired
    RoomRepository roomRepository;

    @Autowired
    Response response;

    @Override
    public Response save(Room room) {
        try {
            roomRepository.save(room);
            response.setCode(200);
            response.setMessage("Room Created Successfully");
        } catch (Exception ex) {
            LOGGER.error("Creating Room Failed", ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Room Failed");
            response.setData(ex.getMessage());
        }
        return response;
    }

    @Override
    public Iterable<Room> findAll(Pageable pageable) {
        try {
           Iterable<Room> room = roomRepository.findAll(pageable);
           return  room;
        } catch (Exception ex) {
            LOGGER.error("Find All Room Failed", ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Room> findByName( String name) {
        try {
            List<Room> r = roomRepository.findByRoomNameLikeIgnoreCase(name);
            return r;
        } catch (Exception ex) {
            LOGGER.error("Find By Room Name Failed ");
            return null;
        }
    }

    @Override
    public List<Room> searchByRoomNoLike(String roomNo) {
        try {
            return roomRepository.findAllByRoomNoLikeIgnoreCase(roomNo);
        } catch (Exception ex) {
            LOGGER.error("Creating Room Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean checkRoomNo(String roomNo) {
        try {
            if (null != roomRepository.findByRoomNo(roomNo)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check room by room no Failed: " + ex.getMessage());
            return false;
        }
    }

}