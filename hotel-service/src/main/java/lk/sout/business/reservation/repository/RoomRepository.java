package lk.sout.business.reservation.repository;

import lk.sout.business.reservation.entity.Room;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoomRepository extends MongoRepository<Room, String> {

    List<Room> findByRoomNameLikeIgnoreCase(String name);

    List<Room> findAllByRoomNoLikeIgnoreCase(String roomNo);

    Room findByRoomNo(String roomNo);
}