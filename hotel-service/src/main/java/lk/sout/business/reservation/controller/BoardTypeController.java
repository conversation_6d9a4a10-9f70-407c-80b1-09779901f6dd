package lk.sout.business.reservation.controller;

import lk.sout.business.reservation.entity.BoardType;
import lk.sout.business.reservation.service.BoardTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("boardType")
public class BoardTypeController {

    @Autowired
    BoardTypeService boardTypeService;

    @RequestMapping(value = "save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody BoardType boardType){
        try {
            return ResponseEntity.ok(boardTypeService.save(boardType));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "findAllPageable", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize){
        try {
            return ResponseEntity.ok(
                    boardTypeService.findAllPageable(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(){
        try {
            return ResponseEntity.ok(boardTypeService.findAll());
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "findByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findByNameLike(@RequestParam String boardType){
        try {
            return ResponseEntity.ok(boardTypeService.findByNameLike(boardType));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "findById", method = RequestMethod.GET)
    private ResponseEntity<?> findById(@RequestParam String id){
        try {
            return ResponseEntity.ok(boardTypeService.findById(id));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}