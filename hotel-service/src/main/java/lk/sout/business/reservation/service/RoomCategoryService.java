package lk.sout.business.reservation.service;

import lk.sout.core.entity.Response;
import lk.sout.business.reservation.entity.RoomCategory;
import java.util.List;
import org.springframework.data.domain.Pageable;

public interface RoomCategoryService {

    Response save(RoomCategory roomCategory);

    Iterable<RoomCategory> findAll(Pageable pageable);

    List<RoomCategory> findByName(String name);
}