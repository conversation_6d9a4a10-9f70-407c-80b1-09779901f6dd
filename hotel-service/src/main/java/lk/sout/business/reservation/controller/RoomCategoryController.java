package lk.sout.business.reservation.controller;

import lk.sout.business.reservation.entity.RoomCategory;
import lk.sout.business.reservation.service.RoomCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.MediaType;


@RestController
@RequestMapping("/roomCategory")
public class RoomCategoryController {
    @Autowired
    RoomCategoryService roomCategoryService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save (@RequestBody RoomCategory roomCategory) {
        try {
            return ResponseEntity.ok(roomCategoryService.save(roomCategory));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll (@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(roomCategoryService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByName", method = RequestMethod.GET)
    private ResponseEntity<?> findByName (@RequestParam("any") String name) {
        try {
            return ResponseEntity.ok(roomCategoryService.findByName(name));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
 }