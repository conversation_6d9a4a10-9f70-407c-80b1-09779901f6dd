package lk.sout.business.reservation.service;

import lk.sout.core.entity.Response;
import lk.sout.business.reservation.entity.Reservation;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;


public interface ReservationService {

    Response saveReservation(Reservation reservation);

    Response saveInvoice(Reservation reservationInvoice);

    List<Reservation> findAllPendingReservations();

    Iterable<Reservation> findAll(Pageable pageable);

    Integer getAllCount();

    boolean checkFromDate(String roomId, LocalDate from);

    List<Reservation> findAllBookedRoom(LocalDate today);

    Reservation findById(String id);

    Reservation findOne(String id);

    List<Reservation> findAllByStatusLikeIgnoreCase(String booked);

    List<Reservation> findReservationByRoomNo(String id, String status);

    Reservation findByIdAndRoomId(String reservationId, String roomId);

    Reservation findReservationInvoiceByInvNo(String invoiceNo);
}