package lk.sout.business.reservation.repository;

import lk.sout.business.reservation.entity.RoomCategory;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoomCategoryRepository extends MongoRepository<RoomCategory, String> {
    List<RoomCategory> findByNameLikeIgnoreCase(String name);
}