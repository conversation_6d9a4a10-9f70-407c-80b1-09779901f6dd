package lk.sout.business.reservation.repository;

import lk.sout.business.reservation.entity.Reservation;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDate;
import java.util.List;

public interface ReservationRepository extends MongoRepository<Reservation, String> {

    Reservation findByRoomAndFrom(String roomId, LocalDate from);

    Reservation findAllById(String id);

    Reservation findByIdAndReservationCode(String id, String code);

    List<Reservation> findAllByFrom(LocalDate from);

    Reservation findByRoomId(String roomId);

    List<Reservation> findAllByReservationStatusLikeIgnoreCase(String booked);

    List<Reservation> findAllByRoom_IdAndReservationStatus(String id, String status);

    Reservation findByIdAndRoomId(String id, String roomId);

    List<Reservation> findAllByReservationStatusId(String id);

    Reservation findByInvoiceNo(String invoiceNo);
}