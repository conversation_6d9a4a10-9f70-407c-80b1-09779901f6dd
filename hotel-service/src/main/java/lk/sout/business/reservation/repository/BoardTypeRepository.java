package lk.sout.business.reservation.repository;

import lk.sout.business.reservation.entity.BoardType;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BoardTypeRepository extends MongoRepository<BoardType, String> {
    List<BoardType> findByNameLikeIgnoreCase(String boardType);

    BoardType findBoardTypeById(String id);
}