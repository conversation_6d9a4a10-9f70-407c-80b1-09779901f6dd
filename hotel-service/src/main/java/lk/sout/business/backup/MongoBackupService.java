package lk.sout.business.backup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import com.mongodb.client.MongoClient;
import org.bson.Document;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for MongoDB backup operations
 */
@Service
public class MongoBackupService {

    private static final Logger logger = LoggerFactory.getLogger(MongoBackupService.class);

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    @Qualifier("defaultMongoClient")
    private MongoClient mongoClient;

    @Value("${spring.data.mongodb.host:localhost}")
    private String mongoHost;

    @Value("${spring.data.mongodb.port:27017}")
    private int mongoPort;

    @Value("${spring.data.mongodb.username:#{null}}")
    private String mongoUsername;

    @Value("${spring.data.mongodb.password:#{null}}")
    private String mongoPassword;

    @Value("${spring.data.mongodb.authDatabase:admin}")
    private String authDatabase;

    @Value("${backup.temp.directory:./temp-backups}")
    private String tempBackupDirectory;

    @Value("${backup.database.pattern:generalWeb*}")
    private String databasePattern;

    /**
     * Create backup of all tenant databases
     */
    public File createBackup() throws IOException, InterruptedException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        String backupFileName = "backup-" + timestamp + ".tar.gz";
        
        // Create temp directory
        Path tempDir = Paths.get(tempBackupDirectory);
        Files.createDirectories(tempDir);
        
        Path backupDir = tempDir.resolve("backup-" + timestamp);
        Files.createDirectories(backupDir);

        logger.info("🚀 Starting MongoDB backup at {}", timestamp);

        // Auto-discover all tenant databases
        List<String> databases = discoverDatabases();
        logger.info("📋 Found {} databases to backup: {}", databases.size(), databases);

        for (String database : databases) {
            backupDatabase(database, backupDir);
        }

        // Create compressed archive
        File archiveFile = createArchive(backupDir, tempDir.resolve(backupFileName));
        
        // Cleanup temp backup directory
        deleteDirectory(backupDir.toFile());
        
        logger.info("✅ Backup completed: {} (Size: {} MB)", 
                   backupFileName, archiveFile.length() / 1024 / 1024);
        
        return archiveFile;
    }

    /**
     * Backup individual database
     */
    private void backupDatabase(String databaseName, Path backupDir) throws IOException, InterruptedException {
        logger.info("📦 Backing up database: {}", databaseName);

        List<String> command = new ArrayList<>();
        command.add("mongodump");
        command.add("--host");
        command.add(mongoHost + ":" + mongoPort);
        command.add("--db");
        command.add(databaseName);
        command.add("--out");
        command.add(backupDir.toString());

        // Add authentication if configured
        if (mongoUsername != null && !mongoUsername.isEmpty()) {
            command.add("--username");
            command.add(mongoUsername);
            command.add("--password");
            command.add(mongoPassword);
            command.add("--authenticationDatabase");
            command.add(authDatabase);
        }

        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        // Read output
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                logger.debug("mongodump: {}", line);
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("mongodump failed for database " + databaseName + " with exit code: " + exitCode);
        }

        logger.info("✅ Database backup completed: {}", databaseName);
    }

    /**
     * Create compressed archive
     */
    private File createArchive(Path sourceDir, Path archivePath) throws IOException, InterruptedException {
        logger.info("🗜️ Creating compressed archive...");

        List<String> command = new ArrayList<>();
        command.add("tar");
        command.add("-czf");
        command.add(archivePath.toString());
        command.add("-C");
        command.add(sourceDir.getParent().toString());
        command.add(sourceDir.getFileName().toString());

        ProcessBuilder processBuilder = new ProcessBuilder(command);
        Process process = processBuilder.start();

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("Archive creation failed with exit code: " + exitCode);
        }

        return archivePath.toFile();
    }

    /**
     * Delete directory recursively
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    /**
     * Auto-discover databases using alternative method (no admin permissions needed)
     */
    private List<String> discoverDatabases() {
        logger.info("🔍 Discovering databases with pattern: {}", databasePattern);

        List<String> databases = new ArrayList<>();

        // Try method 1: Direct admin command (requires listDatabases permission)
        try {
            logger.info("🔧 Method 1: Attempting database discovery using admin command...");

            Document listDatabasesResult = mongoClient.getDatabase("admin")
                    .runCommand(new Document("listDatabases", 1));

            @SuppressWarnings("unchecked")
            List<Document> databaseList = (List<Document>) listDatabasesResult.get("databases");

            if (databaseList != null) {
                logger.info("📊 Found {} total databases via admin command", databaseList.size());

                for (Document dbDoc : databaseList) {
                    String dbName = dbDoc.getString("name");
                    if (dbName != null && dbName.startsWith("generalWeb")) {
                        databases.add(dbName);
                        logger.info("✅ Found database: {}", dbName);
                    }
                }
            }

        } catch (Exception e) {
            logger.warn("⚠️ Admin command failed (likely permissions): {}", e.getMessage());

            // Method 2: Try known database connections (no admin permissions needed)
            logger.info("🔄 Method 2: Trying direct database connection approach...");
            databases = discoverDatabasesByConnection();
        }

        // Method 3: Fallback to command line
        if (databases.isEmpty()) {
            try {
                logger.info("🔄 Method 3: Trying command-based discovery...");
                databases = discoverDatabasesUsingCommand();
            } catch (Exception cmdError) {
                logger.error("❌ Command-based discovery also failed: {}", cmdError.getMessage());
            }
        }

        // Final fallback: use known tenant databases
        if (databases.isEmpty()) {
            logger.warn("⚠️ All discovery methods failed, using known tenant databases");
            databases = getKnownTenantDatabases();
        }

        logger.info("📊 Final database list for backup: {}", databases);
        return databases;
    }

    /**
     * Discover databases by trying to connect to known patterns
     */
    private List<String> discoverDatabasesByConnection() {
        List<String> databases = new ArrayList<>();

        // List of common tenant patterns to check
        String[] commonTenants = {
            "generalWeb",
            "generalWebDemo",
            "generalWebNewcitymobile",
            "generalWebWanigarathna",
            "generalWebTest",
            "generalWebDev",
            "generalWebStaging"
        };

        logger.info("🔍 Testing database connections for known patterns...");

        for (String dbName : commonTenants) {
            try {
                // Try to ping the database
                Document pingResult = mongoClient.getDatabase(dbName)
                    .runCommand(new Document("ping", 1));

                if (pingResult.getDouble("ok") == 1.0) {
                    databases.add(dbName);
                    logger.info("✅ Database exists and accessible: {}", dbName);
                }

            } catch (Exception e) {
                logger.debug("⏭️ Database not accessible: {} ({})", dbName, e.getMessage());
            }
        }

        return databases;
    }

    /**
     * Get known tenant databases from configuration or discovery
     */
    private List<String> getKnownTenantDatabases() {
        List<String> databases = new ArrayList<>();

        // Add base database
        databases.add("generalWeb");

        // Add known tenant databases
        databases.add("generalWebDemo");
        databases.add("generalWebNewcitymobile");
        databases.add("generalWebWanigarathna");

        logger.info("📋 Using known tenant databases: {}", databases);
        return databases;
    }

    /**
     * Fallback method using mongo command
     */
    private List<String> discoverDatabasesUsingCommand() throws IOException, InterruptedException {
        List<String> databases = new ArrayList<>();

        List<String> command = new ArrayList<>();
        command.add("mongo");
        command.add("--host");
        command.add(mongoHost + ":" + mongoPort);
        command.add("--quiet");
        command.add("--eval");
        command.add("db.adminCommand('listDatabases').databases.forEach(function(db) { if(db.name.match(/^generalWeb/)) print(db.name); })");

        // Add authentication if configured
        if (mongoUsername != null && !mongoUsername.isEmpty()) {
            command.add("--username");
            command.add(mongoUsername);
            command.add("--password");
            command.add(mongoPassword);
            command.add("--authenticationDatabase");
            command.add(authDatabase);
        }

        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.startsWith("generalWeb") && !line.isEmpty()) {
                    databases.add(line);
                }
            }
        }

        process.waitFor();
        return databases;
    }

    /**
     * Get list of databases to backup
     */
    public String[] getDatabaseList() {
        try {
            List<String> databases = discoverDatabases();
            return databases.toArray(new String[0]);
        } catch (Exception e) {
            logger.error("Failed to discover databases: {}", e.getMessage());
            // Return fallback list
            return new String[]{"generalWeb", "generalWebDemo", "generalWebNewcitymobile", "generalWebWanigarathna"};
        }
    }

    /**
     * Get backup configuration for diagnostic purposes
     */
    public Map<String, Object> getBackupConfiguration() {
        Map<String, Object> config = new HashMap<>();

        config.put("tempBackupDirectory", tempBackupDirectory);
        config.put("databasePattern", databasePattern);
        config.put("mongoHost", mongoHost);
        config.put("mongoPort", mongoPort);
        config.put("mongoUsername", mongoUsername);
        config.put("authDatabase", authDatabase);

        // Check if temp directory exists and is writable
        try {
            Path tempDir = Paths.get(tempBackupDirectory);
            config.put("tempDirectoryExists", Files.exists(tempDir));
            config.put("tempDirectoryIsDirectory", Files.isDirectory(tempDir));
            config.put("tempDirectoryIsWritable", Files.isWritable(tempDir));
            config.put("tempDirectoryAbsolutePath", tempDir.toAbsolutePath().toString());
        } catch (Exception e) {
            config.put("tempDirectoryError", e.getMessage());
        }

        return config;
    }

    /**
     * Debug method to get ALL databases (not filtered)
     */
    public String[] getAllDatabasesForDebug() {
        List<String> allDatabases = new ArrayList<>();

        try {
            logger.error("🐛 DEBUG: Starting database discovery...");
            logger.error("🐛 DEBUG: MongoClient instance: {}", mongoClient.getClass().getName());

            // Test basic connection first
            try {
                Document pingResult = mongoClient.getDatabase("admin").runCommand(new Document("ping", 1));
                logger.error("🐛 DEBUG: Ping successful: {}", pingResult.toJson());
            } catch (Exception pingError) {
                logger.error("🐛 DEBUG: Ping failed: {}", pingError.getMessage());
                allDatabases.add("PING_ERROR: " + pingError.getMessage());
                return allDatabases.toArray(new String[0]);
            }

            // Now try listDatabases
            logger.error("🐛 DEBUG: Executing listDatabases command...");
            Document listDatabasesCommand = new Document("listDatabases", 1);
            logger.error("🐛 DEBUG: Command: {}", listDatabasesCommand.toJson());

            Document listDatabasesResult = mongoClient.getDatabase("admin")
                    .runCommand(listDatabasesCommand);

            logger.error("🐛 DEBUG: Raw response: {}", listDatabasesResult.toJson());

            // Check if command was successful
            Object okValue = listDatabasesResult.get("ok");
            logger.error("🐛 DEBUG: Command OK value: {}", okValue);

            if (okValue == null || !okValue.equals(1.0)) {
                logger.error("🐛 DEBUG: Command failed, OK value is not 1.0");
                allDatabases.add("COMMAND_FAILED: ok=" + okValue);
                return allDatabases.toArray(new String[0]);
            }

            @SuppressWarnings("unchecked")
            List<Document> databaseList = (List<Document>) listDatabasesResult.get("databases");

            if (databaseList == null) {
                logger.error("🐛 DEBUG: Database list is null!");
                allDatabases.add("NULL_DATABASE_LIST");
                return allDatabases.toArray(new String[0]);
            }

            logger.error("🐛 DEBUG: Found {} databases in response", databaseList.size());

            for (Document dbDoc : databaseList) {
                logger.error("🐛 DEBUG: Processing database document: {}", dbDoc.toJson());
                String dbName = dbDoc.getString("name");
                if (dbName != null) {
                    allDatabases.add(dbName);
                    logger.error("🐛 DEBUG: Added database: {}", dbName);
                } else {
                    logger.error("🐛 DEBUG: Database name is null in document: {}", dbDoc.toJson());
                }
            }

            logger.error("🐛 DEBUG: Final database list: {}", allDatabases);

        } catch (Exception e) {
            logger.error("🐛 DEBUG: Exception during database discovery: {}", e.getMessage(), e);
            allDatabases.add("EXCEPTION: " + e.getMessage());
            allDatabases.add("EXCEPTION_CLASS: " + e.getClass().getSimpleName());
        }

        // If we got here with empty list, it means the query worked but returned no databases
        if (allDatabases.isEmpty()) {
            logger.error("🐛 DEBUG: Query succeeded but returned empty database list!");
            allDatabases.add("EMPTY_RESULT");
        }

        return allDatabases.toArray(new String[0]);
    }
}
