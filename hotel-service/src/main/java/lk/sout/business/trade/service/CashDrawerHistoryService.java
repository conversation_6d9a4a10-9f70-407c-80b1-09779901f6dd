package lk.sout.business.trade.service;

import lk.sout.business.trade.entity.CashDrawerHistory;

import java.time.LocalDate;
import java.util.List;

public interface CashDrawerHistoryService {

    boolean save(CashDrawerHistory cashDrawerHistory);

    List<CashDrawerHistory> findAll();

    CashDrawerHistory findByDateAndCounter(LocalDate date, String counter);

    Boolean existsByCounter(String counter);

    boolean dayClose(Double actualAmount, Double withdrawalAmount, Double balance);
}
