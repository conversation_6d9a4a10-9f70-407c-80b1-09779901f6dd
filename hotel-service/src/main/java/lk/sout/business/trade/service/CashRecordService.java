package lk.sout.business.trade.service;

import lk.sout.core.entity.MetaData;
import lk.sout.business.trade.entity.CashRecord;

import java.time.LocalDate;
import java.util.List;

public interface CashRecordService {

    boolean save(CashRecord cashRecord);

    List<CashRecord> findRecordsForToday();

    List<CashRecord> findRecordsBetween(LocalDate sDate, LocalDate eDate);

    List<CashRecord> findByCounterAndDateBetween(String counter, LocalDate sDate, LocalDate eDate);

    List<CashRecord> findByCounterAndTypeAndDateBetween(String counter, String typeId, LocalDate sDate, LocalDate eDate);

    List<CashRecord> findByTypeAndDateBetween(String typeId, LocalDate sDate, LocalDate eDate);

    List<CashRecord> findByPurposeAndCounterAndDateBetween(MetaData purpose, String counter, LocalDate sDate,
                                                           LocalDate eDate);

    Double calculateTotalCashOut(LocalDate date, String counter);

    Double calculateCashOutWithoutDayEnd(LocalDate date, String counter);

    Double calculateOtherCashIn(LocalDate date, String counter);

    Double getDayEndWithdrawalAmount(LocalDate date, String counter);

    boolean dayStart(Double addingAmount);

    boolean addCash(Double addingAmount);

    boolean withdraw(Double withdrawingAmount, String purpose);
}
