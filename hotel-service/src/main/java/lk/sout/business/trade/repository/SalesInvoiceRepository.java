/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.business.trade.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.business.trade.entity.SalesInvoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

//import lk.sout.erp.entity.Customer;

/**
 * <AUTHOR>
 */
@Repository
public interface SalesInvoiceRepository extends MongoRepository<SalesInvoice, String> {

    List<SalesInvoice> findByDate(LocalDate date);

    List<SalesInvoice> findAllByOrderByIdDesc();

    List<SalesInvoice> findAllByCustomerNoOrderByIdDesc(String customerNo);

    List<SalesInvoice> findAllByStatusNot(MetaData metaData);

    List<SalesInvoice> findAllByBalanceGreaterThan(Double balance);

    Page<SalesInvoice> findAllByStatus(MetaData metaData, Pageable pageable);

    List<SalesInvoice> findAllByStatusAndCustomerNo(String status, String customerNo);

    List<SalesInvoice> findByInvoiceNoLikeIgnoreCase(String salesInvoiceId);

    SalesInvoice findByInvoiceNo(String salesInvoiceId);

    SalesInvoice findByReference(String reference);

    List<SalesInvoice> findAllByPaymentMethodValue(String paymentMethod);

    @Query(fields = "{'invoiceNo': 1, 'customerNo': 1, 'customerName': 1, 'date': 1, 'totalAmount': 1, 'balance': 1, 'status': 1}")
    Page<SalesInvoice> findAllByPaymentMethodId(String paymentMethodId, Pageable pageable);

    @Query(fields = "{'invoiceNo': 1, 'customerNo': 1, 'customerName': 1, 'date': 1, " +
            "'totalAmount': 1, 'balance': 1, 'status': 1}")
    Page<SalesInvoice> findAllByStatusId(String paymentStatusId, Pageable pageable);

    List<SalesInvoice> findAllByDateBetween(LocalDateTime sDate, LocalDateTime eDate);

    /**
     * Find sales invoices by date range with pagination and ordering
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @param pageable Pagination information
     * @return Page of sales invoices for the date range
     */
    Page<SalesInvoice> findAllByDateBetweenOrderByDateDesc(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find sales invoices by customer number with pagination and ordering
     * @param customerNo Customer number
     * @param pageable Pagination information
     * @return Page of sales invoices for the customer
     */
    Page<SalesInvoice> findAllByCustomerNoOrderByDateDesc(String customerNo, Pageable pageable);

    /**
     * Find all sales invoices by customer number
     * @param customerNo Customer number
     * @return List of sales invoices for the customer
     */
    List<SalesInvoice> findAllByCustomerNo(String customerNo);

    /**
     * Find all sales invoices by status
     * @param status Payment status
     * @return List of sales invoices for the status
     */
    List<SalesInvoice> findAllByStatus(MetaData status);

    /**
     * Find all sales invoices by cashier username with pagination and ordering
     * @param cashierUserName Cashier username
     * @param pageable Pagination information
     * @return Page of sales invoices for the cashier
     */
    Page<SalesInvoice> findAllByCashierUserNameOrderByDateDesc(String cashierUserName, Pageable pageable);

    /**
     * Find all sales invoices by cashier username
     * @param cashierUserName Cashier username
     * @return List of sales invoices for the cashier
     */
    List<SalesInvoice> findAllByCashierUserName(String cashierUserName);


}
