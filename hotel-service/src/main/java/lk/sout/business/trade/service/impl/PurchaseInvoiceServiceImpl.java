/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package lk.sout.business.trade.service.impl;

import lk.sout.business.inventory.entity.Item;
import lk.sout.business.inventory.service.ItemService;
import lk.sout.business.inventory.service.StockService;
import lk.sout.business.trade.entity.PurchaseInvoice;
import lk.sout.business.trade.entity.Supplier;
import lk.sout.business.trade.repository.PurchaseInvoiceRepository;
import lk.sout.business.trade.service.CashRecordService;
import lk.sout.business.trade.service.SupplierService;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.entity.Transaction;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PurchaseInvoiceServiceImpl implements lk.sout.business.trade.service.PurchaseInvoiceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseInvoiceServiceImpl.class);

    @Autowired
    Response response;

    @Autowired
    PurchaseInvoiceRepository purchaseInvoiceRepository;

    @Autowired
    TransactionService transactionService;

    @Autowired
    Transaction transaction;

    @Autowired
    ItemService itemService;

    @Autowired
    StockService stockService;

    @Autowired
    SupplierService supplierService;

    @Autowired
    Environment env;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    CashRecordService cashRecordService;

    @Autowired
    Sequence sequence;

    @Autowired
    Item item;

    @Override
    public PurchaseInvoice basicSave(PurchaseInvoice purchaseInvoice) {
        try {
            return purchaseInvoiceRepository.save(purchaseInvoice);
        } catch (Exception e) {
            LOGGER.error("Basic Save Purchase Invoice Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    @Transactional
    public Response save(PurchaseInvoice purchaseInvoice) {
        try {
            String seqId = "";
            sequence = sequenceService.findSequenceByName("PurchaseInvoice");
            seqId = (sequence.getPrefix() + String.valueOf((sequence.getCounter() + 1)));
            sequence = null;
            purchaseInvoice.setPurchaseInvoiceNo(seqId);

            MetaData piType = metaDataService.searchMetaData("PurchaseInvoice", "Expense");

            MetaData paymentMethod = metaDataService.searchMetaData("Cashier", "PIPaymentMethod");

            if (purchaseInvoice.getTotalAmount() <= purchaseInvoice.getPayment()) {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
            } else if (purchaseInvoice.getPayment() == 0) {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
            } else {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }

            if (purchaseInvoice.getPaymentMethod().getId().equals(paymentMethod.getId())) {
                MetaData purpose = metaDataService.searchMetaData("Purchasing", "CashOutPurpose");
                if (!cashRecordService.withdraw(purchaseInvoice.getPayment(), purpose.getId()))
                    throw new NumberFormatException("Purchase Invoice Saving Failed");
            }

            PurchaseInvoice pi = purchaseInvoiceRepository.save(purchaseInvoice);
            sequenceService.incrementSequence("PurchaseInvoice");

            if (!(pi.getPayment() <= 0)) {
                // Create transaction using the centralized method
                transactionService.createTransaction(
                    pi.getPayment(),                                      // amount
                    "-",                                                // operator (expense is negative)
                    pi.getPurchaseInvoiceNo(),                           // refNo
                    "Purchase Invoice" + "(" + pi.getPaymentMethod().getValue() + ")", // refType
                    pi.getSupplier().getName(),                          // thirdParty
                    "Expense",                                          // typeCategory
                    "PurchaseInvoice",                                  // typeValue
                    pi.getPaymentMethod().getValue(),                    // paymentMethod
                    pi.getSupplier().getRegNo(),                        // remark
                    pi.getDate()                                        // date
                );
            }

            stockService.createByPurchaseInvRecs(purchaseInvoice.getPurchaseInvoiceRecords(), false);
            response.setCode(200);
            response.setMessage("PI Added Successfully");

            return response;
        } catch (NumberFormatException ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Adding PI Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Adding PI Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Iterable<PurchaseInvoice> findAll(Integer page, Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("purchaseInvoiceNo").descending());
            return purchaseInvoiceRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<PurchaseInvoice> findAllBySupplierOrderByIdDesc(String supplier) {
        try {
            return purchaseInvoiceRepository.findAllBySupplierOrderByIdDesc(supplier);
        } catch (Exception ex) {
            LOGGER.error("Find All SalesInvoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<PurchaseInvoice> findAllByMetaDataNotCompleted(MetaData metaData) {
        try {
            return purchaseInvoiceRepository.findAllByStatusNot(metaData);
        } catch (Exception ex) {
            LOGGER.error("Find All Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public PurchaseInvoice searchByInvoiceNo(String invoiceNo) {
        return purchaseInvoiceRepository.findByInvoiceNo(invoiceNo);
    }

    @Override
    public List<PurchaseInvoice> findBySupplierId(String id) {
        try {
            Supplier supplier = supplierService.findBySupplierCode(id);
            return purchaseInvoiceRepository.findBySupplier(supplier.getId());
        } catch (Exception e) {
            LOGGER.error("Find All Customer Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<PurchaseInvoice> findAllByDate(LocalDate date) {
        try {
            List<PurchaseInvoice> invoices = purchaseInvoiceRepository.findAllByDateBetween(
                    date.atStartOfDay(), date.plusDays(1).atStartOfDay());
            return invoices;
        } catch (Exception e) {
            LOGGER.error("Find All PurchaseInvoice Failed " + e.getMessage());
            return null;
        }

    }

    @Override
    public PurchaseInvoice findById(String id) {
        return purchaseInvoiceRepository.findById(id).get();
    }

    @Override
    public PurchaseInvoice findByPurchaseInvoiceNo(String purchaseInvoiceNo) {
        try {
            return purchaseInvoiceRepository.findByPurchaseInvoiceNo(purchaseInvoiceNo);
        } catch (Exception e) {
            LOGGER.error("Find All PurchaseInvoice Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    @Transactional
    public Response payBalance(String purchaseInvoiceNo, Double amount) {
        try {
            PurchaseInvoice purchaseInvoice = purchaseInvoiceRepository.findByPurchaseInvoiceNo(purchaseInvoiceNo);

            // ✅ CRITICAL FIX: Check if purchase invoice is cancelled
            MetaData cancelledStatus = metaDataService.searchMetaData("Cancelled", "PaymentStatus");
            if (purchaseInvoice.getStatus().getId().equals(cancelledStatus.getId())) {
                response.setCode(401);
                response.setMessage("Cannot process payment for cancelled purchase invoice");
                return response;
            }

            if (purchaseInvoice.getBalance().compareTo(amount) <= 0) {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
            } else if (purchaseInvoice.getBalance().compareTo(amount) > 0 && amount > 0) {
                purchaseInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }

            purchaseInvoice.setPayment(purchaseInvoice.getPayment() + amount);
            Double balance = purchaseInvoice.getBalance() - amount;
            purchaseInvoice.setBalance(balance.compareTo(0.0) < 0.0 ? 0.0 : balance);

            purchaseInvoiceRepository.save(purchaseInvoice);

            MetaData piType = metaDataService.searchMetaData("PurchaseInvoice", "Expense");

            if (amount.compareTo(0.0) > 0) {
                // Create transaction using the centralized method
                transactionService.createTransaction(
                    amount,                                      // amount
                    "+",                                       // operator (positive for balance payment)
                    purchaseInvoice.getPurchaseInvoiceNo(),     // refNo
                    "Past Purchase Invoice Payment",            // refType
                    purchaseInvoice.getSupplier().getName(),    // thirdParty
                    "Expense",                                 // typeCategory
                    "PurchaseInvoice",                         // typeValue
                    null,                                      // paymentMethod
                    null,                                      // remark
                    LocalDateTime.now()                         // date
                );
            }

            response.setCode(200);
            response.setData(purchaseInvoice.getInvoiceNo());
            response.setMessage("Purchase Invoice Balance Paid");
            return response;
        } catch (Exception e) {
            LOGGER.error(e + "Balance Payment Failed");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            response.setData(e.getMessage());
            response.setCode(501);
            response.setMessage("Purchase Invoice Balance Paying Failed");
            return response;
        }
    }

    @Override
    public List<PurchaseInvoice> findAllByStatusId(String statusId) {
        try {
            List<PurchaseInvoice> list = purchaseInvoiceRepository.findAllByStatusIdOrderByPurchaseInvoiceNoDesc(statusId);
            return list;
        } catch (Exception e) {
            LOGGER.error("Invoice Searching Operation Failed");
            return null;
        }
    }
}
