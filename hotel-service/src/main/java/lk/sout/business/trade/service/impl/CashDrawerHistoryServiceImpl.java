package lk.sout.business.trade.service.impl;

import lk.sout.core.entity.User;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.UserService;
import lk.sout.business.trade.entity.CashDrawer;
import lk.sout.business.trade.entity.CashRecord;
import lk.sout.business.trade.entity.CashDrawerHistory;
import lk.sout.business.trade.repository.CashDrawerHistoryRepository;
import lk.sout.business.trade.repository.CashDrawerRepository;
import lk.sout.business.trade.service.CashRecordService;
import lk.sout.business.trade.service.CashDrawerHistoryService;
import lk.sout.business.trade.service.CashDrawerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;


@Service
public class CashDrawerHistoryServiceImpl implements CashDrawerHistoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CashDrawerHistoryServiceImpl.class);

    @Autowired
    CashDrawerHistoryRepository cashDrawerHistoryRepository;

    @Autowired
    CashDrawerRepository cashDrawerRepository;

    @Autowired
    CashRecordService cashRecordService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    CashDrawerService cashDrawerService;

    @Autowired
    CashDrawerHistory cashDrawerHistory;

    @Autowired
    UserService userService;

    public boolean save(CashDrawerHistory cashDrawerHistory) {
        try {
            CashRecord cashRecord = new CashRecord();
            cashRecord.setPurpose(metaDataService.searchMetaData("Day End", "CashOutPurpose"));
            cashRecord.setType(metaDataService.searchMetaData("Cash Out", "Cash"));
            cashRecord.setAmount(cashDrawerHistory.getWithdrawalAmount());
            cashRecord.setCounter(cashDrawerHistory.getDrawerNo());
            cashRecord.setDate(cashDrawerHistory.getDate());
            if (cashDrawerHistory.getShortage() != 0) {
                cashDrawerService.correctCashDrawer(cashDrawerHistory.getActualAmount(), "1");
            }
            cashRecordService.save(cashRecord);
            cashDrawerHistoryRepository.save(cashDrawerHistory);
            LOGGER.info("CashierHistory saved");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Saving CashierHistory Failed: " + e.getMessage());
            return false;
        }
    }

    public List<CashDrawerHistory> findAll() {
        try {
            return cashDrawerHistoryRepository.findAll();
        } catch (Exception e) {
            LOGGER.error("Saving CashierHistory Failed: " + e.getMessage());
            return null;
        }
    }

    public CashDrawerHistory findByDateAndCounter(LocalDate date, String counter) {
        try {
            return cashDrawerHistoryRepository.findByDateAndDrawerNo(date, counter);
        } catch (Exception e) {
            LOGGER.error("Finding CashierHistory by Date Failed: " + e.getMessage());
            return null;
        }
    }

    public Boolean existsByCounter(String counter) {
        try {
            return cashDrawerHistoryRepository.existsByDrawerNo(counter);
        } catch (Exception e) {
            LOGGER.error("Finding CashierHistory by Date Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public boolean dayClose(Double actualAmount, Double withdrawalAmount, Double balance) {
        try {
            User user = userService.findUser();
            CashDrawer cashDrawer = cashDrawerService.findByDrawerNo("1");
            cashDrawerHistory.setOpeningBalance(cashDrawer.getOpeningBalance());
            cashDrawerHistory.setClosingBalance(balance);
            cashDrawerHistory.setCashierAmountBeforeDayEnd(cashDrawer.getCurrentBalance());
            cashDrawerHistory.setWithdrawalAmount(withdrawalAmount);
            cashDrawerHistory.setActualAmount(actualAmount);
            cashDrawerHistory.setShortage(cashDrawerHistory.getActualAmount() - cashDrawerHistory.getCashierAmountBeforeDayEnd());
            cashDrawerHistory.setDrawerNo("1");
            cashDrawerHistory.setDate(cashDrawer.getLastStartedDate());
            save(cashDrawerHistory);
            return true;
        } catch (Exception e) {
            LOGGER.error("Finding CashierHistory by Date Failed: " + e.getMessage());
            return false;
        }
    }

}
