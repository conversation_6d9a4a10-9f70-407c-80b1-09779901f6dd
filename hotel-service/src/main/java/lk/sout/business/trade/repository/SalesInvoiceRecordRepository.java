package lk.sout.business.trade.repository;

import lk.sout.business.trade.entity.SalesInvoiceRecord;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SalesInvoiceRecordRepository extends MongoRepository<SalesInvoiceRecord,String> {

    List<SalesInvoiceRecord> findAllByItemCodeAndDate(String itemCode, LocalDateTime date);

    List<SalesInvoiceRecord> findAllByItemCodeAndDateBetween(String itemCode, LocalDateTime startDate, LocalDateTime endDate);

    List<SalesInvoiceRecord> findAllByItemCode(String itemCode);
}
