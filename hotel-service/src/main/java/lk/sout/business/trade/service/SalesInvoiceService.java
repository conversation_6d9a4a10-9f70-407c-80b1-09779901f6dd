package lk.sout.business.trade.service;

import lk.sout.core.entity.Response;
import lk.sout.business.trade.entity.PayBalance;
import lk.sout.business.trade.entity.SalesInvoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 2/4/2020
 */
public interface SalesInvoiceService {

    Response save(SalesInvoice salesInvoice, boolean isUpdate);

    SalesInvoice basicSave(SalesInvoice salesInvoice);

    Iterable<SalesInvoice> findAll(Pageable pageable);

    Iterable<SalesInvoice> findAllPendingPages(Pageable pageable);

    List<SalesInvoice> findAllByOrderByIdDesc();

    List<SalesInvoice> findAllByCustomerOrderByIdDesc(String customer);

    List<SalesInvoice> findByCustomerNicBr(String nicBr);

    List<SalesInvoice> findPendingByCustomer(String nicBr);

    SalesInvoice findByReference(String reference);

    SalesInvoice findByInvNo(String invNo);

    Iterable<SalesInvoice> findAllByPaymentMethod(String paymentMethodId, Pageable pageable);

    List<SalesInvoice> findByDate(LocalDate Date);

    List<SalesInvoice> findAllByMetaDataNotCompleted();

    List<SalesInvoice> findAllBalanceGreaterThan(Double balance);

    List<SalesInvoice> findBySalesInvoiceIdLikeIgnoreCase(String invoiceNo);

    SalesInvoice findById(String id);

    Response payBalance(PayBalance payBalance);

    Iterable<SalesInvoice> findAllByPaymentStatus(String paymentStatusId, Pageable pageable);

    Response amendSi(String invoiceNo);

    /**
     * Find sales invoices by date range with pagination
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @param pageable Pagination information
     * @return Page of sales invoices for the date range
     */
    Page<SalesInvoice> findByDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find all sales invoices by date range
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @return List of sales invoices for the date range
     */
    List<SalesInvoice> findAllByDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find sales invoices by customer ID with pagination
     * @param customerId Customer ID
     * @param pageable Pagination information
     * @return Page of sales invoices for the customer
     */
    Page<SalesInvoice> findByCustomerId(String customerId, Pageable pageable);

    /**
     * Find all sales invoices
     * @return List of all sales invoices
     */
    List<SalesInvoice> findAll();

    /**
     * Find all sales invoices by customer ID
     * @param customerId Customer ID
     * @return List of sales invoices for the customer
     */
    List<SalesInvoice> findAllByCustomerId(String customerId);

    /**
     * Find all sales invoices by payment status ID
     * @param statusId Payment status ID
     * @return List of sales invoices for the payment status
     */
    List<SalesInvoice> findAllByPaymentStatusId(String statusId);

    /**
     * Generate Excel report for sales invoices
     * @param invoices List of sales invoices to include in the report
     * @return Excel file as ByteArrayInputStream
     */
    ByteArrayInputStream generateExcelReport(List<SalesInvoice> invoices);

    /**
     * Generate PDF report for sales invoices
     * @param invoices List of sales invoices to include in the report
     * @return PDF file as ByteArrayInputStream
     */
    ByteArrayInputStream generatePdfReport(List<SalesInvoice> invoices);

    /**
     * Update an existing sales invoice
     * @param salesInvoice The sales invoice with updated information
     * @return Response object with status and message
     */
    Response updateInvoice(SalesInvoice salesInvoice);

    /**
     * Find sales invoices with multiple filters
     * @param startDate Start date (optional)
     * @param endDate End date (optional)
     * @param customerNo Customer number (optional)
     * @param invoiceNo Invoice number (optional)
     * @param drawerNo Cash drawer number (optional)
     * @param cashierUserName Cashier username (optional)
     * @param routeNo Route number (optional)
     * @param pageable Pagination information
     * @return Page of filtered sales invoices
     */
    Page<SalesInvoice> findWithFilters(String startDate, String endDate, String customerNo,
                                      String invoiceNo, String drawerNo, String cashierUserName,
                                      String routeNo, Pageable pageable);
}
