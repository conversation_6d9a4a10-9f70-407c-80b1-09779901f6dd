package lk.sout.business.trade.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.business.trade.entity.Customer;
import lk.sout.business.trade.repository.CustomerRepository;
import lk.sout.business.trade.service.CustomerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */

@Service
public class CustomerServiceImpl implements CustomerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerServiceImpl.class);

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    Response response;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    SequenceService sequenceService;

    @Override
    public Response save(Customer customer) {
        try {
            // Check if customer with same telephone exists
            if (customer.getTelephone1() != null && !customer.getTelephone1().isEmpty()) {
                Customer existingCustomer = findByTelephone(customer.getTelephone1());
                if (existingCustomer != null && (customer.getId() == null || !customer.getId().equals(existingCustomer.getId()))) {
                    response.setSuccess(false);
                    response.setCode(400);
                    response.setMessage("Customer with telephone " + customer.getTelephone1() + " already exists");
                    return response;
                }
            }

            // Handle updates vs new customers differently
            if (customer.getId() != null) {
                // This is an update to an existing customer
                try {
                    // Get the existing customer to preserve important fields
                    Customer existingCustomer = customerRepository.findById(customer.getId()).orElse(null);
                    if (existingCustomer != null) {
                        // Preserve the balance from the existing customer
                        customer.setBalance(existingCustomer.getBalance());
                    } else {
                        LOGGER.warn("Update requested for customer ID: " + customer.getId() + " but customer not found");
                    }
                } catch (Exception ex) {
                    LOGGER.error("Error retrieving existing customer for update: " + ex.getMessage());
                }
            } else {
                // This is a new customer
                // Check if customer with same NIC exists for new customers
                if (customer.getNicBr() != null && !customer.getNicBr().isEmpty() && checkNic(customer.getNicBr())) {
                    response.setSuccess(false);
                    response.setCode(400);
                    response.setMessage("Customer with NIC " + customer.getNicBr() + " already exists");
                    return response;
                }

                // Set initial balance to 0 for new customers
                customer.setBalance(0.0);

                // Always generate a customer number for new customers
                if (customer.getCustomerNo() == null || customer.getCustomerNo().isEmpty()) {
                    // Generate customer number
                    Sequence sequence = sequenceService.findSequenceByName("CustomerNo");
                    if (sequence != null) {
                        String customerNo = sequence.getPrefix() + String.format("%04d", sequence.getCounter() + 1);
                        customer.setCustomerNo(customerNo);

                        // Increment the sequence counter
                        sequence.setCounter(sequence.getCounter() + 1);
                        sequenceService.save(sequence);
                    } else {
                        LOGGER.error("CustomerNo sequence not found");
                        response.setSuccess(false);
                        response.setCode(500);
                        response.setMessage("Failed to generate customer number: sequence not found");
                        return response;
                    }
                }
            }

            // Save the customer
            customerRepository.save(customer);

            // Set response
            response.setSuccess(true);
            response.setCode(200);
            response.setMessage("Customer " + (customer.getId() != null ? "Updated" : "Created") + " Successfully");

            return response;
        } catch (Exception ex) {
            LOGGER.error("Creating/Updating Customer Failed: " + ex.getMessage());

            response.setSuccess(false);
            response.setCode(500);
            response.setMessage("Creating/Updating Customer Failed: " + ex.getMessage());

            return response;
        }
    }

    @Override
    public Iterable<Customer> findAll(Pageable pageable) {
        try {
            return customerRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Customers Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findAllActive(boolean result) {
        try {
            return customerRepository.findAllByActive(result);
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Customer findById(String id) {
        try {
            return customerRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Customer findByNicBr(String nicBr) {
        try {
            return customerRepository.findByNicBr(nicBr);
        } catch (Exception ex) {
            LOGGER.error("Find by NIC BR Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findByNicLike(String nic) {
        try {
            return customerRepository.findByNicBrLike(nic);
        } catch (Exception ex) {
            LOGGER.error("Find by NIC BR Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findByTpLike(String tp) {
        try {
            return customerRepository.findByTelephone1Like(tp);
        } catch (Exception ex) {
            LOGGER.error("Find by NIC BR Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findAllByNameLikeIgnoreCaseAndActive(String itemCode, Boolean active) {
        return customerRepository.findAllByNameLikeIgnoreCase(itemCode);
    }

    public Customer findDefaultCustomer() {
        Customer customer = customerRepository.findByName("Default Customer");
        return customer;
    }

    @Override
    public boolean checkNic(String nic) {
        try {
            if (null != customerRepository.findByNicBr(nic)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Customer by nic Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Customer findByTelephone(String telephone) {
        try {
            return customerRepository.findByTelephone1(telephone);
        } catch (Exception ex) {
            LOGGER.error("Find customer by telephone failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Customer findByCustomerNo(String customerNo) {
        try {
            return customerRepository.findByCustomerNo(customerNo);
        } catch (Exception ex) {
            LOGGER.error("Find customer by customer number failed: " + ex.getMessage());
            return null;
        }
    }
}
