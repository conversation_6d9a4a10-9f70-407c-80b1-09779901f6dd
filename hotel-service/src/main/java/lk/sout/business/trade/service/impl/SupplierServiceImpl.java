/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.business.trade.service.impl;

import lk.sout.business.trade.entity.Supplier;
import lk.sout.business.trade.repository.SupplierRepository;
import lk.sout.business.trade.service.SupplierService;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.service.SequenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> PC
 */
@Service
public class SupplierServiceImpl implements SupplierService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierServiceImpl.class);

    @Autowired
    SupplierRepository supplierRepository;

    @Autowired
    Response response;

    @Autowired
    SequenceService sequenceService;

    @Override
    public Response save(Supplier supplier) {
        try {
            // Generate supplier number for new suppliers
            if (supplier.getId() == null) {
                // Always generate a supplier number for new suppliers
                if (supplier.getSupplierNo() == null || supplier.getSupplierNo().isEmpty()) {
                    // Generate supplier number
                    Sequence sequence = sequenceService.findSequenceByName("SupplierNo");
                    if (sequence != null) {
                        String supplierNo = sequence.getPrefix() + String.format("%04d", sequence.getCounter() + 1);
                        supplier.setSupplierNo(supplierNo);

                        // Increment the sequence counter
                        sequence.setCounter(sequence.getCounter() + 1);
                        sequenceService.save(sequence);
                        LOGGER.info("Generated supplier number: " + supplierNo);
                    } else {
                        LOGGER.error("SupplierNo sequence not found");
                        response.setCode(501);
                        response.setMessage("Failed to generate supplier number: sequence not found");
                        return response;
                    }
                }
            }

            // Save the supplier
            supplierRepository.save(supplier);

            // Set response
            response.setSuccess(true);
            response.setCode(200);
            response.setMessage("Supplier " + (supplier.getId() != null ? "Updated" : "Created") + " Successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Creating/Updating Supplier Failed: " + ex.getMessage());

            response.setSuccess(false);
            response.setCode(500);
            response.setMessage("Creating/Updating Supplier Failed: " + ex.getMessage());

            return response;
        }
    }

    @Override
    public Iterable<Supplier> findAll(Integer page, Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("id").descending());
            return  supplierRepository.findAll(pageable);
        }catch (Exception e){
            LOGGER.error(e + "Supplier retrieving failed");
            return null;
        }
    }

    @Override
    public List<Supplier> findAllActive(boolean result) {
        try {
            return supplierRepository.findAllByNameNotAndActiveOrderByIdDesc("Default Supplier",result);
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Supplier findBySupplierCode(String supplierCode) {
        return supplierRepository.findByRegNoLikeAndNameNot(supplierCode,"Default Supplier");
    }

    @Override
    public Supplier findById(String id) {
        return supplierRepository.findById(id).get();
    }

    @Override
    public Supplier findByCode(String supplierCode) {
        return supplierRepository.findByRegNo(supplierCode);
    }

    @Override
    public List<Supplier> findByNameLikeIgnoreCase(String supplierName) {
        return supplierRepository.findByNameLikeIgnoreCaseAndRegNoNot(supplierName,"default id");
    }

    @Override
    public boolean findSupplierCode(String text) {
        boolean check = false;
        if (supplierRepository.findByRegNoIgnoreCase(text) != null) {
            check = true;
        } else if (supplierRepository.findByRegNoIgnoreCase(text) == null) {
            check = false;
        }
        return check;
    }

    @Override
    public Supplier findDefaultSupplier() {
        Supplier supplier =  supplierRepository.findByName("Default Supplier");
        return supplier;
    }
}
