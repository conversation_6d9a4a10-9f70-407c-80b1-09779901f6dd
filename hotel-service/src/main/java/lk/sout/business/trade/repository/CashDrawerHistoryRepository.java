package lk.sout.business.trade.repository;


import lk.sout.business.trade.entity.CashDrawerHistory;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

@Repository
public interface CashDrawerHistoryRepository extends MongoRepository<CashDrawerHistory, String> {

    CashDrawerHistory findByDateAndDrawerNo(LocalDate date, String counter);

    boolean existsByDrawerNo(String counter);
}
