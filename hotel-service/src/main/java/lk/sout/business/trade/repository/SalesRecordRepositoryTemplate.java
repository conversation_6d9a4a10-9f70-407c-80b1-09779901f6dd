package lk.sout.business.trade.repository;

import lk.sout.business.inventory.entity.Item;
import lk.sout.business.trade.entity.ItemSaleSummaryAggr;
import lk.sout.business.trade.entity.SalesInvoiceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

@Repository
public class SalesRecordRepositoryTemplate {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesRecordRepositoryTemplate.class);

    @Autowired
    MongoTemplate mongoTemplate;

    /**
     * Find sales grouped by date between date range with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    public List<ItemSaleSummaryAggr> findSalesGroupByDateBetween(LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} profit between {} and {}", unrealized ? "unrealized" : "realized", sDate, eDate);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode",
                            "recordType","paymentStatus").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by date range
                    match(Criteria.where("createdDate").gte(sDate).lt(eDate.plusDays(1))),
                    match(Criteria.where("recordType").is("Sale")),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode") // Keep the barcode
                            .sum("quantity").as("quantity")
                            .sum("price").as("subTotal")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "subTotal", "cost", "itemName", "barcode", "itemCode").
                            andExpression("subTotal - cost").as("profit")
            );
            AggregationResults<ItemSaleSummaryAggr> groupResults
                    = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            List<ItemSaleSummaryAggr> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            LOGGER.error("Error finding {} profit: {}", unrealized ? "unrealized" : "realized", ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    /**
     * Find unrealized profit (from non-paid sales) between dates
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return Total profit amount
     */
    public Double findUnrealizedProfitBetween(LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} profit between {} and {}", unrealized ? "unrealized" : "realized", sDate, eDate);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need
                    project("quantity", "createdDate", "itemCost", "price", "recordType", "paymentStatus")
                            .andExpression("quantity * itemCost").as("cost"),
                    // Filter by date range
                    match(Criteria.where("createdDate").gte(sDate).lt(eDate.plusDays(1))),
                    match(Criteria.where("recordType").is("Sale")),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group all records together
                    group()
                            .sum("price").as("totalPrice")
                            .sum("cost").as("totalCost"),
                    // Calculate total profit
                    project()
                            .andExpression("totalPrice - totalCost").as("unrealizedProfit")
            );

            AggregationResults<org.bson.Document> results = mongoTemplate.aggregate(agg, "salesInvoiceRecord", org.bson.Document.class);
            List<org.bson.Document> resultList = results.getMappedResults();

            if (resultList.isEmpty()) {
                return 0.0;
            }

            org.bson.Document result = resultList.get(0);
            Double profit = result.getDouble("unrealizedProfit");
            return profit != null ? profit : 0.0;
        } catch (Exception ex) {
            LOGGER.error("Error finding {} profit: {}", unrealized ? "unrealized" : "realized", ex.getMessage(), ex);
            return 0.0;
        }
    }

    public List<ItemSaleSummaryAggr> findSalesItemGroupByDateAndItem(Item item, LocalDate sDate, LocalDate eDate) {
        try {
            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "date").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by date range and item code
                    match(Criteria.where("createdDate").gt(sDate).lt(eDate)),
                    match(Criteria.where("itemCode").regex(item.getItemCode())),
                    // Group by date and itemCode, preserving barcode and itemName
                    group("date", "itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode") // Keep the barcode
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "date", "itemName", "barcode", "itemCode").
                            andExpression("price - cost").as("profit")
            );
            AggregationResults<ItemSaleSummaryAggr> groupResults
                    = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            List<ItemSaleSummaryAggr> result = groupResults.getMappedResults();

            return result;
        } catch (Exception ex) {
            LOGGER.error("Error finding sales item group by date and item: {}", ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by cash drawer (cashier) with option to filter by payment status
     * @param drawerNo Cash drawer number
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByCashier(String drawerNo, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by cash drawer: {}", unrealized ? "unrealized" : "realized", drawerNo);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "drawerNo", "paymentStatus").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by drawer number
                    match(Criteria.where("drawerNo").is(drawerNo)),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .first("paymentStatus").as("paymentStatus")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode", "paymentStatus").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by cash drawer: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by cash drawer (cashier) and date range with option to filter by payment status
     * @param drawerNo Cash drawer number
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByCashierAndDateRange(String drawerNo, LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by cash drawer: {} between {} and {}", unrealized ? "unrealized" : "realized", drawerNo, sDate, eDate);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "drawerNo", "paymentStatus").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by drawer number and date range
                    match(Criteria.where("drawerNo").is(drawerNo).and("createdDate").gt(sDate).lt(eDate)),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .first("paymentStatus").as("paymentStatus")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode", "paymentStatus").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by cash drawer and date range: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by user with option to filter by payment status
     * @param username User's username
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByUser(String username, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by user: {}", unrealized ? "unrealized" : "realized", username);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "cashierUserName", "paymentStatus").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by username
                    match(Criteria.where("cashierUserName").is(username)),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .first("paymentStatus").as("paymentStatus")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode", "paymentStatus").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by user: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by user and date range with option to filter by payment status
     * @param username User's username
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByUserAndDateRange(String username, LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by user: {} between {} and {}", unrealized ? "unrealized" : "realized", username, sDate, eDate);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "cashierUserName", "paymentStatus").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by username and date range
                    match(Criteria.where("cashierUserName").is(username).and("createdDate").gt(sDate).lt(eDate)),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .first("paymentStatus").as("paymentStatus")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode", "paymentStatus").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by user and date range: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by route with option to filter by payment status
     * @param routeNo Route number
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByRoute(String routeNo, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by route: {}", unrealized ? "unrealized" : "realized", routeNo);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "routeNo", "paymentStatus").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by route number
                    match(Criteria.where("routeNo").is(routeNo)),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .first("paymentStatus").as("paymentStatus")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode", "paymentStatus").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by route: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by route and date range with option to filter by payment status
     * @param routeNo Route number
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByRouteAndDateRange(String routeNo, LocalDate sDate, LocalDate eDate, boolean unrealized) {
        try {
            LOGGER.info("Finding {} sales by route: {} between {} and {}", unrealized ? "unrealized" : "realized", routeNo, sDate, eDate);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "routeNo", "paymentStatus").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by route number and date range
                    match(Criteria.where("routeNo").is(routeNo).and("createdDate").gt(sDate).lt(eDate)),
                    // Filter by payment status based on unrealized flag
                    unrealized ? match(Criteria.where("paymentStatus").ne("Paid")) : match(Criteria.where("paymentStatus").is("Paid")),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .first("paymentStatus").as("paymentStatus")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode", "paymentStatus").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding {} sales by route and date range: {}", unrealized ? "unrealized" : "realized", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by user and route
     * @param username User's username
     * @param routeNo Route number
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByUserAndRoute(String username, String routeNo) {
        try {
            LOGGER.info("Finding sales by user: {} and route: {}", username, routeNo);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "cashierUserName", "routeNo").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by username and route number
                    match(Criteria.where("cashierUserName").is(username).and("routeNo").is(routeNo)),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding sales by user and route: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find sales records by user, route, and date range
     * @param username User's username
     * @param routeNo Route number
     * @param sDate Start date
     * @param eDate End date
     * @return List of ItemSaleSummaryAggr
     */
    public List<ItemSaleSummaryAggr> findSalesByUserAndRouteAndDateRange(String username, String routeNo, LocalDate sDate, LocalDate eDate) {
        try {
            LOGGER.info("Finding sales by user: {} and route: {} between {} and {}", username, routeNo, sDate, eDate);

            TypedAggregation<SalesInvoiceRecord> agg = newAggregation(SalesInvoiceRecord.class,
                    // First, project all the fields we need including barcode
                    project("quantity", "createdDate", "itemCost", "price", "itemCode", "itemName", "barcode", "cashierUserName", "routeNo").
                            andExpression("quantity * itemCost").as("cost"),
                    // Filter by username, route number, and date range
                    match(Criteria.where("cashierUserName").is(username).and("routeNo").is(routeNo).and("createdDate").gt(sDate).lt(eDate)),
                    // Group by itemCode and preserve barcode and itemName
                    group("itemCode")
                            .first("itemName").as("itemName")
                            .first("barcode").as("barcode")
                            .sum("quantity").as("quantity")
                            .sum("price").as("price")
                            .sum("cost").as("cost"),
                    // Make sure to include barcode in the final projection
                    project("quantity", "price", "cost", "itemName", "barcode", "itemCode").
                            andExpression("price - cost").as("profit")
            );

            AggregationResults<ItemSaleSummaryAggr> groupResults = mongoTemplate.aggregate(agg, ItemSaleSummaryAggr.class);
            return groupResults.getMappedResults();
        } catch (Exception e) {
            LOGGER.error("Error finding sales by user, route, and date range: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

}
