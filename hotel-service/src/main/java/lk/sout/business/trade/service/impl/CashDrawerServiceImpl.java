package lk.sout.business.trade.service.impl;

import lk.sout.core.entity.Action;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import lk.sout.business.trade.entity.CashDrawer;
import lk.sout.business.trade.repository.CashDrawerRepository;
import lk.sout.business.trade.service.CashDrawerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class CashDrawerServiceImpl implements CashDrawerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CashDrawerServiceImpl.class);

    @Autowired
    CashDrawerRepository cashDrawerRepository;

    @Autowired
    CashDrawer newCashDrawer;

    @Autowired
    Action action;

    @Autowired
    ActionService actionService;

    @Autowired
    MetaDataService metaDataService;

    public boolean save(CashDrawer cashDrawer) {
        try {
            cashDrawerRepository.save(cashDrawer);
            LOGGER.info("Cashier saved");
            return true;
        } catch (Exception e) {
            LOGGER.error("Saving Cashier Failed: " + e.getMessage());
            return false;
        }
    }

    public boolean topUpCashDrawer(Double topUpAmount, String counterId) {
        try {
            CashDrawer cashDrawer = cashDrawerRepository.findByDrawerNo(counterId);
            if (null != cashDrawer) {
                Double avlBalance = cashDrawer.getCurrentBalance();
                cashDrawer.setCurrentBalance(avlBalance + topUpAmount);
                cashDrawerRepository.save(cashDrawer);
            } else {
                newCashDrawer.setId(null);
                newCashDrawer.setOpeningBalance(0.0);
                newCashDrawer.setDrawerNo(counterId);
                newCashDrawer.setCurrentBalance(topUpAmount);
                cashDrawerRepository.save(newCashDrawer);
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Top up Cashier Failed");
            return false;
        }
    }

    public boolean deductFromCashDrawer(Double amount, String counterId) {
        try {
            CashDrawer cashDrawer = cashDrawerRepository.findByDrawerNo(counterId);
            if (null != cashDrawer) {
                Double avlBalance = cashDrawer.getCurrentBalance();
                if (avlBalance.compareTo(amount) >= 0) {
                    cashDrawer.setCurrentBalance(avlBalance - amount);
                    cashDrawerRepository.save(cashDrawer);
                } else {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Deducting From Cashier Failed");
            return false;
        }
    }

    public boolean correctCashDrawer(Double amount, String counterId) {
        try {
            CashDrawer cashDrawer = cashDrawerRepository.findByDrawerNo(counterId);
            if (null != cashDrawer) {
                cashDrawer.setCurrentBalance(amount);
                cashDrawerRepository.save(cashDrawer);

                action.setOperator(cashDrawer.getCurrentBalance().compareTo(amount) > 0 ? "-" : "+");
                action.setReference(counterId);
                action.setChange(String.valueOf(amount - cashDrawer.getCurrentBalance()));
                action.setRemark("Cashier balance altered");
                action.setType(metaDataService.searchMetaData("Cashier Amount Alter", "Action").getValue());
                actionService.save(action);
            } else {
                return false;
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Deducting From Cashier Failed");
            return false;
        }
    }

    public List<CashDrawer> findAll() {
        try {
            return cashDrawerRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Retrieving Cashier details failed");
            return null;
        }
    }

    public CashDrawer findByDrawerNo(String counter) {
        try {
            CashDrawer cashDrawer = cashDrawerRepository.findByDrawerNo(counter);
            return cashDrawer;
        } catch (Exception ex) {
            LOGGER.error("Retrieving Cashier details failed");
            return null;
        }
    }

    @Override
    public boolean checkDrawerNoStatus(String counter) {
        CashDrawer cashDrawer = findByDrawerNo(counter);
        if (!cashDrawer.isActive() && cashDrawer.getLastClosedDate().isEqual(LocalDate.now())) {
            return false;
        }
        if (cashDrawer.isActive() && cashDrawer.getLastStartedDate().isBefore(LocalDate.now())) {
            return false;
        }
        //this should located at last
        if (cashDrawer.getLastStartedDate().isBefore(LocalDate.now())) {
            return false;
        }
        return true;
    }



}
