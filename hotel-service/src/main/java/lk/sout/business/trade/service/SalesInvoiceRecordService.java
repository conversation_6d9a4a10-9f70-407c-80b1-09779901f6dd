package lk.sout.business.trade.service;

import lk.sout.business.trade.entity.ItemSaleSummaryAggr;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 2/4/2020
 */
public interface SalesInvoiceRecordService {


    /**
     * Find profit by range filter with option to filter by payment status
     * @param rangeId Range ID from metadata
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    List<ItemSaleSummaryAggr> findByRangeFilter(String rangeId, boolean unrealized);


    /**
     * Find profit between dates with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    List<ItemSaleSummaryAggr> findProfitBetween(LocalDate sDate, LocalDate eDate, boolean unrealized);


    /**
     * Find sales grouped by date between dates with option to filter by payment status
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr with profit data
     */
    List<ItemSaleSummaryAggr> findSalesGroupByDateBetween(LocalDate sDate, LocalDate eDate, boolean unrealized);

    /**
     * Find unrealized profit (from non-paid sales) between dates
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return Total profit amount
     */
    Double findUnrealizedProfitBetween(LocalDate sDate, LocalDate eDate, boolean unrealized);


    List<ItemSaleSummaryAggr> findSalesItemGroupByDate(String itemId, LocalDate sDate, LocalDate eDate);


    /**
     * Find sales records by cash drawer (cashier) with option to filter by payment status
     * @param drawerNo Cash drawer number
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByCashier(String drawerNo, boolean unrealized);

    /**
     * Find sales records by cash drawer (cashier) and date range with option to filter by payment status
     * @param drawerNo Cash drawer number
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByCashier(String drawerNo, LocalDate sDate, LocalDate eDate, boolean unrealized);

    /**
     * Find sales records by user with option to filter by payment status
     * @param username User's username
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByUser(String username, boolean unrealized);

    /**
     * Find sales records by user and date range with option to filter by payment status
     * @param username User's username
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByUser(String username, LocalDate sDate, LocalDate eDate, boolean unrealized);

    /**
     * Find sales records by route with option to filter by payment status
     * @param routeNo Route number
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByRoute(String routeNo, boolean unrealized);

    /**
     * Find sales records by route and date range with option to filter by payment status
     * @param routeNo Route number
     * @param sDate Start date
     * @param eDate End date
     * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByRoute(String routeNo, LocalDate sDate, LocalDate eDate, boolean unrealized);

    /**
     * Find sales records by user and route
     * @param username User's username
     * @param routeNo Route number
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByUserAndRoute(String username, String routeNo);

    /**
     * Find sales records by user, route, and date range
     * @param username User's username
     * @param routeNo Route number
     * @param sDate Start date
     * @param eDate End date
     * @return List of ItemSaleSummaryAggr
     */
    List<ItemSaleSummaryAggr> findSalesByUserAndRoute(String username, String routeNo, LocalDate sDate, LocalDate eDate);
}
