package lk.sout.business.trade.service.impl;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import lk.sout.business.trade.entity.SalesInvoice;
import lk.sout.business.trade.repository.SalesInvoiceRepository;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Stream;

/**
 * Implementation of Sales Invoice Report Service
 */
@Service
public class SalesInvoiceReportServiceImpl {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceReportServiceImpl.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private SalesInvoiceRepository salesInvoiceRepository;

    /**
     * Find sales invoices by date range
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @return List of sales invoices for the date range
     */
    public List<SalesInvoice> findByDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return salesInvoiceRepository.findAllByDateBetween(startDate, endDate);
    }

    /**
     * Export sales invoices to Excel
     * @param invoices List of sales invoices to include in the report
     * @return Excel file as ByteArrayInputStream
     */
    public ByteArrayInputStream exportToExcel(List<SalesInvoice> invoices) {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            Sheet sheet = workbook.createSheet("Sales Invoices");

            // Create header row
            Row headerRow = sheet.createRow(0);

            // Apply header style
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);

            // Create header cells
            String[] columns = {
                    "Invoice No", "Date", "Customer", "Total Amount",
                    "Payment", "Balance", "Payment Method", "Status"
            };

            for (int i = 0; i < columns.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columns[i]);
                cell.setCellStyle(headerStyle);
            }

            // Create data rows
            if (invoices == null || invoices.isEmpty()) {
                // If no invoices provided, get all invoices
                invoices = salesInvoiceRepository.findAll();
            }

            int rowIdx = 1;
            for (SalesInvoice invoice : invoices) {
                Row row = sheet.createRow(rowIdx++);

                row.createCell(0).setCellValue(invoice.getInvoiceNo());
                row.createCell(1).setCellValue(invoice.getDate() != null ?
                        invoice.getDate().format(DATE_FORMATTER) : "");
                row.createCell(2).setCellValue(invoice.getCustomerName() != null ?
                        invoice.getCustomerName() : "");
                row.createCell(3).setCellValue(invoice.getTotalAmount());
                row.createCell(4).setCellValue(invoice.getPayment());
                row.createCell(5).setCellValue(invoice.getBalance());
                row.createCell(6).setCellValue(invoice.getPaymentMethod() != null ?
                        invoice.getPaymentMethod().getValue() : "");
                row.createCell(7).setCellValue(invoice.getStatus() != null ?
                        invoice.getStatus().getValue() : "");
            }

            // Resize columns to fit content
            for (int i = 0; i < columns.length; i++) {
                sheet.autoSizeColumn(i);
            }

            workbook.write(out);
            return new ByteArrayInputStream(out.toByteArray());
        } catch (IOException e) {
            LOGGER.error("Error exporting sales invoices to Excel", e);
            return null;
        }
    }

    /**
     * Export sales invoices to Excel (legacy method for backward compatibility)
     * @param startDate Start date (inclusive) or null for all
     * @param endDate End date (inclusive) or null for all
     * @return Excel file as ByteArrayInputStream
     */
    public ByteArrayInputStream exportToExcel(LocalDateTime startDate, LocalDateTime endDate) {
        List<SalesInvoice> invoices;
        if (startDate != null && endDate != null) {
            invoices = salesInvoiceRepository.findAllByDateBetween(startDate, endDate);
        } else {
            invoices = salesInvoiceRepository.findAll();
        }
        return exportToExcel(invoices);
    }

    /**
     * Export sales invoices to PDF
     * @param invoices List of sales invoices to include in the report
     * @return PDF file as ByteArrayInputStream
     */
    public ByteArrayInputStream exportToPdf(List<SalesInvoice> invoices) {
        Document document = new Document(PageSize.A4.rotate());
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try {
            PdfWriter.getInstance(document, out);
            document.open();

            // Add title
            Font titleFont = (Font) FontFactory.getFont(FontFactory.HELVETICA_BOLD, 18);
            Paragraph title = new Paragraph("Sales Invoice Report", (com.itextpdf.text.Font) titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            document.add(title);
            document.add(Chunk.NEWLINE);



            // Create table
            PdfPTable table = new PdfPTable(8);
            table.setWidthPercentage(100);

            // Set column widths
            float[] columnWidths = {1.5f, 2f, 2f, 1.5f, 1.5f, 1.5f, 2f, 1.5f};
            table.setWidths(columnWidths);

            // Add table headers
            Stream.of("Invoice No", "Date", "Customer", "Total Amount",
                    "Payment", "Balance", "Payment Method", "Status")
                    .forEach(columnTitle -> {
                        PdfPCell header = new PdfPCell();
                        header.setBackgroundColor(BaseColor.LIGHT_GRAY);
                        header.setBorderWidth(1);
                        header.setPhrase(new Phrase(columnTitle));
                        header.setHorizontalAlignment(Element.ALIGN_CENTER);
                        header.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        header.setPadding(5);
                        table.addCell(header);
                    });

            // Add data rows
            if (invoices == null || invoices.isEmpty()) {
                // If no invoices provided, get all invoices
                invoices = salesInvoiceRepository.findAll();
            }

            for (SalesInvoice invoice : invoices) {
                table.addCell(createCell(invoice.getInvoiceNo()));
                table.addCell(createCell(invoice.getDate() != null ?
                        invoice.getDate().format(DATE_FORMATTER) : ""));
                table.addCell(createCell(invoice.getCustomerName() != null ?
                        invoice.getCustomerName() : ""));
                table.addCell(createCell(String.format("%.2f", invoice.getTotalAmount())));
                table.addCell(createCell(String.format("%.2f", invoice.getPayment())));
                table.addCell(createCell(String.format("%.2f", invoice.getBalance())));
                table.addCell(createCell(invoice.getPaymentMethod() != null ?
                        invoice.getPaymentMethod().getValue() : ""));
                table.addCell(createCell(invoice.getStatus() != null ?
                        invoice.getStatus().getValue() : ""));
            }

            document.add(table);
            document.close();

            return new ByteArrayInputStream(out.toByteArray());
        } catch (DocumentException e) {
            LOGGER.error("Error exporting sales invoices to PDF", e);
            return null;
        }
    }

    /**
     * Export sales invoices to PDF (legacy method for backward compatibility)
     * @param startDate Start date (inclusive) or null for all
     * @param endDate End date (inclusive) or null for all
     * @return PDF file as ByteArrayInputStream
     */
    public ByteArrayInputStream exportToPdf(LocalDateTime startDate, LocalDateTime endDate) {
        List<SalesInvoice> invoices;
        if (startDate != null && endDate != null) {
            invoices = salesInvoiceRepository.findAllByDateBetween(startDate, endDate);
        } else {
            invoices = salesInvoiceRepository.findAll();
        }
        return exportToPdf(invoices);
    }

    /**
     * Create a cell for the PDF table
     * @param text Cell text
     * @return PdfPCell
     */
    private PdfPCell createCell(String text) {
        PdfPCell cell = new PdfPCell(new Phrase(text));
        cell.setPadding(5);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        return cell;
    }
}
