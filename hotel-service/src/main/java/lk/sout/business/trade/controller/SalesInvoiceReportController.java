package lk.sout.business.trade.controller;

import lk.sout.config.Constant;
import lk.sout.business.trade.entity.SalesInvoice;
import lk.sout.business.trade.service.SalesInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller for Sales Invoice Report
 */
@RestController
@RequestMapping("/salesInvoiceReport")
public class SalesInvoiceReportController {

    @Autowired
    private SalesInvoiceService salesInvoiceService;

    /**
     * Get all sales invoices without pagination
     * @return List of all sales invoices
     */
    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAll(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            // Return all invoices without pagination
            return ResponseEntity.ok(salesInvoiceService.findAll());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by date range without pagination
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @return List of sales invoices for the date range
     */
    @RequestMapping(value = "/findByDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> findByDateRange(
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate endDate) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

            // Return all invoices for the date range without pagination
            List<SalesInvoice> invoices = salesInvoiceService.findAllByDateBetween(startDateTime, endDateTime);

            return ResponseEntity.ok(invoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by customer without pagination
     * @param customerNo Customer Number
     * @return List of sales invoices for the customer
     */
    @RequestMapping(value = "/findByCustomer", method = RequestMethod.GET)
    public ResponseEntity<?> findByCustomer(
            @RequestParam String customerNo,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            // Get all invoices
            List<SalesInvoice> allInvoices = salesInvoiceService.findAll();

            // Filter invoices by customer number
            List<SalesInvoice> filteredInvoices = allInvoices.stream()
                .filter(invoice -> invoice.getCustomerNo() != null && customerNo.equals(invoice.getCustomerNo()))
                .collect(Collectors.toList());

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by payment status without pagination
     * @param statusId Payment status ID
     * @return List of sales invoices for the payment status
     */
    @RequestMapping(value = "/findByStatus", method = RequestMethod.GET)
    public ResponseEntity<?> findByStatus(
            @RequestParam String statusId,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            // Return all invoices for the payment status without pagination
            List<SalesInvoice> invoices = salesInvoiceService.findAllByPaymentStatusId(statusId);
            return ResponseEntity.ok(invoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by cashier username without pagination
     * @param cashierUserName Cashier username
     * @return List of sales invoices for the cashier
     */
    @RequestMapping(value = "/findByCashier", method = RequestMethod.GET)
    public ResponseEntity<?> findByCashier(
            @RequestParam(required = false) String cashierUserName,
            @RequestParam(required = false) String drawerNo,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            // Get all invoices
            List<SalesInvoice> allInvoices = salesInvoiceService.findAll();
            List<SalesInvoice> filteredInvoices;

            // Check which parameter is provided
            if (drawerNo != null && !drawerNo.isEmpty()) {
                // Filter invoices by drawer number
                filteredInvoices = allInvoices.stream()
                    .filter(invoice -> invoice.getDrawerNo() != null &&
                            drawerNo.equals(invoice.getDrawerNo()))
                    .collect(Collectors.toList());
            } else if (cashierUserName != null && !cashierUserName.isEmpty()) {
                // Filter invoices by cashier username
                filteredInvoices = allInvoices.stream()
                    .filter(invoice -> invoice.getCashierUserName() != null &&
                            cashierUserName.equals(invoice.getCashierUserName()))
                    .collect(Collectors.toList());
            } else {
                // No valid filter provided
                return ResponseEntity.badRequest().body("Either cashierUserName or drawerNo must be provided");
            }

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by cashier username and date range without pagination
     * @param cashierUserName Cashier username (optional)
     * @param drawerNo Cash drawer number (optional)
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @return List of sales invoices for the cashier and date range
     */
    @RequestMapping(value = "/findByCashierAndDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> findByCashierAndDateRange(
            @RequestParam(required = false) String cashierUserName,
            @RequestParam(required = false) String drawerNo,
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate endDate,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

            // Get invoices for the date range
            List<SalesInvoice> dateRangeInvoices = salesInvoiceService.findAllByDateBetween(startDateTime, endDateTime);
            List<SalesInvoice> filteredInvoices;

            // Check which parameter is provided
            if (drawerNo != null && !drawerNo.isEmpty()) {
                // Filter by drawer number
                filteredInvoices = dateRangeInvoices.stream()
                    .filter(invoice -> invoice.getDrawerNo() != null &&
                            drawerNo.equals(invoice.getDrawerNo()))
                    .collect(Collectors.toList());
            } else if (cashierUserName != null && !cashierUserName.isEmpty()) {
                // Filter by cashier username
                filteredInvoices = dateRangeInvoices.stream()
                    .filter(invoice -> invoice.getCashierUserName() != null &&
                            cashierUserName.equals(invoice.getCashierUserName()))
                    .collect(Collectors.toList());
            } else {
                // No valid filter provided
                return ResponseEntity.badRequest().body("Either cashierUserName or drawerNo must be provided");
            }

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by route number without pagination
     * @param routeNo Route number
     * @return List of sales invoices for the route
     */
    @RequestMapping(value = "/findByRoute", method = RequestMethod.GET)
    public ResponseEntity<?> findByRoute(
            @RequestParam String routeNo,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            // Get all invoices
            List<SalesInvoice> allInvoices = salesInvoiceService.findAll();

            // Filter invoices by route number
            List<SalesInvoice> filteredInvoices = allInvoices.stream()
                .filter(invoice -> invoice.getRouteNo() != null && routeNo.equals(invoice.getRouteNo()))
                .collect(Collectors.toList());

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by route number and date range without pagination
     * @param routeNo Route number
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @return List of sales invoices for the route and date range
     */
    @RequestMapping(value = "/findByRouteAndDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> findByRouteAndDateRange(
            @RequestParam String routeNo,
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate endDate,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

            // Get invoices for the date range
            List<SalesInvoice> dateRangeInvoices = salesInvoiceService.findAllByDateBetween(startDateTime, endDateTime);

            // Filter by route number
            List<SalesInvoice> filteredInvoices = dateRangeInvoices.stream()
                .filter(invoice -> invoice.getRouteNo() != null && routeNo.equals(invoice.getRouteNo()))
                .collect(Collectors.toList());

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Export sales invoices to Excel
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @param customerId Customer ID
     * @param statusId Payment status ID
     * @return Excel file as a resource
     */
    @RequestMapping(value = "/exportToExcel", method = RequestMethod.GET)
    public ResponseEntity<InputStreamResource> exportToExcel(
            @RequestParam(required = false) @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate endDate,
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String statusId,
            @RequestParam(required = false) String cashierUserName,
            @RequestParam(required = false) String routeNo) {
        try {
            ByteArrayInputStream in;
            List<SalesInvoice> invoices = null;

            // Apply filters to get the data
            if (startDate != null && endDate != null) {
                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

                // Get all invoices for the date range
                invoices = salesInvoiceService.findAllByDateBetween(startDateTime, endDateTime);
            } else if (customerId != null) {
                // Get all invoices
                invoices = salesInvoiceService.findAll();

                // Filter invoices by customer number
                final String custId = customerId;
                invoices = invoices.stream()
                        .filter(invoice -> invoice.getCustomerNo() != null && custId.equals(invoice.getCustomerNo()))
                        .collect(Collectors.toList());
            } else if (statusId != null) {
                // Get invoices for the status
                invoices = salesInvoiceService.findAllByPaymentStatusId(statusId);
            } else {
                // Get all invoices
                invoices = salesInvoiceService.findAll();
            }

            // Apply additional filters if needed
            if (invoices != null) {
                if (customerId != null && (startDate != null || statusId != null || cashierUserName != null || routeNo != null)) {
                    final String custId = customerId;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getCustomerNo() != null && custId.equals(invoice.getCustomerNo()))
                            .collect(Collectors.toList());
                }

                if (statusId != null && (startDate != null || customerId != null || cashierUserName != null || routeNo != null)) {
                    final String statId = statusId;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getStatus() != null && statId.equals(invoice.getStatus().getId()))
                            .collect(Collectors.toList());
                }

                if (cashierUserName != null && (startDate != null || customerId != null || statusId != null || routeNo != null)) {
                    final String cashUser = cashierUserName;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getCashierUserName() != null &&
                                    cashUser.equals(invoice.getCashierUserName()))
                            .collect(Collectors.toList());
                }

                if (routeNo != null && (startDate != null || customerId != null || statusId != null || cashierUserName != null)) {
                    final String route = routeNo;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getRouteNo() != null && route.equals(invoice.getRouteNo()))
                            .collect(Collectors.toList());
                }
            }

            // Generate Excel with the filtered data
            in = salesInvoiceService.generateExcelReport(invoices);

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Disposition", "attachment; filename=sales_invoices.xlsx");

            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                    .body(new InputStreamResource(in));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by user without pagination
     * @param username Username
     * @return List of sales invoices for the user
     */
    @RequestMapping(value = "/findByUser", method = RequestMethod.GET)
    public ResponseEntity<?> findByUser(
            @RequestParam String username,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            // Get all invoices
            List<SalesInvoice> allInvoices = salesInvoiceService.findAll();

            // Filter invoices by username (cashierUserName)
            List<SalesInvoice> filteredInvoices = allInvoices.stream()
                .filter(invoice -> invoice.getCashierUserName() != null &&
                        username.equals(invoice.getCashierUserName()))
                .collect(Collectors.toList());

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by user and date range without pagination
     * @param username Username
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @return List of sales invoices for the user and date range
     */
    @RequestMapping(value = "/findByUserAndDateRange", method = RequestMethod.GET)
    public ResponseEntity<?> findByUserAndDateRange(
            @RequestParam String username,
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate endDate,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

            // Get invoices for the date range
            List<SalesInvoice> dateRangeInvoices = salesInvoiceService.findAllByDateBetween(startDateTime, endDateTime);

            // Filter by username (cashierUserName)
            List<SalesInvoice> filteredInvoices = dateRangeInvoices.stream()
                .filter(invoice -> invoice.getCashierUserName() != null &&
                        username.equals(invoice.getCashierUserName()))
                .collect(Collectors.toList());

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find sales invoices by user and route without pagination
     * @param username Username
     * @param routeNo Route number
     * @param startDate Start date (optional, format: yyyy-MM-dd)
     * @param endDate End date (optional, format: yyyy-MM-dd)
     * @return List of sales invoices for the user and route
     */
    @RequestMapping(value = "/findByUserAndRoute", method = RequestMethod.GET)
    public ResponseEntity<?> findByUserAndRoute(
            @RequestParam String username,
            @RequestParam String routeNo,
            @RequestParam(required = false) @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate endDate,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10000") Integer pageSize) {
        try {
            List<SalesInvoice> baseInvoices;

            // Check if date range is provided
            if (startDate != null && endDate != null) {
                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

                // Get invoices for the date range
                baseInvoices = salesInvoiceService.findAllByDateBetween(startDateTime, endDateTime);
            } else {
                // Get all invoices
                baseInvoices = salesInvoiceService.findAll();
            }

            // Filter by both username and route number
            List<SalesInvoice> filteredInvoices = baseInvoices.stream()
                .filter(invoice ->
                    invoice.getCashierUserName() != null && username.equals(invoice.getCashierUserName()) &&
                    invoice.getRouteNo() != null && routeNo.equals(invoice.getRouteNo()))
                .collect(Collectors.toList());

            return ResponseEntity.ok(filteredInvoices);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Export sales invoices to PDF
     * @param startDate Start date (inclusive, format: yyyy-MM-dd)
     * @param endDate End date (inclusive, format: yyyy-MM-dd)
     * @param customerId Customer ID
     * @param statusId Payment status ID
     * @return PDF file as a resource
     */
    @RequestMapping(value = "/exportToPdf", method = RequestMethod.GET)
    public ResponseEntity<InputStreamResource> exportToPdf(
            @RequestParam(required = false) @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = Constant.STANDARD_DATE_FORMAT) LocalDate endDate,
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String statusId,
            @RequestParam(required = false) String cashierUserName,
            @RequestParam(required = false) String routeNo) {
        try {
            ByteArrayInputStream in;
            List<SalesInvoice> invoices = null;

            // Apply filters to get the data
            if (startDate != null && endDate != null) {
                LocalDateTime startDateTime = startDate.atStartOfDay();
                LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

                // Get all invoices for the date range
                invoices = salesInvoiceService.findAllByDateBetween(startDateTime, endDateTime);
            } else if (customerId != null) {
                // Get all invoices
                invoices = salesInvoiceService.findAll();

                // Filter invoices by customer number
                final String custId = customerId;
                invoices = invoices.stream()
                        .filter(invoice -> invoice.getCustomerNo() != null && custId.equals(invoice.getCustomerNo()))
                        .collect(Collectors.toList());
            } else if (statusId != null) {
                // Get invoices for the status
                invoices = salesInvoiceService.findAllByPaymentStatusId(statusId);
            } else {
                // Get all invoices
                invoices = salesInvoiceService.findAll();
            }

            // Apply additional filters if needed
            if (invoices != null) {
                if (customerId != null && (startDate != null || statusId != null || cashierUserName != null || routeNo != null)) {
                    final String custId = customerId;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getCustomerNo() != null && custId.equals(invoice.getCustomerNo()))
                            .collect(Collectors.toList());
                }

                if (statusId != null && (startDate != null || customerId != null || cashierUserName != null || routeNo != null)) {
                    final String statId = statusId;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getStatus() != null && statId.equals(invoice.getStatus().getId()))
                            .collect(Collectors.toList());
                }

                if (cashierUserName != null && (startDate != null || customerId != null || statusId != null || routeNo != null)) {
                    final String cashUser = cashierUserName;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getCashierUserName() != null &&
                                    cashUser.equals(invoice.getCashierUserName()))
                            .collect(Collectors.toList());
                }

                if (routeNo != null && (startDate != null || customerId != null || statusId != null || cashierUserName != null)) {
                    final String route = routeNo;
                    invoices = invoices.stream()
                            .filter(invoice -> invoice.getRouteNo() != null && route.equals(invoice.getRouteNo()))
                            .collect(Collectors.toList());
                }
            }

            // Generate PDF with the filtered data
            in = salesInvoiceService.generatePdfReport(invoices);

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Disposition", "attachment; filename=sales_invoices.pdf");

            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(new InputStreamResource(in));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
