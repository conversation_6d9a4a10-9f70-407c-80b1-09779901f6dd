package lk.sout.business.dashboard.controller;

import lk.sout.core.entity.DailyStatistics;
import lk.sout.core.service.StatisticsService;
import lk.sout.business.dashboard.service.DailyStatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * REST controller for dashboard operations
 * Created by <PERSON><PERSON><PERSON> Weera<PERSON> on 7/17/2023
 */

@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DashboardController.class);

    @Autowired
    private DailyStatService dailyStatService;

    @Autowired
    private StatisticsService statisticsService;

    /**
     * Get today's dashboard summary
     * @return Dashboard summary for today
     */
    @RequestMapping(value = "/today", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getTodayDashboard() {
        try {
            Map<String, Object> summary = dailyStatService.getTodayDashboardSummary();
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            LOGGER.error("Error getting today's dashboard: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get dashboard summary for a specific date
     * @param date The date to get dashboard for (format: yyyy-MM-dd)
     * @return Dashboard summary for the specified date
     */
    @RequestMapping(value = "/date", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getDashboardByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            Map<String, Object> summary = dailyStatService.getDashboardSummaryForDate(date);
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            LOGGER.error("Error getting dashboard for date {}: {}", date, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get dashboard summary for a date range
     * @param startDate Start date (format: yyyy-MM-dd)
     * @param endDate End date (format: yyyy-MM-dd)
     * @return Aggregated dashboard summary for the date range
     */
    @RequestMapping(value = "/dateRange", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getDashboardByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            if (startDate.isAfter(endDate)) {
                return ResponseEntity.badRequest().body("Start date cannot be after end date");
            }

            Map<String, Object> summary = dailyStatService.getDashboardSummaryForDateRange(startDate, endDate);
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            LOGGER.error("Error getting dashboard for date range {} to {}: {}", startDate, endDate, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get dashboard summary for the current week
     * @return Dashboard summary for the current week
     */
    @RequestMapping(value = "/thisWeek", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getThisWeekDashboard() {
        try {
            LocalDate today = LocalDate.now();
            LocalDate startOfWeek = today.minusDays(today.getDayOfWeek().getValue() - 1);

            Map<String, Object> summary = dailyStatService.getDashboardSummaryForDateRange(startOfWeek, today);
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            LOGGER.error("Error getting this week's dashboard: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get dashboard summary for the current month
     * @return Dashboard summary for the current month
     */
    @RequestMapping(value = "/thisMonth", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getThisMonthDashboard() {
        try {
            LocalDate today = LocalDate.now();
            LocalDate startOfMonth = today.withDayOfMonth(1);

            Map<String, Object> summary = dailyStatService.getDashboardSummaryForDateRange(startOfMonth, today);
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            LOGGER.error("Error getting this month's dashboard: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get dashboard summary for the last 7 days
     * @return Dashboard summary for the last 7 days
     */
    @RequestMapping(value = "/last7Days", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getLast7DaysDashboard() {
        try {
            LocalDate today = LocalDate.now();
            LocalDate sevenDaysAgo = today.minusDays(6); // Including today makes it 7 days

            Map<String, Object> summary = dailyStatService.getDashboardSummaryForDateRange(sevenDaysAgo, today);
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            LOGGER.error("Error getting last 7 days dashboard: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get dashboard summary for the last 30 days
     * @return Dashboard summary for the last 30 days
     */
    @RequestMapping(value = "/last30Days", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getLast30DaysDashboard() {
        try {
            LocalDate today = LocalDate.now();
            LocalDate thirtyDaysAgo = today.minusDays(29); // Including today makes it 30 days

            Map<String, Object> summary = dailyStatService.getDashboardSummaryForDateRange(thirtyDaysAgo, today);
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            LOGGER.error("Error getting last 30 days dashboard: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get historical chart data for a date range
     * @param startDate Start date (format: yyyy-MM-dd)
     * @param endDate End date (format: yyyy-MM-dd)
     * @return List of daily statistics for chart visualization
     */
    @RequestMapping(value = "/chartData", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getChartData(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            if (startDate.isAfter(endDate)) {
                return ResponseEntity.badRequest().body("Start date cannot be after end date");
            }

            // Limit to maximum 90 days to prevent performance issues
            if (startDate.isBefore(endDate.minusDays(90))) {
                startDate = endDate.minusDays(90);
                LOGGER.warn("Date range limited to 90 days for performance. Adjusted start date to: {}", startDate);
            }

            List<DailyStatistics> chartData = statisticsService.findByDateRange(startDate, endDate);
            LOGGER.info("Retrieved {} daily statistics records for chart data from {} to {}",
                       chartData.size(), startDate, endDate);

            return ResponseEntity.ok(chartData);
        } catch (Exception e) {
            LOGGER.error("Error getting chart data for date range {} to {}: {}", startDate, endDate, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
