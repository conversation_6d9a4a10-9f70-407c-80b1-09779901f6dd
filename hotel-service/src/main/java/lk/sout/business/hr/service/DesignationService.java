package lk.sout.business.hr.service;

import lk.sout.business.hr.entity.Designation;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 6/21/2018
 */
public interface DesignationService {

    boolean save(Designation designation);


    Iterable<Designation> findAll(Pageable pageable);

    List<Designation> searchDesignationNameLike(String name);


    List<Designation> findAll();

    Designation findById(String id);

    boolean findByDesignationName(String name);
}
