package lk.sout.business.hr.controller;

import lk.sout.business.hr.entity.LeaveRequest;
import lk.sout.business.hr.service.LeaveRequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/leaveRequest")
public class LeaveRequestController {

    @Autowired
    LeaveRequestService leaveRequestService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody

    private ResponseEntity<?> save(@RequestBody LeaveRequest leaveRequest){
        try{
            return  ResponseEntity.ok(leaveRequestService.save(leaveRequest));
        }catch (Exception e){
            return  ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }

    @RequestMapping(value = "/findAll" ,method = RequestMethod.GET)
    private ResponseEntity<?>findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(leaveRequestService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDatesAndEmployee" ,method = RequestMethod.GET)
    private ResponseEntity<?>findByDatesAndEmployee(@RequestParam("from") Date from, @RequestParam("to") Date to, @RequestParam("employee") String employee) {
        try {
            return ResponseEntity.ok(leaveRequestService.findByDatesAndEmployee(from,to,employee));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> search(@RequestParam("any") String any) {
        try {
            return ResponseEntity.ok(leaveRequestService.findByEpf(any));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


}
