package lk.sout.business.hr.repository;

import lk.sout.business.hr.entity.Leave;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LeaveRepository extends MongoRepository<Leave, String> {

    Page<Leave> findAll(Pageable pageable);

    Leave findByLeaveType(String s);

}
