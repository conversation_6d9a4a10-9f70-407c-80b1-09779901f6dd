package lk.sout.business.hr.service.impl;

import lk.sout.business.hr.entity.Designation;
import lk.sout.business.hr.repository.DesignationRepository;
import lk.sout.business.hr.service.DesignationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 6/18/2018
 */
@Service
public class DesignationServiceImpl implements DesignationService {

    final static Logger LOGGER = LoggerFactory.getLogger(Designation.class);

    @Autowired
    DesignationRepository designationRepository;

    @Override
    public boolean save(Designation designation) {
        try {
            designationRepository.save(designation);
            return true;
        } catch (Exception ex) {
            LOGGER.error("designation saving failed" + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Designation> findAll(Pageable pageable) {
        try {
            return designationRepository.findAll(pageable);

        } catch (Exception ex) {
            LOGGER.error("designation  failed" + ex.getMessage());
            return null;
        }
    }




    @Override
    public List<Designation> searchDesignationNameLike(String name) {
        try {
            return designationRepository.findAllByDesignationNameLikeIgnoreCase(name);
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Designation> findAll() {
        try {
            return designationRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("designation  failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public Designation findById(String id) {
        try {
            return designationRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("designation  failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean findByDesignationName(String name) {
        try {
            Designation designation = designationRepository.findByDesignationName(name);
            if (designation != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Designation Failed: " + ex.getMessage());
            return false;
        }
    }
}

