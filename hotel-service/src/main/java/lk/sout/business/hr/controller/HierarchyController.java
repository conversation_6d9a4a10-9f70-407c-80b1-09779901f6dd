package lk.sout.business.hr.controller;


import lk.sout.business.hr.entity.Hierarchy;
import lk.sout.business.hr.service.HierarchyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/hierarchy")
public class HierarchyController {
    @Autowired
    HierarchyService hierarchyService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody

    private ResponseEntity<?> save(@RequestBody Hierarchy hierarchy) {
        try {
            return ResponseEntity.ok(hierarchyService.save(hierarchy));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll() {
        try {
            return ResponseEntity.ok(hierarchyService.findAll());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> search(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(hierarchyService.findByReportingManager(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPagination" ,method = RequestMethod.GET)
    private ResponseEntity<?>findAllPagination(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(hierarchyService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}

