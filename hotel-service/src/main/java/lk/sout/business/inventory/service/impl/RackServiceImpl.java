package lk.sout.business.inventory.service.impl;

import lk.sout.business.inventory.entity.Rack;
import lk.sout.business.inventory.repository.RackRepository;
import lk.sout.business.inventory.service.RackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


@Service
public class RackServiceImpl implements RackService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RackService.class);

    @Autowired
    RackRepository rackRepository;

    public boolean save(Rack rack) {
        try {
            rackRepository.save(rack);
            LOGGER.info("Rack saved. " + rack.getRackNo());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Rack Failed. " + rack.getRackNo());
            return false;
        }
    }

    public boolean remove(Rack rack) {
        try {
            rackRepository.delete(rack);
            LOGGER.info("Rack removed. " + rack.getRackNo());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Rackd Failed. " + rack.getRackNo());
            return false;
        }
    }

    public Iterable<Rack> findAll(Integer page, Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("id").descending());
            return rackRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("designation  failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Rack> findAllRacks() {
        try {
            return rackRepository.findAll();
        }catch (Exception e){
            LOGGER.error("designation  failed" + e.getMessage());
            return null;

        }
    }

    public Rack findOne(String id) {
        try {
            Optional<Rack> rack = rackRepository.findById(id);
            return rack.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving a rack failed");
            return null;
        }
    }

    @Override
    public List<Rack> findByRackNo(String key) {
        try {
            return rackRepository.findByRackNoLikeIgnoreCase(key);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a rack failed");
            return null;
        }
    }



}
