package lk.sout.business.inventory.controller;

import lk.sout.business.inventory.entity.ItemType;
import lk.sout.business.inventory.service.ItemTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/itemType")
public class ItemTypeController {

    @Autowired
    ItemTypeService itemTypeService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody ItemType itemType) {
        try {
            return ResponseEntity.ok(itemTypeService.save(itemType));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize")String pageSize) {
        try {
            return ResponseEntity.ok(itemTypeService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllForSelect", method = RequestMethod.GET)
    private ResponseEntity<?> findAllForSelect() {
        try {
            return ResponseEntity.ok(itemTypeService.findAll());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> delete(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(itemTypeService.remove(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET)
    private ResponseEntity<?> search(@RequestParam("any") String name) {
        try {
            return ResponseEntity.ok(itemTypeService.findByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
