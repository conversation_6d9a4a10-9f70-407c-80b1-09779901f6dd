package lk.sout.business.inventory.service;

import lk.sout.business.inventory.entity.SubCategory;

import java.util.List;

public interface SubCategoryService {

    boolean save(SubCategory subCategory);

    Iterable<SubCategory> findAll(Integer page, Integer pageSize);

    boolean remove(String id);

    List<SubCategory> findByName(String name);

    List<SubCategory> findByNameAndParent(String key, String catName);

    List<SubCategory> findByCategoryId(String categoryId);

    SubCategory findById(String id);

    int getCount();
}
