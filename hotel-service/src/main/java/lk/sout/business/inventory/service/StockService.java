package lk.sout.business.inventory.service;

import lk.sout.core.entity.Response;
import lk.sout.business.inventory.entity.*;
import lk.sout.business.trade.entity.PurchaseInvoiceRecord;
import lk.sout.business.trade.entity.SalesInvoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 2/4/2020
 */

public interface StockService {

    Response save(Stock stock, String type, String ref);

    boolean basicSave(Stock stock);

    Response createByPurchaseInvRecs(List<PurchaseInvoiceRecord> piRecords, boolean manual);

    Iterable<Stock> findAllByWarehouse(Pageable pageable, int warehouseCode);

    List<Stock> findReorderListByWarehouse(int warehouseCode, Double threshold);

    List<Stock> findReorderList(Double threshold, String catCode, String brandCode);

    List<Stock> findBySupplier(String supplierCode, Double threshold);

    List<Stock> findByBarcodeAndWarehouse(String barcode, int warehouseCode);

    List<String[]> findPricesByBarcodeAndWarehouse(String barcode, int warehouseCode);

    List<Stock> findByWarehouseAndBarcodeLike(String barcode);

    List<Stock> findByWarehouseAndItemNameLike(String itemName);

    List<Stock> findByItemCode(String itemCode);

    Stock findByItemCodeAndWarehouseAndPrice(String itemCode, int warehouseCode, double price);

    Stock findMainStockByItemCode(String itemCode, double price);

    Response deductFromStock(SalesInvoice salesInvoice, int warehouseCode);

    Response adjust(String stockId, Double actualValue, String remark);

    Response transferStock(TransferStock transferStock);

    List<Stock> getDeadStockList();

    Page<Stock> findAllStocks(Pageable pageable);

    List<Stock> findAllByBarcodeLike(String code);

    List<Stock> findAllByNameLike(String name);

    List<Stock> findStockByItemCategoryAndWh(String id, int whCode);

    List<Stock> findStockByBrandAndWh(String id, int whCode);

    boolean topUpStock(String itemCode, int warehouseCode, Double qty, double price, boolean b);

    List<StockSummary> findStockSummary();

    boolean addStockMovement(String type, String itemCode, Stock stock);

    List<StockMovement> findStockMovementByItemAndDateBetween(String barcode, int whCode,
                                                              LocalDate fromDate, LocalDate toDate);

    byte[] generateDetailReport(String groupBy);

    List<Stock> findStockRecordsByItemAndWarehouse(String itemCode, int warehouseCode);

    Stock findById(String stockId);

}
