/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package lk.sout.business.inventory.service.impl;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import lk.sout.core.entity.Action;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.UserService;
import lk.sout.business.inventory.entity.*;
import lk.sout.business.inventory.repository.*;
import lk.sout.business.inventory.service.ItemService;
import lk.sout.business.inventory.service.StockService;
import lk.sout.business.inventory.service.WarehouseService;
import lk.sout.business.trade.entity.PurchaseInvoiceRecord;
import lk.sout.business.trade.entity.SalesInvoice;
import lk.sout.business.trade.entity.SalesInvoiceRecord;
import lk.sout.business.trade.repository.SalesInvoiceRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.ByteArrayOutputStream;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
public class StockServiceImpl implements StockService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockServiceImpl.class);

    @Autowired
    Response response;

    @Autowired
    StockRepository stockRepository;

    @Autowired
    UserService userService;

    @Autowired
    TransferStockRepository transferStockRepository;

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    ItemService itemService;

    @Autowired
    Stock stock;

    @Autowired
    Item item;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ActionService actionService;

    @Autowired
    Action action;

    @Autowired
    WarehouseService warehouseService;

    @Autowired
    SalesInvoiceRecordRepository salesInvoiceRecordRepository;

    @Autowired
    CustomStockRepository customStockRepository;

    @Autowired
    StockMovement stockMovement;

    @Autowired
    StockMovementRepository stockMovementRepository;

    @Value("${sout.mainWhCode}")
    int mainWhCode;

    @Override
    public Response save(Stock stock, String type, String itemCode) {
        try {
            addStockMovement(type, itemCode, stock);
            stockRepository.save(stock);
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Successfully Saved");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Saving stock failed " + ex.getMessage());
            response.setCode(501);
            response.setSuccess(false);
            response.setMessage("Saving Failed");
            ex.printStackTrace();
            return response;
        }
    }

    @Override
    //Quantity doesn't involved, so no need of updating stock movement, just saving
    public boolean basicSave(Stock stock) {
        try {
            stockRepository.save(stock);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Basic Save Failed " + ex.getMessage());
            ex.printStackTrace();
            return false;
        }
    }

    @Transactional
    public Response createByPurchaseInvRecs(List<PurchaseInvoiceRecord> piRecords, boolean manual) {
        try {
            for (PurchaseInvoiceRecord pir : piRecords) {
                String itemCode = pir.getItemCode();

                // Fetch stock record
                Stock stock = stockRepository.findByItemCodeAndWarehouseCodeAndSellingPrice(
                        itemCode, pir.getWarehouseCode(), pir.getSellingPrice()
                );

                // Fetch item details
                Item item = itemService.findOneByItemCode(itemCode);
                if (item == null) {
                    throw new IllegalArgumentException("Item not found for code: " + itemCode);
                }

                // Update item properties if necessary
                boolean itemUpdated = false;
                if (pir.getSellingPrice() != null && pir.getSellingPrice().compareTo(item.getSellingPrice()) != 0) {
                    item.setSellingPrice(pir.getSellingPrice());
                    itemUpdated = true;
                }
                if (pir.getItemCost() != null && pir.getItemCost().compareTo(item.getItemCost()) != 0) {
                    item.setItemCost(pir.getItemCost());
                    itemUpdated = true;
                }
                if (itemUpdated) {
                    itemRepository.save(item);
                }

                // Update or create stock record
                if (stock != null) {
                    // Update existing stock
                    stock.setQuantity(stock.getQuantity() + pir.getQuantity());
                    if (item.getDeadStockLevel() != null) {
                        stock.setDeadStockLevel(item.getDeadStockLevel());
                    }
                    save(stock, "Updating Stock By Purchasing", itemCode);
                } else {
                    // Create new stock
                    Warehouse wh = warehouseService.findByCode(pir.getWarehouseCode());
                    if (wh == null) {
                        throw new IllegalArgumentException("Warehouse not found for code: " + pir.getWarehouseCode());
                    }
                    stock = new Stock();
                    stock.setQuantity(pir.getQuantity());
                    stock.setDeadStockLevel(item.getDeadStockLevel());
                    stock.setItemCode(item.getItemCode());
                    stock.setBarcode(item.getBarcode());
                    stock.setItemName(item.getItemName());
                    stock.setSellingPrice(pir.getSellingPrice());
                    stock.setItemCost(pir.getItemCost());
                    stock.setCategoryCode(item.getItemCategory() != null ? item.getItemCategory().getCode() : null);
                    stock.setCategoryName(item.getItemCategory() != null ? item.getItemCategory().getCategoryName() : null);
                    stock.setModelCode(item.getModel() != null ? item.getModel().getCode() : null);
                    stock.setModelName(item.getModel() != null ? item.getModel().getName() : null);
                    stock.setBrandCode(item.getBrand() != null ? item.getBrand().getCode() : null);
                    stock.setBrandName(item.getBrand() != null ? item.getBrand().getName() : null);
                    stock.setSupplierCode(item.getSupplier() != null ? item.getSupplier().getSupplierNo() : null);
                    stock.setSupplierName(item.getSupplier() != null ? item.getSupplier().getName() : null);
                    stock.setWarehouseCode(wh.getCode());
                    stock.setWarehouseName(wh.getName());
                    stock.setActive(true);
                    save(stock, "Creating Stock By Purchasing", itemCode);
                }
            }
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Item Added Successfully");
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Adding Item Failed: " + ex.getMessage(), ex);
            response.setSuccess(false);
            response.setCode(501);
            response.setMessage("Adding Item Failed");
            response.setData(ex.getMessage());
        }
        return response;
    }


    @Override
    public Iterable<Stock> findAllByWarehouse(Pageable pageable, int warehouseCode) {
        try {
            Iterable<Stock> subStocks = stockRepository.findAllByWarehouseCode(pageable, warehouseCode);
            return subStocks;
        } catch (Exception ex) {
            LOGGER.error("findAllByWarehouse failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findByBarcodeAndWarehouse(String barcode, int warehouseCode) {
        return stockRepository.findByBarcodeAndWarehouseCode(barcode, warehouseCode);
    }

    @Override
    public List<String[]> findPricesByBarcodeAndWarehouse(String barcode, int warehouseCode) {
        try {
            List<Stock> list = stockRepository.findByBarcodeAndWarehouseCode(barcode, warehouseCode);
            return list.stream()
                    .filter(stock -> stock.getQuantity() != null)
                    .sorted((s1, s2) -> s2.getQuantity().compareTo(s1.getQuantity()))
                    .map(stock -> new String[]{stock.getQuantity().toString(), stock.getSellingPrice().toString()})
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            LOGGER.error("findPricesByBarcodeAndWarehouse issue : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findByWarehouseAndBarcodeLike(String barcode) {
        return stockRepository.findTop15ByWarehouseCodeAndBarcodeLikeIgnoreCase(mainWhCode, barcode);
    }

    @Override
    public List<Stock> findByWarehouseAndItemNameLike(String itemName) {
        return stockRepository.findTop15ByWarehouseCodeAndItemNameLikeIgnoreCase(mainWhCode, itemName);
    }

    @Override
    public Stock findByItemCodeAndWarehouseAndPrice(String itemCode, int warehouseCode, double price) {
        try {
            return stockRepository.findByItemCodeAndWarehouseCodeAndSellingPrice(itemCode, warehouseCode, price);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Stock findMainStockByItemCode(String itemCode, double price) {
        try {
            return stockRepository.findByItemCodeAndWarehouseCodeAndSellingPrice(itemCode, mainWhCode, price);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Stock> findByItemCode(String itemCode) {
        return stockRepository.findByItemCode(itemCode);
    }

    public boolean topUpStock(String itemCode, int warehouseCode, Double qty, double sellingPrice, boolean returnRecord) {
        boolean response = false;
        Double quantity = 0.0;
        Stock stock1 = findByItemCodeAndWarehouseAndPrice(itemCode, warehouseCode, sellingPrice);

        if (stock1 != null) {
            quantity = stock1.getQuantity();
        } else {
            quantity = 0.0;
            qty = 0.0;
        }

        if (qty >= 0 || returnRecord) {
            stock1.setQuantity(quantity + qty);
            save(stock1, returnRecord ? "Updating Stock by Returning Items" : "Updating Stock by Manual Adding", itemCode);
            response = true;
        } else {
            response = false;
        }
        return response;
    }

    @Override
    public List<StockSummary> findStockSummary() {
        try {
            return customStockRepository.findStockSummary();
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            return null;
        }
    }

    @Override
    public Response deductFromStock(SalesInvoice salesInvoice, int warehouseCode) {
        Response response = new Response();

        try {
            for (SalesInvoiceRecord salesInvoiceRecord : salesInvoice.getSalesInvoiceRecords()) {
                Item item = itemRepository.findByItemCode(salesInvoiceRecord.getItemCode());
                salesInvoiceRecord.setDate(LocalDate.now());
                salesInvoiceRecord.setInvoiceNo(salesInvoice.getInvoiceNo());
                salesInvoiceRecord.setWarehouseCode(warehouseCode);
                salesInvoiceRecord.setItemCode(item.getItemCode());

                if (item.isManageStock()) {
                    // Use stockId instead of composite key lookup
                    if (salesInvoiceRecord.getStockId() == null || salesInvoiceRecord.getStockId().isEmpty()) {
                        response.setSuccess(false);
                        response.setCode(400);
                        response.setMessage(String.format("Stock ID is required for item: %s (%s)",
                                          item.getItemName(), item.getItemCode()));
                        return response;
                    }

                    Stock stock = stockRepository.findById(salesInvoiceRecord.getStockId()).orElse(null);

                    if (stock == null) {
                        response.setSuccess(false);
                        response.setCode(400);
                        response.setMessage(String.format("Stock record not found for item: %s (%s) with Stock ID: %s",
                                          item.getItemName(), item.getItemCode(), salesInvoiceRecord.getStockId()));
                        return response;
                    }

                    salesInvoiceRecord.setStockCount(stock.getQuantity());

                    // We already validated sufficient stock, but double-check for safety
                    if (stock.getQuantity().compareTo(salesInvoiceRecord.getQuantity()) >= 0) {
                        stock.setQuantity(stock.getQuantity() - salesInvoiceRecord.getQuantity());
                        save(stock, "Updating Stock by Sales Invoice Deduction", item.getItemCode());
                    } else {
                        response.setSuccess(false);
                        response.setCode(400);
                        response.setMessage(String.format("Insufficient stock for item: %s (%s). Required: %.2f, Available: %.2f",
                                          item.getItemName(), item.getBarcode(), salesInvoiceRecord.getQuantity(), stock.getQuantity()));
                        return response;
                    }
                }
                salesInvoiceRecordRepository.save(salesInvoiceRecord);
            }

            response.setSuccess(true);
            response.setCode(200);
            response.setMessage("Stock deducted successfully");
            return response;

        } catch (Exception ex) {
            LOGGER.error("deductFromStock failed: {}", ex.getMessage(), ex);
            response.setSuccess(false);
            response.setCode(500);
            response.setMessage("Stock deduction error: " + ex.getMessage());
            return response;
        }
    }

    @Transactional
    public Response adjust(String stockId, Double actualValue, String remark) {
        try {
            Double systemValue;

            Stock stock = stockRepository.findById(stockId).get();
            systemValue = stock.getQuantity() != null ? stock.getQuantity() : 0.0;
            stock.setQuantity(actualValue);

            MetaData stockAdjustment = metaDataService.searchMetaData("Adjust Stock", "Action");
            action = new Action();
            action.setType(stockAdjustment.getValue());
            action.setReference(stock.getBarcode());
            action.setReference2(stock.getItemName());
            if (actualValue.compareTo(systemValue) > 0) {
                action.setOperator("+");
            } else {
                action.setOperator("-");
            }
            action.setChange(String.valueOf(actualValue - systemValue));
            action.setRemark(remark);

            actionService.save(action);
            save(stock, "Updating Stock by Count Adjustment", stock.getItemCode());
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Successfully Saved");
            return response;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            response.setCode(501);
            response.setData(e.getMessage());
            response.setSuccess(false);
            response.setMessage("Saving Failed");
            return response;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response transferStock(TransferStock transferStock) {
        try {
            Warehouse targetWarehouse = warehouseService.findByCode(transferStock.getTargetWarehouseCode());
            Warehouse sourceWarehouse = warehouseService.findByCode(transferStock.getSourceWarehouseCode());

            Double transferringQty = transferStock.getTransferQty();
            if (transferringQty == null || transferringQty <= 0) {
                throw new IllegalArgumentException("Invalid transfer quantity.");
            }

            Stock sourceRec = stockRepository.findByItemCodeAndWarehouseCodeAndSellingPrice(
                    transferStock.getItemCode(), sourceWarehouse.getCode(), transferStock.getSellingPrice());
            if (sourceRec == null) {
                throw new IllegalArgumentException("Source stock record does not exist for the given item.");
            }

            Stock targetRec = stockRepository.findByItemCodeAndWarehouseCodeAndSellingPrice(
                    transferStock.getItemCode(), targetWarehouse.getCode(), transferStock.getSellingPrice());

            if (sourceRec.getQuantity().compareTo(transferringQty) >= 0) {
                if (targetRec == null) {
                    Item item = itemService.findOneByItemCode(transferStock.getItemCode());
                    stock.setId(null);
                    stock.setActive(true);
                    stock.setBarcode(item.getBarcode());
                    stock.setItemCode(transferStock.getItemCode());
                    stock.setItemName(item.getItemName());
                    stock.setQuantity(transferringQty);
                    stock.setSellingPrice(sourceRec.getSellingPrice());
                    stock.setItemCost(sourceRec.getItemCost());
                    stock.setDeadStockLevel(item.getDeadStockLevel());
                    stock.setWarehouseCode(targetWarehouse.getCode());
                    stock.setWarehouseName(targetWarehouse.getName());
                    stock.setCategoryCode(item.getItemCategory() != null ? item.getItemCategory().getCode() : null);
                    stock.setCategoryName(item.getItemCategory() != null ? item.getItemCategory().getCategoryName() : null);
                    stock.setModelCode(item.getModel() != null ? item.getModel().getCode() : null);
                    stock.setModelName(item.getModel() != null ? item.getModel().getName() : null);
                    stock.setBrandCode(item.getBrand() != null ? item.getBrand().getCode() : null);
                    stock.setBrandName(item.getBrand() != null ? item.getBrand().getName() : null);
                    stock.setSupplierCode(item.getSupplier() != null ? item.getSupplier().getSupplierNo() : null);
                    stock.setSupplierName(item.getSupplier() != null ? item.getSupplier().getName() : null);
                    save(stock, "Creating Stock by Transfer In", transferStock.getItemCode());
                } else {
                    targetRec.setQuantity(targetRec.getQuantity() + transferringQty);
                    save(targetRec, "Updating Stock by Transfer In", transferStock.getItemCode());
                }
                sourceRec.setQuantity(sourceRec.getQuantity() - transferringQty);
                save(sourceRec, "Updating Stock by Transfer Out", transferStock.getItemCode());
            } else {
                throw new IllegalArgumentException("Insufficient stock quantity in source warehouse.");
            }

            transferStockRepository.save(transferStock);
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Successfully Saved");
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Stock Transfer failed " + ex.getMessage());
            response.setCode(501);
            response.setSuccess(false);
            response.setMessage("Stock Transfer Failed");
            return response;
        }
    }


    @Override
    public List<Stock> getDeadStockList() {
        List<Stock> deadStock = new ArrayList<>();
        try {
            List<Stock> stock = stockRepository.findAll();
            stock.forEach(stk -> {
                if (Double.compare(stk.getQuantity(), stk.getDeadStockLevel()) <= 0) {
                    deadStock.add(stk);
                }
            });
            return deadStock;
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("getDeadStockList failed");
            return deadStock;
        }
    }

    @Override
    public Page<Stock> findAllStocks(Pageable pageable) {
        try {
            return stockRepository.findAllByWarehouseCode(pageable, mainWhCode);
        } catch (Exception ex) {
            LOGGER.error("Retrieving Dead Stock failed");
            return null;
        }
    }

    @Override
    public List<Stock> findAllByBarcodeLike(String code) {
        try {
            return stockRepository.findAllByBarcodeLikeIgnoreCase(code);
        } catch (Exception e) {
            LOGGER.error("Search item Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findAllByNameLike(String name) {
        try {
            List<Stock> items = stockRepository.findAllByItemNameLikeIgnoreCase(name);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findStockByItemCategoryAndWh(String code, int whCode) {
        try {
            List<Stock> stocks = stockRepository.findAllByCategoryCodeAndWarehouseCode(code, whCode);
            return stocks;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Stock> findStockByBrandAndWh(String code, int whCode) {
        try {
            List<Stock> stocks = stockRepository.findAllByBrandCodeAndWarehouseCode(code, whCode);
            return stocks;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Stock> findReorderListByWarehouse(int warehouseCode, Double threshold) {
        try {
            return customStockRepository.findReorderListByWarehouse(warehouseCode, threshold);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findReorderList(Double threshold, String catCode, String brandCode) {
        try {
            if (threshold > 0 && !catCode.isEmpty() && brandCode.isEmpty()) {
                return customStockRepository.findReorderListForItemCategory(threshold, catCode);
            } else if (threshold > 0 && catCode.isEmpty() && !brandCode.isEmpty()) {
                return customStockRepository.findReorderListForItemBrand(threshold, brandCode);
            } else if (threshold > 0 && !catCode.isEmpty() && !brandCode.isEmpty()) {
                return customStockRepository.findReorderListForCatAndBrand(threshold, catCode, brandCode);
            } else {
                return customStockRepository.findReorderList(threshold);
            }
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findBySupplier(String supplierCode, Double threshold) {
        try {
            if (threshold == null || threshold <= 0) {
                threshold = 0.0; // Default threshold if not provided
            }
            return customStockRepository.findReorderListBySupplier(threshold, supplierCode);
        } catch (Exception ex) {
            LOGGER.error("Error finding stocks by supplier: {}", ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean addStockMovement(String type, String itemCode, Stock newStock) {
        try {
            Stock oldStock = stockRepository.findByItemCodeAndWarehouseCodeAndSellingPrice(
                    newStock.getItemCode(), newStock.getWarehouseCode(), newStock.getSellingPrice());
            stockMovement.setId(null);
            if (null != oldStock) {
                stockMovement.setQuantity(newStock.getQuantity() - oldStock.getQuantity());
                stockMovement.setStockCountBefore(oldStock.getQuantity());
                stockMovement.setStockCountAfter(newStock.getQuantity());
            } else {
                stockMovement.setQuantity(newStock.getQuantity());
                stockMovement.setStockCountBefore(0.0);
                stockMovement.setStockCountAfter(newStock.getQuantity());
            }
            stockMovement.setType(type);
            stockMovement.itemCode(itemCode);
            stockMovement.setBarcode(newStock.getBarcode());
            stockMovement.setItemName(newStock.getItemName());
            stockMovement.setWhCode(newStock.getWarehouseCode());
            stockMovement.setDateTime(LocalDateTime.now());
            stockMovementRepository.save(stockMovement);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Find All  Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<StockMovement> findStockMovementByItemAndDateBetween(String barcode, int whCode,
                                                                     LocalDate fromDate, LocalDate toDate) {
        try {
            return stockMovementRepository.findByBarcodeAndWhCodeAndDateTimeBetween(barcode, whCode,
                    fromDate.atStartOfDay(), toDate.plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find Stock Movement  Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public byte[] generateDetailReport(String groupBy) {
        try {
            // Log the groupBy parameter
            LOGGER.info("Generating stock report with groupBy: {}", groupBy);

            // Get all warehouses
            List<Warehouse> warehouses = warehouseService.findAll();
            if (warehouses == null) {
                warehouses = new ArrayList<>();
                LOGGER.warn("No warehouses found, using empty list");
            }

            // Get global totals
            StockTotals globalTotals = customStockRepository.calculateTotalValues();
            if (globalTotals == null) {
                globalTotals = new StockTotals();
                globalTotals.setTotalSalesValue(0.0);
                globalTotals.setTotalCostValue(0.0);
                LOGGER.warn("Failed to fetch global totals, using defaults");
            }

            // Create PDF
            Document document = new Document(PageSize.A4);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter.getInstance(document, baos);
            document.open();

            // Add title
            Font titleFont = new Font(Font.FontFamily.HELVETICA, 16, Font.BOLD);
            Paragraph title = new Paragraph("STOCK REPORT", titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(20);
            document.add(title);

            // Add date
            Font normalFont = new Font(Font.FontFamily.HELVETICA, 8, Font.NORMAL);
            Paragraph datePara = new Paragraph("Generated on: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), normalFont);
            datePara.setAlignment(Element.ALIGN_RIGHT);
            datePara.setSpacingAfter(20);
            document.add(datePara);

            // Summary of warehouses from findStockSummary
            List<StockSummary> summaries = customStockRepository.findStockSummary();
            if (summaries != null && !summaries.isEmpty()) {
                // Create warehouse summaries table
                PdfPTable table = new PdfPTable(5);
                table.setWidthPercentage(100);
                table.setSpacingBefore(10f);
                table.setSpacingAfter(10f);

                // Add table headers
                Font headerFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
                Stream.of("Warehouse", "No. of Items", "Quantity", "Price Value (LKR)", "Cost Value (LKR)")
                        .forEach(columnTitle -> {
                            PdfPCell header = new PdfPCell();
                            header.setBackgroundColor(BaseColor.LIGHT_GRAY);
                            header.setBorderWidth(1);
                            header.setPhrase(new Phrase(columnTitle, headerFont));
                            header.setHorizontalAlignment(Element.ALIGN_CENTER);
                            header.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            table.addCell(header);
                        });

                // Add data rows
                DecimalFormat df = new DecimalFormat("#,##0.00");
                for (StockSummary summary : summaries) {
                    table.addCell(new Phrase(summary.getWarehouseName(), normalFont));
                    table.addCell(new Phrase(df.format(summary.getNoOfItems()), normalFont));
                    table.addCell(new Phrase(df.format(summary.getTotalQuantity()), normalFont));
                    table.addCell(new Phrase(df.format(summary.getTotalPriceValue()), normalFont));
                    table.addCell(new Phrase(df.format(summary.getTotalCostValue()), normalFont));
                }

                document.add(table);
            }

            // Track grand totals
            double grandTotalSalesValue = 0;
            double grandTotalCostValue = 0;
            int totalItems = 0;
            double totalQuantity = 0;

            Font warehouseFont = new Font(Font.FontFamily.HELVETICA, 10, Font.BOLD);
            Font subTotalFont = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD);

            // Determine how to fetch and group stock items based on groupBy parameter
            List<Stock> allStockItems;
            String groupByTitle = "";

            if (groupBy != null && !groupBy.isEmpty()) {
                // Use the appropriate grouping method based on the groupBy parameter
                switch (groupBy.toLowerCase()) {
                    case "category":
                        allStockItems = customStockRepository.findStockDetailsGroupedByCategory();
                        groupByTitle = "Grouped by Category";
                        break;
                    case "brand":
                        allStockItems = customStockRepository.findStockDetailsGroupedByBrand();
                        groupByTitle = "Grouped by Brand";
                        break;
                    case "model":
                        allStockItems = customStockRepository.findStockDetailsGroupedByModel();
                        groupByTitle = "Grouped by Model";
                        break;
                    case "supplier":
                        allStockItems = customStockRepository.findStockDetailsGroupedBySupplier();
                        groupByTitle = "Grouped by Supplier";
                        break;
                    default:
                        // Default to no grouping
                        allStockItems = customStockRepository.findStockDetailsForReport();
                        break;
                }

                // Add grouping title to the report
                if (!groupByTitle.isEmpty()) {
                    Paragraph groupingHeader = new Paragraph(groupByTitle, titleFont);
                    groupingHeader.setAlignment(Element.ALIGN_CENTER);
                    groupingHeader.setSpacingAfter(10);
                    document.add(groupingHeader);
                }

                // When using grouping, we'll display all items together rather than by warehouse
                if (allStockItems != null && !allStockItems.isEmpty()) {
                    // Create table for all items
                    PdfPTable detailsTable = new PdfPTable(8); // Added an extra column for the grouping field
                    detailsTable.setWidthPercentage(100);
                    detailsTable.setSpacingBefore(5f);
                    detailsTable.setSpacingAfter(10f);

                    // Set column widths for better readability
                    try {
                        float[] columnWidths = {2f, 4f, 1.5f, 1.5f, 1.5f, 1.5f, 2f, 2f};
                        detailsTable.setWidths(columnWidths);
                    } catch (DocumentException e) {
                        LOGGER.warn("Failed to set column widths: " + e.getMessage());
                    }

                    // Add table headers
                    Font headerFont = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD);

                    // Add the grouping column header
                    String groupingHeader = "";
                    switch (groupBy.toLowerCase()) {
                        case "category":
                            groupingHeader = "Category";
                            break;
                        case "brand":
                            groupingHeader = "Brand";
                            break;
                        case "model":
                            groupingHeader = "Model";
                            break;
                        case "supplier":
                            groupingHeader = "Supplier";
                            break;
                    }

                    detailsTable.addCell(new PdfPCell(new Phrase(groupingHeader, headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    detailsTable.addCell(new PdfPCell(new Phrase("Item Name", headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    detailsTable.addCell(new PdfPCell(new Phrase("Barcode", headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    detailsTable.addCell(new PdfPCell(new Phrase("Warehouse", headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    detailsTable.addCell(new PdfPCell(new Phrase("Quantity", headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    detailsTable.addCell(new PdfPCell(new Phrase("Unit Price", headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    detailsTable.addCell(new PdfPCell(new Phrase("Total Value", headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    detailsTable.addCell(new PdfPCell(new Phrase("Total Cost", headerFont)) {{
                        setBackgroundColor(BaseColor.LIGHT_GRAY);
                        setPadding(5);
                    }});

                    // Add data rows
                    DecimalFormat df = new DecimalFormat("#,##0.00");
                    String currentGroup = "";
                    double groupTotalSales = 0;
                    double groupTotalCost = 0;
                    int groupItemCount = 0;
                    double groupTotalQuantity = 0;

                    for (Stock stock : allStockItems) {
                        // Get the grouping value
                        String groupValue = "";
                        switch (groupBy.toLowerCase()) {
                            case "category":
                                groupValue = stock.getCategoryName() != null ? stock.getCategoryName() : "Unknown";
                                break;
                            case "brand":
                                groupValue = stock.getBrandName() != null ? stock.getBrandName() : "Unknown";
                                break;
                            case "model":
                                groupValue = stock.getModelName() != null ? stock.getModelName() : "Unknown";
                                break;
                            case "supplier":
                                groupValue = stock.getSupplierName() != null ? stock.getSupplierName() : "Unknown";
                                break;
                        }

                        // Format the display value to include the code
                        String displayValue = groupValue;

                        // Check if we're starting a new group
                        if (!groupValue.equals(currentGroup) && !currentGroup.isEmpty()) {
                            // Add group subtotal row
                            // Use the group name directly
                            String currentGroupDisplay = currentGroup;
                            PdfPCell groupTotalHeader = new PdfPCell(new Phrase(currentGroupDisplay + " Total", headerFont));
                            groupTotalHeader.setColspan(6);
                            groupTotalHeader.setHorizontalAlignment(Element.ALIGN_RIGHT);
                            groupTotalHeader.setBackgroundColor(BaseColor.LIGHT_GRAY);
                            detailsTable.addCell(groupTotalHeader);

                            PdfPCell groupTotalSalesCell = new PdfPCell(new Phrase(df.format(groupTotalSales), headerFont));
                            groupTotalSalesCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                            detailsTable.addCell(groupTotalSalesCell);

                            PdfPCell groupTotalCostCell = new PdfPCell(new Phrase(df.format(groupTotalCost), headerFont));
                            groupTotalCostCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                            detailsTable.addCell(groupTotalCostCell);

                            // Reset group totals
                            groupTotalSales = 0;
                            groupTotalCost = 0;
                            groupItemCount = 0;
                            groupTotalQuantity = 0;
                        }

                        // Update current group
                        currentGroup = groupValue;

                        // Handle null values to prevent NullPointerExceptions
                        double quantity = (stock.getQuantity() != null) ? stock.getQuantity() : 0.0;
                        double sellingPrice = (stock.getSellingPrice() != null) ? stock.getSellingPrice() : 0.0;
                        double itemCost = (stock.getItemCost() != null) ? stock.getItemCost() : 0.0;

                        double totalPrice = quantity * sellingPrice;
                        double totalCost = quantity * itemCost;

                        // Add to group totals
                        groupTotalSales += totalPrice;
                        groupTotalCost += totalCost;
                        groupItemCount++;
                        groupTotalQuantity += quantity;

                        // Add to grand totals
                        grandTotalSalesValue += totalPrice;
                        grandTotalCostValue += totalCost;
                        totalItems++;
                        totalQuantity += quantity;

                        // Add row to table
                        detailsTable.addCell(new Phrase(displayValue, normalFont));
                        detailsTable.addCell(new Phrase(stock.getItemName() != null ? stock.getItemName() : "", normalFont));
                        detailsTable.addCell(new Phrase(stock.getBarcode() != null ? stock.getBarcode() : "", normalFont));
                        detailsTable.addCell(new Phrase(stock.getWarehouseName() != null ? stock.getWarehouseName() : "", normalFont));
                        detailsTable.addCell(new Phrase(df.format(quantity), normalFont));
                        detailsTable.addCell(new Phrase(df.format(sellingPrice), normalFont));
                        detailsTable.addCell(new Phrase(df.format(totalPrice), normalFont));
                        detailsTable.addCell(new Phrase(df.format(totalCost), normalFont));
                    }

                    // Add final group subtotal
                    if (!currentGroup.isEmpty()) {
                        // Use the group name directly
                        String currentGroupDisplay = currentGroup;
                        PdfPCell groupTotalHeader = new PdfPCell(new Phrase(currentGroupDisplay + " Total", headerFont));
                        groupTotalHeader.setColspan(6);
                        groupTotalHeader.setHorizontalAlignment(Element.ALIGN_RIGHT);
                        groupTotalHeader.setBackgroundColor(BaseColor.LIGHT_GRAY);
                        detailsTable.addCell(groupTotalHeader);

                        PdfPCell groupTotalSalesCell = new PdfPCell(new Phrase(df.format(groupTotalSales), headerFont));
                        groupTotalSalesCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                        detailsTable.addCell(groupTotalSalesCell);

                        PdfPCell groupTotalCostCell = new PdfPCell(new Phrase(df.format(groupTotalCost), headerFont));
                        groupTotalCostCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                        detailsTable.addCell(groupTotalCostCell);
                    }

                    // Add grand total row
                    PdfPCell grandTotalHeader = new PdfPCell(new Phrase("Grand Total", headerFont));
                    grandTotalHeader.setColspan(6);
                    grandTotalHeader.setHorizontalAlignment(Element.ALIGN_RIGHT);
                    grandTotalHeader.setBackgroundColor(BaseColor.GRAY);
                    detailsTable.addCell(grandTotalHeader);

                    PdfPCell grandTotalSalesCell = new PdfPCell(new Phrase(df.format(grandTotalSalesValue), headerFont));
                    grandTotalSalesCell.setBackgroundColor(BaseColor.GRAY);
                    detailsTable.addCell(grandTotalSalesCell);

                    PdfPCell grandTotalCostCell = new PdfPCell(new Phrase(df.format(grandTotalCostValue), headerFont));
                    grandTotalCostCell.setBackgroundColor(BaseColor.GRAY);
                    detailsTable.addCell(grandTotalCostCell);

                    document.add(detailsTable);
                }
            } else {
                // No grouping, process each warehouse as before
                for (Warehouse warehouse : warehouses) {
                    // Skip if warehouse is null
                    if (warehouse == null) {
                        LOGGER.warn("Null warehouse found, skipping");
                        continue;
                    }

                    // Get stocks for this warehouse
                    List<Stock> warehouseItems = customStockRepository.findStocksByWarehouse(warehouse.getCode());

                    if (warehouseItems == null || warehouseItems.isEmpty()) {
                        LOGGER.info("No items found for warehouse: " + warehouse.getName());
                        continue; // Skip warehouses with no items
                    }

                    LOGGER.info("Found " + warehouseItems.size() + " items for warehouse: " + warehouse.getName());

                    // Add a page break (if not the first warehouse)
                    if (grandTotalSalesValue > 0) {
                        document.newPage();
                    }

                    // Add warehouse header with more prominence
                    Paragraph warehouseHeader = new Paragraph("Warehouse: " + warehouse.getName(), warehouseFont);
                    warehouseHeader.setSpacingAfter(10f);
                    document.add(warehouseHeader);

                    // Add stock records label
                    Paragraph stockLabel = new Paragraph("Stock Records:", new Font(Font.FontFamily.HELVETICA, 9, Font.BOLD));
                    stockLabel.setSpacingAfter(5f);
                    document.add(stockLabel);

                    // Create table for this warehouse with better spacing
                    PdfPTable detailsTable = new PdfPTable(7);
                    detailsTable.setWidthPercentage(100);
                    detailsTable.setSpacingBefore(5f);
                    detailsTable.setSpacingAfter(10f);

                    // Set column widths for better readability
                    try {
                        float[] columnWidths = {2f, 5f, 1.5f, 1.5f, 1.5f, 2f, 2f};
                        detailsTable.setWidths(columnWidths);
                    } catch (DocumentException e) {
                        LOGGER.warn("Failed to set column widths: " + e.getMessage());
                    }

                    // Add table headers with enhanced formatting
                    Font headerFont = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD);
                    Stream.of("Barcode", "Item Name", "Quantity", "Unit Price", "Unit Cost", "Total Price", "Cost Value")
                            .forEach(columnTitle -> {
                                PdfPCell header = new PdfPCell();
                                header.setBackgroundColor(BaseColor.LIGHT_GRAY);
                                header.setBorderWidth(1);
                                header.setPhrase(new Phrase(columnTitle, headerFont));
                                header.setHorizontalAlignment(Element.ALIGN_CENTER);
                                header.setVerticalAlignment(Element.ALIGN_MIDDLE);
                                header.setPadding(4f);
                                detailsTable.addCell(header);
                            });

                    // Get warehouse totals using our new method
                    StockTotals warehouseTotals = customStockRepository.calculateTotalValuesByWarehouse(warehouse.getCode());

                    // Initialize warehouse totals
                    double warehouseTotalSales = warehouseTotals.getTotalSalesValue();
                    double warehouseTotalCost = warehouseTotals.getTotalCostValue();
                    int warehouseItemCount = 0;
                    double warehouseTotalQuantity = 0;

                    // Add details data rows for this warehouse
                    DecimalFormat df = new DecimalFormat("#,##0.00");
                    for (Stock stock : warehouseItems) {
                        // Handle null values to prevent NullPointerExceptions
                        double quantity = (stock.getQuantity() != null) ? stock.getQuantity() : 0.0;
                        double sellingPrice = (stock.getSellingPrice() != null) ? stock.getSellingPrice() : 0.0;
                        double itemCost = (stock.getItemCost() != null) ? stock.getItemCost() : 0.0;

                        double totalPrice = quantity * sellingPrice;
                        double totalCost = quantity * itemCost;

                        // These values are already calculated above
                        // Create cells with proper alignment and padding for better readability
                        PdfPCell barcodeCell = new PdfPCell(new Phrase(stock.getBarcode() != null ? stock.getBarcode() : "", normalFont));
                        barcodeCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        barcodeCell.setPadding(3f);
                        detailsTable.addCell(barcodeCell);

                        PdfPCell nameCell = new PdfPCell(new Phrase(stock.getItemName() != null ? stock.getItemName() : "", normalFont));
                        nameCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        nameCell.setPadding(3f);
                        detailsTable.addCell(nameCell);

                        PdfPCell qtyCell = new PdfPCell(new Phrase(df.format(quantity), normalFont));
                        qtyCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                        qtyCell.setPadding(3f);
                        detailsTable.addCell(qtyCell);

                        PdfPCell priceCell = new PdfPCell(new Phrase(df.format(sellingPrice), normalFont));
                        priceCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                        priceCell.setPadding(3f);
                        detailsTable.addCell(priceCell);

                        PdfPCell unitCostCell = new PdfPCell(new Phrase(df.format(itemCost), normalFont));
                        unitCostCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                        unitCostCell.setPadding(3f);
                        detailsTable.addCell(unitCostCell);

                        PdfPCell totalPriceCell = new PdfPCell(new Phrase(df.format(totalPrice), normalFont));
                        totalPriceCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                        totalPriceCell.setPadding(3f);
                        detailsTable.addCell(totalPriceCell);

                        PdfPCell totalCostCell = new PdfPCell(new Phrase(df.format(totalCost), normalFont));
                        totalCostCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                        totalCostCell.setPadding(3f);
                        detailsTable.addCell(totalCostCell);

                        // Update item count and quantity for this warehouse
                        warehouseItemCount++;
                        warehouseTotalQuantity += quantity;
                    }

                    // Add warehouse subtotals row with background color
                    PdfPCell totalHeader = new PdfPCell(new Phrase("Warehouse Total", subTotalFont));
                    totalHeader.setColspan(5);
                    totalHeader.setHorizontalAlignment(Element.ALIGN_RIGHT);
                    totalHeader.setBackgroundColor(BaseColor.LIGHT_GRAY);
                    detailsTable.addCell(totalHeader);

                    PdfPCell totalSalesCell = new PdfPCell(new Phrase(df.format(warehouseTotalSales), subTotalFont));
                    totalSalesCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                    detailsTable.addCell(totalSalesCell);

                    PdfPCell totalCostCell = new PdfPCell(new Phrase(df.format(warehouseTotalCost), subTotalFont));
                    totalCostCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
                    detailsTable.addCell(totalCostCell);

                    document.add(detailsTable);

                    // Add warehouse summary header
                    Paragraph summaryHeader = new Paragraph("Warehouse Summary:", new Font(Font.FontFamily.HELVETICA, 9, Font.BOLD));
                    summaryHeader.setSpacingBefore(10f);
                    summaryHeader.setSpacingAfter(5f);
                    document.add(summaryHeader);

                    // Add warehouse summary table
                    PdfPTable whSummaryTable = new PdfPTable(2);
                    whSummaryTable.setWidthPercentage(60);
                    whSummaryTable.setSpacingBefore(5f);
                    whSummaryTable.setSpacingAfter(15f);
                    whSummaryTable.setHorizontalAlignment(Element.ALIGN_RIGHT);

                    whSummaryTable.addCell(new PdfPCell(new Phrase("Total Items", subTotalFont)) {{
                        setHorizontalAlignment(Element.ALIGN_LEFT);
                        setPadding(5);
                        setBorder(Rectangle.NO_BORDER);
                    }});
                    whSummaryTable.addCell(new PdfPCell(new Phrase(String.valueOf(warehouseItemCount), subTotalFont)) {{
                        setHorizontalAlignment(Element.ALIGN_RIGHT);
                        setPadding(5);
                        setBorder(Rectangle.NO_BORDER);
                    }});

                    whSummaryTable.addCell(new PdfPCell(new Phrase("Total Quantity", subTotalFont)) {{
                        setHorizontalAlignment(Element.ALIGN_LEFT);
                        setPadding(5);
                        setBorder(Rectangle.NO_BORDER);
                    }});
                    whSummaryTable.addCell(new PdfPCell(new Phrase(df.format(warehouseTotalQuantity), subTotalFont)) {{
                        setHorizontalAlignment(Element.ALIGN_RIGHT);
                        setPadding(5);
                        setBorder(Rectangle.NO_BORDER);
                    }});

                    document.add(whSummaryTable);

                    // Update grand totals
                    grandTotalSalesValue += warehouseTotalSales;
                    grandTotalCostValue += warehouseTotalCost;
                    totalItems += warehouseItemCount;
                    totalQuantity += warehouseTotalQuantity;
                }
            }

            // Add grand totals section on a new page
            document.newPage();
            document.add(new Paragraph("STOCK REPORT SUMMARY", titleFont) {{
                setAlignment(Element.ALIGN_CENTER);
                setSpacingAfter(20);
            }});

            PdfPTable grandTotalTable = new PdfPTable(2);
            grandTotalTable.setWidthPercentage(60);
            grandTotalTable.setSpacingBefore(10f);
            grandTotalTable.setSpacingAfter(10f);
            grandTotalTable.setHorizontalAlignment(Element.ALIGN_CENTER);

            Font grandTotalFont = new Font(Font.FontFamily.HELVETICA, 8, Font.BOLD);
            DecimalFormat df = new DecimalFormat("#,##0.00");

            // Add total rows
            grandTotalTable.addCell(new PdfPCell(new Phrase("Total Items", grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_LEFT);
                setPadding(5);
            }});
            grandTotalTable.addCell(new PdfPCell(new Phrase(String.valueOf(totalItems), grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_RIGHT);
                setPadding(5);
            }});

            grandTotalTable.addCell(new PdfPCell(new Phrase("Total Quantity", grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_LEFT);
                setPadding(5);
            }});
            grandTotalTable.addCell(new PdfPCell(new Phrase(df.format(totalQuantity), grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_RIGHT);
                setPadding(5);
            }});

            grandTotalTable.addCell(new PdfPCell(new Phrase("Total Sales Value (LKR)", grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_LEFT);
                setPadding(5);
            }});
            grandTotalTable.addCell(new PdfPCell(new Phrase(df.format(grandTotalSalesValue), grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_RIGHT);
                setPadding(5);
            }});

            grandTotalTable.addCell(new PdfPCell(new Phrase("Total Cost Value (LKR)", grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_LEFT);
                setPadding(5);
            }});
            grandTotalTable.addCell(new PdfPCell(new Phrase(df.format(grandTotalCostValue), grandTotalFont)) {{
                setHorizontalAlignment(Element.ALIGN_RIGHT);
                setPadding(5);
            }});

            // Add gross profit with different background
            PdfPCell profitLabelCell = new PdfPCell(new Phrase("Gross Profit (LKR)", grandTotalFont));
            profitLabelCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            profitLabelCell.setPadding(5);
            profitLabelCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            grandTotalTable.addCell(profitLabelCell);

            PdfPCell profitValueCell = new PdfPCell(new Phrase(df.format(grandTotalSalesValue - grandTotalCostValue), grandTotalFont));
            profitValueCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            profitValueCell.setPadding(5);
            profitValueCell.setBackgroundColor(BaseColor.LIGHT_GRAY);
            grandTotalTable.addCell(profitValueCell);

            document.add(grandTotalTable);

            document.close();
            return baos.toByteArray();
        } catch (Exception e) {
            LOGGER.error("Failed to generate stock report PDF: " + e.getMessage(), e);
            throw new RuntimeException("Failed to generate PDF report", e);
        }
    }

    @Override
    public List<Stock> findStockRecordsByItemAndWarehouse(String itemCode, int warehouseCode) {
        try {
            return stockRepository.findAllByItemCodeAndWarehouseCodeOrderBySellingPriceAsc(itemCode, warehouseCode);
        } catch (Exception ex) {
            LOGGER.error("Error finding stock records for item {} in warehouse {}: {}", itemCode, warehouseCode, ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    @Override
    public Stock findById(String stockId) {
        try {
            Optional<Stock> stock = stockRepository.findById(stockId);
            return stock.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Error finding stock by ID {}: {}", stockId, ex.getMessage(), ex);
            return null;
        }
    }

}
