package lk.sout.business.inventory.repository;

import lk.sout.business.inventory.entity.ItemCategory;
import lk.sout.business.inventory.entity.SubCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SubCategoryRepository extends MongoRepository<SubCategory, String> {

    List<SubCategory> findBySubCategoryNameLikeIgnoreCaseAndActive(String name, Boolean active);

    List<SubCategory> findBySubCategoryNameLikeIgnoreCaseAndItemCategoryAndActive(String name, ItemCategory itemCategory, Boolean active);

    List<SubCategory> findByItemCategoryAndActive(ItemCategory itemCategory, Boolean active);

    Page<SubCategory> findAll(Pageable pageable);

    List<SubCategory> findAll();
}
