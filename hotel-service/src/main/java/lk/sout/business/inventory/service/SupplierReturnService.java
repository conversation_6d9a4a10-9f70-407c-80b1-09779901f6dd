package lk.sout.business.inventory.service;

import lk.sout.core.entity.Response;
import lk.sout.business.inventory.entity.SupplierReturn;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

public interface SupplierReturnService {
    
    Response save(SupplierReturn supplierReturn);
    
    Page<SupplierReturn> findAll(Pageable pageable);
    
    List<SupplierReturn> findBySupplierCode(String supplierCode);
    
    List<SupplierReturn> findByItemCode(String itemCode);
    
    List<SupplierReturn> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    List<SupplierReturn> findBySupplierAndDateRange(String supplierCode, LocalDateTime startDate, LocalDateTime endDate);
    
    SupplierReturn findById(String id);
}
