package lk.sout.business.inventory.entity;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 4/18/2018
 */
@Document
@Component
public class Brand {

    @Id
    private String id;

    @Indexed(unique = true)
    private String name;

    //@Indexed(unique = true)
    String code;

    private boolean active;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

}
