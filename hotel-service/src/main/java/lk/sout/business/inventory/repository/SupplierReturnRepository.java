package lk.sout.business.inventory.repository;

import lk.sout.business.inventory.entity.SupplierReturn;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SupplierReturnRepository extends MongoRepository<SupplierReturn, String> {
    
    Page<SupplierReturn> findAll(Pageable pageable);
    
    List<SupplierReturn> findBySupplierCode(String supplierCode);
    
    List<SupplierReturn> findByItemCode(String itemCode);
    
    List<SupplierReturn> findByReturnDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<SupplierReturn> findBySupplierCodeAndReturnDateBetween(String supplierCode, LocalDateTime startDate, LocalDateTime endDate);
}
