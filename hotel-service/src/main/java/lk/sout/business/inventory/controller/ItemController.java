package lk.sout.business.inventory.controller;

import lk.sout.core.entity.Response;
import lk.sout.core.service.UserService;
import lk.sout.business.inventory.entity.Brand;
import lk.sout.business.inventory.entity.Item;
import lk.sout.business.inventory.entity.ItemCategory;
import lk.sout.business.inventory.entity.SubCategory;
import lk.sout.business.inventory.service.ItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/item")
public class ItemController {

    @Autowired
    ItemService itemService;

    @Autowired
    UserService userService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Item item) {
        try {
            return ResponseEntity.ok(itemService.save(item));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(itemService.findAll(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findActiveByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveByNameLikeForSerialManagement", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveByNameLikeForSerialManagement(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findActiveByNameLikeForSerialManagement(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveByBarcodeLikeForSerialManagement", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveByBarcodeLikeForSerialManagement(@RequestParam("barcode") String barcode) {
        try {
            return ResponseEntity.ok(itemService.findActiveByBarcodeLikeForSerialManagement(barcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveServiceByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveServiceByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findActiveServiceByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveServiceByCodeLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveServiceByCodeLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findActiveServiceByCodeLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findAllByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByAny", method = RequestMethod.GET)
    private ResponseEntity<?> findByAny(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findByAny(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOneByItemCode", method = RequestMethod.GET)
    private ResponseEntity<?> findOneByItemCode(@RequestParam("itemCode") String code) {
        try {
            return ResponseEntity.ok(itemService.findOneByItemCode(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcodeLike", method = RequestMethod.GET)
    private ResponseEntity<?> searchByBarcodeLike(@RequestParam("barcode") String code) {
        try {
            return ResponseEntity.ok(itemService.findAllByBarcodeLike(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/checkAvailabilityByBarcode", method = RequestMethod.GET)
    private ResponseEntity<?> checkAvailabilityByBarcode(@RequestParam("barcode") String code) {
        try {
            return ResponseEntity.ok(itemService.findDuplicateBarcode(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOneByBarcode", method = RequestMethod.GET)
    private ResponseEntity<?> findOneByBarcode(@RequestParam("barcode") String code) {
        try {
            return ResponseEntity.ok(itemService.findOneByBarcode(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByItemCode", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByItemCode(@RequestParam("itemCode") String code) {
        try {
            return ResponseEntity.ok(itemService.findOneByItemCode(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOneById", method = RequestMethod.GET)
    private ResponseEntity<?> findOneById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(itemService.findOne(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByCategory", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByCategory(@RequestBody ItemCategory category) {
        try {
            return ResponseEntity.ok(itemService.findByCategory(category));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBySubCategory", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findBySubCategory(@RequestBody SubCategory subCategory) {
        try {
            return ResponseEntity.ok(itemService.findBySubCategory(subCategory));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBrand", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByBrand(@RequestBody Brand brand) {
        try {
            return ResponseEntity.ok(itemService.findByBrand(brand));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    /**
     * Find all items with filters
     * @param page Page number
     * @param pageSize Page size
     * @param categoryId Category ID filter (optional)
     * @param brandId Brand ID filter (optional)
     * @param modelId Model ID filter (optional)
     * @param supplierId Supplier ID filter (optional)
     * @param wholesale Wholesale flag filter (optional)
     * @param retail Retail flag filter (optional)
     * @param manageStock Manage stock flag filter (optional)
     * @param active Active flag filter (optional)
     * @param sortBy Field to sort by (optional, default: itemName)
     * @param sortDirection Sort direction (optional, default: asc)
     * @param groupBy Field to group by (optional, default: empty)
     * @return Filtered items
     */
    @RequestMapping(value = "/findAllFiltered", method = RequestMethod.GET)
    private ResponseEntity<?> findAllFiltered(
            @RequestParam("page") String page,
            @RequestParam("pageSize") String pageSize,
            @RequestParam(value = "categoryId", required = false) String categoryId,
            @RequestParam(value = "brandId", required = false) String brandId,
            @RequestParam(value = "modelId", required = false) String modelId,
            @RequestParam(value = "supplierId", required = false) String supplierId,
            @RequestParam(value = "wholesale", required = false) Boolean wholesale,
            @RequestParam(value = "retail", required = false) Boolean retail,
            @RequestParam(value = "manageStock", required = false) Boolean manageStock,
            @RequestParam(value = "active", required = false) Boolean active,
            @RequestParam(value = "sortBy", required = false, defaultValue = "itemName") String sortBy,
            @RequestParam(value = "sortDirection", required = false, defaultValue = "asc") String sortDirection,
            @RequestParam(value = "groupBy", required = false, defaultValue = "") String groupBy) {
        try {
            return ResponseEntity.ok(itemService.findAllFiltered(
                    PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize)),
                    categoryId,
                    brandId,
                    modelId,
                    supplierId,
                    wholesale,
                    retail,
                    manageStock,
                    active,
                    sortBy,
                    sortDirection,
                    groupBy));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    /**
     * Update barcode for an item and all related records
     * This endpoint is restricted to admin users only
     * @param itemId The ID of the item
     * @param oldBarcode The current barcode
     * @param newBarcode The new barcode
     * @return Response with success/failure message
     */
    @RequestMapping(value = "/updateBarcode", method = RequestMethod.POST)
    private ResponseEntity<?> updateBarcode(
            @RequestParam("itemId") String itemId,
            @RequestParam("oldBarcode") String oldBarcode,
            @RequestParam("newBarcode") String newBarcode) {
        try {
            // Check if user is admin
            if (!userService.isAdmin()) {
                return ResponseEntity.ok(new Response(403, "Only admin users can update barcodes", false,""));
            }

            return ResponseEntity.ok(itemService.updateBarcode(itemId, oldBarcode, newBarcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    /**
     * Update item checkbox properties (wholesale, retail, manageStock, active)
     * This endpoint is restricted to admin users only
     * @param itemId The ID of the item to update
     * @param wholesale Wholesale flag
     * @param retail Retail flag
     * @param manageStock Manage stock flag
     * @param active Active flag
     * @return Response with success/failure message
     */
    @RequestMapping(value = "/updateItemProperties", method = RequestMethod.POST)
    private ResponseEntity<?> updateItemProperties(
            @RequestParam("itemId") String itemId,
            @RequestParam(value = "wholesale", required = false) Boolean wholesale,
            @RequestParam(value = "retail", required = false) Boolean retail,
            @RequestParam(value = "manageStock", required = false) Boolean manageStock,
            @RequestParam(value = "active", required = false) Boolean active) {
        try {
            // Check if user is admin
            if (!userService.isAdmin()) {
                return ResponseEntity.ok(new Response(403, "Only admin users can update item properties", false,""));
            }

            return ResponseEntity.ok(itemService.updateItemProperties(itemId, wholesale, retail, manageStock, active));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


}
