package lk.sout.business.inventory.service;

import lk.sout.business.inventory.entity.ItemCategory;

import java.util.List;

public interface ItemCategoryService {

    boolean save(ItemCategory itemCategory);

    Iterable<ItemCategory> findAll(Integer page, Integer pageSize);

    boolean remove(String id);

    List<ItemCategory> findByName(String name);

    int getCount();

    List<ItemCategory> findAllCategories();

    ItemCategory findById(String id);
}
