package lk.sout.business.inventory.controller;

import lk.sout.business.inventory.entity.UOM;
import lk.sout.business.inventory.service.UOMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/uom")
public class UOMController {

    @Autowired
    UOMService uomService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody UOM uom) {
        try {
            return ResponseEntity.ok(uomService.save(uom));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(uomService.findAll(Integer.parseInt(page), Integer.parseInt(pageSize)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> delete(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(uomService.delete(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET)
    private ResponseEntity<?> search(@RequestParam("any") String name) {
        try {
            return ResponseEntity.ok(uomService.findByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
