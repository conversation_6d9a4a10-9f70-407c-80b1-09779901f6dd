package lk.sout.business.inventory.repository;


import lk.sout.business.inventory.entity.UOM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 4/22/2018
 */
@Repository
public interface UOMRepository extends MongoRepository<UOM, String> {

    List<UOM> findByNameLikeIgnoreCaseAndActive(String key, <PERSON>olean b);

    Page<UOM> findAll(Pageable pageable);

    UOM findByName(String name);
}
