package lk.sout.business.inventory.controller;

import lk.sout.business.inventory.entity.SubCategory;
import lk.sout.business.inventory.service.SubCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/subItemCategory")
public class SubCategoryController {

    @Autowired
    SubCategoryService subCategoryService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody SubCategory subCategory) {
        try {
            return ResponseEntity.ok(subCategoryService.save(subCategory));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/get", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(subCategoryService.findAll(Integer.parseInt(page), Integer.parseInt(pageSize)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET)
    private ResponseEntity<?> search(@RequestParam("any") String name) {
        try {
            return ResponseEntity.ok(subCategoryService.findByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByParent", method = RequestMethod.GET)
    private ResponseEntity<?> findByParent(@RequestParam("key") String key, @RequestParam("catName") String catName) {
        try {
            return ResponseEntity.ok(subCategoryService.findByNameAndParent(key, catName));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByCategoryId", method = RequestMethod.GET)
    private ResponseEntity<?> findByCategoryId(@RequestParam("categoryId") String categoryId) {
        try {
            return ResponseEntity.ok(subCategoryService.findByCategoryId(categoryId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    private ResponseEntity<?> delete(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(subCategoryService.remove(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
