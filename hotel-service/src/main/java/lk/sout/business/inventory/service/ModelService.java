package lk.sout.business.inventory.service;

import lk.sout.core.entity.Response;
import lk.sout.business.inventory.entity.Model;

import java.util.List;

public interface ModelService {

    Response save(Model model);

    boolean remove(Model model);

    Iterable<Model> findAll(Integer page, Integer pageSize);

    Model findOne(String id);

    List<Model> findByName(String key);

    String delete(String id);
}
