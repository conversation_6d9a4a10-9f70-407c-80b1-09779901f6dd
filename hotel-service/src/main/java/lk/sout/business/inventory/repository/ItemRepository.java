package lk.sout.business.inventory.repository;


import lk.sout.business.inventory.entity.*;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/21/2018
 */

@Repository
public interface ItemRepository extends MongoRepository<Item, String> {

    List<Item> findAllByItemNameLikeIgnoreCaseAndActive(String name, <PERSON>olean activate);

    List<Item> findAllByItemTypeAndItemNameLikeIgnoreCaseAndActive(ItemType itemType, String name, <PERSON><PERSON><PERSON> activate);

    List<Item> findAllByItemTypeAndItemCodeLikeIgnoreCaseAndActive(ItemType itemType, String name, Boolean activate);

    List<Item> findAllByItemCodeLikeIgnoreCaseAndActive(String name, Boolean activate);

    List<Item> findTop15ByItemNameLikeIgnoreCase(String name);

    List<Item> findAllByItemCategory(ItemCategory category);

    List<Item> findAllBySubCategory(SubCategory subCategory);

    List<Item> findAllByBarcodeLikeIgnoreCase(String code);

    List<Item> findAllByBrand(Brand brand);

    Item findByItemCode(String s);

    Item findByBarcode(String barcode);

    int countAllByActive(boolean active);
}
