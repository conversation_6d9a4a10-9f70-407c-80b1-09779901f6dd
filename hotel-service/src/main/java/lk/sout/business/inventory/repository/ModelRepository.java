package lk.sout.business.inventory.repository;

import lk.sout.business.inventory.entity.Model;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ModelRepository extends MongoRepository<Model, String> {

    List<Model> findByNameLikeIgnoreCaseAndActive(String key, Boolean active);
}
