package lk.sout.business.inventory.repository;

import lk.sout.business.inventory.entity.*;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/22/2020
 */
@Repository
public class CustomStockRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomStockRepository.class);

    @Autowired
    MongoTemplate mongoTemplate;

    public List<StockSummary> findStockSummary() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "sellingPrice", "itemCost", "warehouseName").
                            andExpression("quantity * sellingPrice").as("tempPriceValue").
                            andExpression("quantity * itemCost").as("tempCostValue"),
                    group("warehouseName").
                            count().as("noOfItems").sum("quantity").as("totalQuantity").
                            sum("tempPriceValue").as("totalPriceValue").sum("tempCostValue").
                            as("totalCostValue"),
                    project("warehouseName", "noOfItems", "totalQuantity", "totalPriceValue", "totalCostValue").
                            and("warehouseName").previousOperation()
            );
            AggregationResults<StockSummary> groupResults
                    = mongoTemplate.aggregate(agg, StockSummary.class);
            List<StockSummary> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListByWarehouse(int warehouseCode, Double threshold) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("warehouseCode").is(warehouseCode));
            query.addCriteria(Criteria.where("quantity").lte(threshold));
            query.fields().include("barcode").include("itemName").include("itemCode").
                    include("sellingPrice").include("quantity").include("deadStockLevel").
                    include("itemCost");
            return mongoTemplate.find(query, Stock.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderList(Double threshold) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListForItemCategory(Double threshold, String catCode) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    match(Criteria.where("categoryCode").is(catCode)),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListForItemBrand(Double threshold, String brandCode) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    match(Criteria.where("brandCode").is(brandCode)),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListBySupplier(Double threshold, String supplierCode) {
        try {
            // First, find all items with the given supplier code
            Query itemQuery = new Query();
            itemQuery.addCriteria(Criteria.where("supplierCode").is(supplierCode));
            itemQuery.fields().include("itemCode");

            List<Document> items = mongoTemplate.find(itemQuery, Document.class, "item");
            List<String> itemCodes = new ArrayList<>();

            for (Document item : items) {
                itemCodes.add(item.getString("itemCode"));
            }

            if (itemCodes.isEmpty()) {
                return new ArrayList<>();
            }

            // Then find stocks for these items with quantity less than threshold
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode"),
                    match(Criteria.where("itemCode").in(itemCodes)),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );

            AggregationResults<Stock> groupResults = mongoTemplate.aggregate(agg, Stock.class);
            return groupResults.getMappedResults();
        } catch (Exception ex) {
            LOGGER.error("Error finding reorder list by supplier: {}", ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }

    public List<Stock> findReorderListForCatAndBrand(Double threshold, String catCode, String brandCode) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    match(Criteria.where("categoryCode").is(catCode)),
                    match(Criteria.where("brandCode").is(brandCode)),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public StockTotals calculateTotalValues() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project()
                            .andExpression("quantity != null ? quantity : 0").as("quantity")
                            .andExpression("sellingPrice != null ? sellingPrice : 0").as("sellingPrice")
                            .andExpression("itemCost != null ? itemCost : 0").as("itemCost")
                            .andExpression("(quantity != null ? quantity : 0) * (sellingPrice != null ? sellingPrice : 0)").as("totalPriceValue")
                            .andExpression("(quantity != null ? quantity : 0) * (itemCost != null ? itemCost : 0)").as("totalCostValue"),
                    group()
                            .sum("totalPriceValue").as("totalSalesValue")
                            .sum("totalCostValue").as("totalCostValue")
            );

            AggregationResults<Document> results = mongoTemplate.aggregate(agg, Stock.class, Document.class);
            Document result = results.getUniqueMappedResult();

            StockTotals totals = new StockTotals();
            if (result != null) {
                totals.setTotalSalesValue(result.get("totalSalesValue", 0.0));
                totals.setTotalCostValue(result.get("totalCostValue", 0.0));
            } else {
                totals.setTotalSalesValue(0.0);
                totals.setTotalCostValue(0.0);
            }
            return totals;
        } catch (Exception ex) {
            LOGGER.error("Failed to calculate total values: " + ex.getMessage(), ex);
            // Return default object instead of null
            StockTotals defaultTotals = new StockTotals();
            defaultTotals.setTotalSalesValue(0.0);
            defaultTotals.setTotalCostValue(0.0);
            return defaultTotals;
        }
    }

    public StockTotals calculateTotalValuesByWarehouse(int warehouseCode) {
        try {
            // Get all stocks for the warehouse
            Query query = new Query();
            query.addCriteria(Criteria.where("warehouseCode").is(warehouseCode));
            List<Stock> stocks = mongoTemplate.find(query, Stock.class);

            // Calculate totals in Java
            double totalSalesValue = 0.0;
            double totalCostValue = 0.0;

            for (Stock stock : stocks) {
                double qty = (stock.getQuantity() != null) ? stock.getQuantity() : 0.0;
                double sellingPrice = (stock.getSellingPrice() != null) ? stock.getSellingPrice() : 0.0;
                double itemCost = (stock.getItemCost() != null) ? stock.getItemCost() : 0.0;

                totalSalesValue += qty * sellingPrice;
                totalCostValue += qty * itemCost;
            }

            StockTotals totals = new StockTotals();
            totals.setTotalSalesValue(totalSalesValue);
            totals.setTotalCostValue(totalCostValue);
            return totals;
        } catch (Exception ex) {
            LOGGER.error("Failed to calculate total values for warehouse " + warehouseCode + ": " + ex.getMessage(), ex);
            // Return default object instead of null
            StockTotals defaultTotals = new StockTotals();
            defaultTotals.setTotalSalesValue(0.0);
            defaultTotals.setTotalCostValue(0.0);
            return defaultTotals;
        }
    }

    public List<Stock> findStockDetailsForReport() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("itemCode", "itemName", "barcode", "quantity", "sellingPrice", "itemCost",
                            "warehouseName", "categoryCode", "brandCode", "categoryName", "brandName", "modelName", "supplierName").
                            andExpression("quantity * sellingPrice").as("totalPriceValue").
                            andExpression("quantity * itemCost").as("totalCostValue"),
                    sort(Sort.Direction.ASC, "warehouseName", "itemName")
            );
            AggregationResults<Stock> groupResults = mongoTemplate.aggregate(agg, Stock.class);
            return groupResults.getMappedResults();
        } catch (Exception ex) {
            LOGGER.error("Failed to fetch stock details for report: " + ex.getMessage());
            return null;
        }
    }

    /**
     * Find stock details grouped by category for report
     */
    public List<Stock> findStockDetailsGroupedByCategory() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("itemCode", "itemName", "barcode", "quantity", "sellingPrice", "itemCost",
                            "warehouseName", "categoryCode", "brandCode", "categoryName", "brandName", "modelName", "supplierName").
                            andExpression("quantity * sellingPrice").as("totalPriceValue").
                            andExpression("quantity * itemCost").as("totalCostValue"),
                    sort(Sort.Direction.ASC, "categoryName", "itemName")
            );
            AggregationResults<Stock> groupResults = mongoTemplate.aggregate(agg, Stock.class);
            return groupResults.getMappedResults();
        } catch (Exception ex) {
            LOGGER.error("Failed to fetch stock details grouped by category: " + ex.getMessage());
            return null;
        }
    }

    /**
     * Find stock details grouped by brand for report
     */
    public List<Stock> findStockDetailsGroupedByBrand() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("itemCode", "itemName", "barcode", "quantity", "sellingPrice", "itemCost",
                            "warehouseName", "categoryCode", "brandCode", "categoryName", "brandName", "modelName", "supplierName").
                            andExpression("quantity * sellingPrice").as("totalPriceValue").
                            andExpression("quantity * itemCost").as("totalCostValue"),
                    sort(Sort.Direction.ASC, "brandName", "itemName")
            );
            AggregationResults<Stock> groupResults = mongoTemplate.aggregate(agg, Stock.class);
            return groupResults.getMappedResults();
        } catch (Exception ex) {
            LOGGER.error("Failed to fetch stock details grouped by brand: " + ex.getMessage());
            return null;
        }
    }

    /**
     * Find stock details grouped by model for report
     */
    public List<Stock> findStockDetailsGroupedByModel() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("itemCode", "itemName", "barcode", "quantity", "sellingPrice", "itemCost",
                            "warehouseName", "categoryCode", "brandCode", "categoryName", "brandName", "modelName", "supplierName").
                            andExpression("quantity * sellingPrice").as("totalPriceValue").
                            andExpression("quantity * itemCost").as("totalCostValue"),
                    sort(Sort.Direction.ASC, "modelName", "itemName")
            );
            AggregationResults<Stock> groupResults = mongoTemplate.aggregate(agg, Stock.class);
            return groupResults.getMappedResults();
        } catch (Exception ex) {
            LOGGER.error("Failed to fetch stock details grouped by model: " + ex.getMessage());
            return null;
        }
    }

    /**
     * Find stock details grouped by supplier for report
     */
    public List<Stock> findStockDetailsGroupedBySupplier() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("itemCode", "itemName", "barcode", "quantity", "sellingPrice", "itemCost",
                            "warehouseName", "categoryCode", "brandCode", "categoryName", "brandName", "modelName", "supplierName").
                            andExpression("quantity * sellingPrice").as("totalPriceValue").
                            andExpression("quantity * itemCost").as("totalCostValue"),
                    sort(Sort.Direction.ASC, "supplierName", "itemName")
            );
            AggregationResults<Stock> groupResults = mongoTemplate.aggregate(agg, Stock.class);
            return groupResults.getMappedResults();
        } catch (Exception ex) {
            LOGGER.error("Failed to fetch stock details grouped by supplier: " + ex.getMessage());
            return null;
        }
    }

    public List<Stock> findStocksByWarehouse(int warehouseCode) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("warehouseCode").is(warehouseCode));
            query.with(Sort.by(Sort.Direction.ASC, "itemName"));

            return mongoTemplate.find(query, Stock.class);
        } catch (Exception ex) {
            LOGGER.error("Failed to fetch stock details for warehouse: " + ex.getMessage(), ex);
            return new ArrayList<>();  // Return empty list instead of null
        }
    }
}
