package lk.sout.business.inventory.service.impl;

import lk.sout.business.inventory.entity.Model;
import lk.sout.business.inventory.repository.ModelRepository;
import lk.sout.business.inventory.service.BrandService;
import lk.sout.business.inventory.service.ModelService;
import lk.sout.core.entity.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ModelServiceImpl implements ModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandService.class);

    @Autowired
    ModelRepository modelRepository;

    public Response save(Model model) {
        try {
            // Validate that brand is not null
            if (model.getBrand() == null) {
                LOGGER.error("Model save failed: Brand is required");
                return new Response(400, "Brand is required for model", null);
            }

            model.setCode(String.valueOf(System.currentTimeMillis()));
            modelRepository.save(model);
            LOGGER.info("Model saved. " + model.getName());
            return new Response(200, "Model saved successfully", model);
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("Saving Model Failed. " + model.getName());
            return new Response(500, "Failed to save model: " + ex.getMessage(), null);
        }
    }

    public boolean remove(Model model) {
        try {
            modelRepository.delete(model);
            LOGGER.info("Brand removed. " + model.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Brand Failed. " + model.getName());
            return false;
        }
    }

    public Iterable<Model> findAll(Integer page, Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("id").descending());
            return modelRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All brands failed");
            return null;
        }
    }

    public Model findOne(String id) {
        try {
            Optional<Model> model = modelRepository.findById(id);
            return model.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public List<Model> findByName(String key) {
        try {
            return modelRepository.findByNameLikeIgnoreCaseAndActive(key, true);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public String delete(String id) {
        try {
            modelRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing brand failed " + id + ". " + ex.getMessage());
            return "failed";
        }
    }

}
