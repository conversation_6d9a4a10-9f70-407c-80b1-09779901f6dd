package lk.sout.business.inventory.controller;

import lk.sout.core.entity.Response;
import lk.sout.business.inventory.entity.SerialNumber;
import lk.sout.business.inventory.service.SerialNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST Controller for SerialNumber management
 */
@RestController
@RequestMapping("/serial-numbers")
@CrossOrigin
public class SerialNumberController {

    @Autowired
    private SerialNumberService serialNumberService;

    /**
     * Save a serial number
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> save(@RequestBody SerialNumber serialNumber) {
        try {
            Response response = serialNumberService.save(serialNumber);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Save multiple serial numbers
     */
    @PostMapping(value = "/bulk", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> saveAll(@RequestBody List<SerialNumber> serialNumbers) {
        try {
            Response response = serialNumberService.saveAll(serialNumbers);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find serial number by ID
     */
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<SerialNumber> findById(@PathVariable String id) {
        try {
            Optional<SerialNumber> serialNumber = serialNumberService.findById(id);
            return serialNumber.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find serial number by serial number string
     */
    @GetMapping(value = "/search/{serialNumber}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<SerialNumber> findBySerialNumber(@PathVariable String serialNumber) {
        try {
            Optional<SerialNumber> serial = serialNumberService.findBySerialNumber(serialNumber);
            return serial.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Check if serial number exists
     */
    @GetMapping(value = "/exists/{serialNumber}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Boolean> existsBySerialNumber(@PathVariable String serialNumber) {
        try {
            boolean exists = serialNumberService.existsBySerialNumber(serialNumber);
            return ResponseEntity.ok(exists);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find all serial numbers for an item
     */
    @GetMapping(value = "/item/{itemCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<SerialNumber>> findByItemCode(@PathVariable String itemCode) {
        try {
            List<SerialNumber> serialNumbers = serialNumberService.findByItemCode(itemCode);
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find serial numbers by item code and status
     */
    @GetMapping(value = "/item/{itemCode}/status/{status}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<SerialNumber>> findByItemCodeAndStatus(@PathVariable String itemCode, 
                                                                     @PathVariable String status) {
        try {
            List<SerialNumber> serialNumbers = serialNumberService.findByItemCodeAndStatus(itemCode, status);
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find available serial numbers for an item in a warehouse
     */
    @GetMapping(value = "/available", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<SerialNumber>> findAvailable(@RequestParam String itemCode, 
                                                           @RequestParam int warehouseCode) {
        try {
            List<SerialNumber> serialNumbers = serialNumberService.findAvailableByItemCodeAndWarehouse(itemCode, warehouseCode);
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Count available serial numbers for an item in a warehouse
     */
    @GetMapping(value = "/count/available", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Long> countAvailable(@RequestParam String itemCode, 
                                              @RequestParam int warehouseCode) {
        try {
            long count = serialNumberService.countAvailableByItemCodeAndWarehouse(itemCode, warehouseCode);
            return ResponseEntity.ok(count);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Add serial numbers for purchase
     */
    @PostMapping(value = "/purchase", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> addForPurchase(@RequestBody Map<String, Object> request) {
        try {
            String itemCode = (String) request.get("itemCode");
            @SuppressWarnings("unchecked")
            List<String> serialNumbers = (List<String>) request.get("serialNumbers");
            String purchaseInvoiceId = (String) request.get("purchaseInvoiceId");
            Double purchasePrice = Double.valueOf(request.get("purchasePrice").toString());
            Integer warehouseCode = Integer.valueOf(request.get("warehouseCode").toString());
            
            LocalDateTime warrantyExpiryDate = null;
            if (request.get("warrantyExpiryDate") != null) {
                warrantyExpiryDate = LocalDateTime.parse(request.get("warrantyExpiryDate").toString());
            }

            Response response = serialNumberService.addSerialNumbersForPurchase(
                    itemCode, serialNumbers, purchaseInvoiceId, purchasePrice, warehouseCode, warrantyExpiryDate);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Mark serial numbers as sold
     */
    @PostMapping(value = "/sold", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> markAsSold(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> serialNumbers = (List<String>) request.get("serialNumbers");
            String salesInvoiceId = (String) request.get("salesInvoiceId");
            Double sellingPrice = Double.valueOf(request.get("sellingPrice").toString());
            LocalDateTime dateSold = LocalDateTime.parse(request.get("dateSold").toString());

            // Customer information (optional)
            String customerId = (String) request.get("customerId");
            String customerName = (String) request.get("customerName");
            String customerNumber = (String) request.get("customerNumber");

            Response response = serialNumberService.markSerialNumbersAsSold(
                    serialNumbers, salesInvoiceId, sellingPrice, dateSold,
                    customerId, customerName, customerNumber);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Mark serial numbers as returned
     */
    @PostMapping(value = "/returned", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> markAsReturned(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> serialNumbers = (List<String>) request.get("serialNumbers");
            String returnInvoiceId = (String) request.get("returnInvoiceId");

            Response response = serialNumberService.markSerialNumbersAsReturned(serialNumbers, returnInvoiceId);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Mark serial numbers as damaged
     */
    @PostMapping(value = "/damaged", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> markAsDamaged(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> serialNumbers = (List<String>) request.get("serialNumbers");
            String notes = (String) request.get("notes");

            Response response = serialNumberService.markSerialNumbersAsDamaged(serialNumbers, notes);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Validate serial numbers for quantity
     */
    @PostMapping(value = "/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> validateForQuantity(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> serialNumbers = (List<String>) request.get("serialNumbers");
            Double quantity = Double.valueOf(request.get("quantity").toString());
            String itemCode = (String) request.get("itemCode");

            Response response = serialNumberService.validateSerialNumbersForQuantity(serialNumbers, quantity, itemCode);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Search serial numbers
     */
    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<SerialNumber>> search(@RequestParam String searchTerm) {
        try {
            List<SerialNumber> serialNumbers = serialNumberService.searchSerialNumbers(searchTerm);
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find warranty expiring soon
     */
    @GetMapping(value = "/warranty/expiring", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<SerialNumber>> findWarrantyExpiringSoon(@RequestParam(defaultValue = "30") int daysAhead) {
        try {
            List<SerialNumber> serialNumbers = serialNumberService.findWarrantyExpiringSoon(daysAhead);
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find expired warranty
     */
    @GetMapping(value = "/warranty/expired", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<SerialNumber>> findExpiredWarranty() {
        try {
            List<SerialNumber> serialNumbers = serialNumberService.findExpiredWarranty();
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find all serial numbers with pagination
     */
    @GetMapping(value = "/paginated", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Page<SerialNumber>> findAllPaginated(@RequestParam(defaultValue = "0") int page,
                                                              @RequestParam(defaultValue = "20") int size,
                                                              @RequestParam(defaultValue = "createdDate") String sortBy,
                                                              @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<SerialNumber> serialNumbers = serialNumberService.findAllPaginated(pageable);
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Find paginated serial numbers by item code
     */
    @GetMapping(value = "/item/{itemCode}/paginated", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Page<SerialNumber>> findByItemCodePaginated(@PathVariable String itemCode,
                                                                     @RequestParam(defaultValue = "0") int page,
                                                                     @RequestParam(defaultValue = "20") int size,
                                                                     @RequestParam(defaultValue = "dateAdded") String sortBy,
                                                                     @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<SerialNumber> serialNumbers = serialNumberService.findByItemCodePaginated(itemCode, pageable);
            return ResponseEntity.ok(serialNumbers);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get serial number statistics for item
     */
    @GetMapping(value = "/stats/{itemCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<SerialNumberService.SerialNumberStats> getStats(@PathVariable String itemCode) {
        try {
            SerialNumberService.SerialNumberStats stats = serialNumberService.getSerialNumberStats(itemCode);
            return ResponseEntity.ok(stats);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update serial number status
     */
    @PutMapping(value = "/{serialNumber}/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> updateStatus(@PathVariable String serialNumber, 
                                                @RequestBody Map<String, String> request) {
        try {
            String newStatus = request.get("status");
            Response response = serialNumberService.updateStatus(serialNumber, newStatus);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update serial number warranty
     */
    @PutMapping(value = "/{serialNumber}/warranty", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> updateWarranty(@PathVariable String serialNumber,
                                                  @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime warrantyExpiryDate) {
        try {
            Response response = serialNumberService.updateWarranty(serialNumber, warrantyExpiryDate);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Transfer serial numbers between warehouses
     */
    @PostMapping(value = "/transfer", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> transfer(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> serialNumbers = (List<String>) request.get("serialNumbers");
            Integer sourceWarehouse = Integer.valueOf(request.get("sourceWarehouse").toString());
            Integer targetWarehouse = Integer.valueOf(request.get("targetWarehouse").toString());

            Response response = serialNumberService.transferSerialNumbers(serialNumbers, sourceWarehouse, targetWarehouse);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete serial number
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> delete(@PathVariable String id) {
        try {
            Response response = serialNumberService.delete(id);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete serial numbers by item code
     */
    @DeleteMapping(value = "/item/{itemCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> deleteByItemCode(@PathVariable String itemCode) {
        try {
            Response response = serialNumberService.deleteByItemCode(itemCode);
            return ResponseEntity.status(response.getCode()).body(response);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
