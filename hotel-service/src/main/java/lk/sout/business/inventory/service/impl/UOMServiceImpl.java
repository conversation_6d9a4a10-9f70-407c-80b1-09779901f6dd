package lk.sout.business.inventory.service.impl;


import lk.sout.business.inventory.entity.UOM;
import lk.sout.business.inventory.repository.UOMRepository;
import lk.sout.business.inventory.service.UOMService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 4/22/2018
 */
@Service
public class UOMServiceImpl implements UOMService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UOMServiceImpl.class);

    @Autowired
    UOMRepository uomRepository;

    List<UOM> uoms = new ArrayList<>();

    @Override
    public boolean save(UOM uom) {
        try {
            uomRepository.save(uom);
            LOGGER.info("UOM saved. " + uom.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving UOM Failed. " + uom.getName());
            return false;
        }
    }

    @Override
    public Iterable<UOM> findAll(Integer page, Integer pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("id").descending());
            return uomRepository.findAll(pageable);
        } catch (Exception e) {
            return null;
        }
    }


    @Override
    public boolean remove(UOM uom) {
        try {
            uomRepository.delete(uom);
            LOGGER.info("UOM removed. " + uom.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing UOM Failed. " + uom.getName());
            return false;
        }
    }

    @Override
    public String delete(String id) {
        try {
            uomRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing UOM failed " + id + ". " + ex.getMessage());
            return "failed";
        }
    }


    @Override
    public List<UOM> findByName(String name) {
        try {
            return uomRepository.findByNameLikeIgnoreCaseAndActive(name, true);
        } catch (Exception ex) {
            LOGGER.error(ex.toString());
            return null;
        }
    }
}
