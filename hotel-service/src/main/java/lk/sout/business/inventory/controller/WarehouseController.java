package lk.sout.business.inventory.controller;

import lk.sout.business.inventory.entity.Warehouse;
import lk.sout.business.inventory.service.WarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/warehouse")
public class WarehouseController {

    @Autowired
    WarehouseService warehouseService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Warehouse warehouse) {
        try {
            return ResponseEntity.ok(warehouseService.save(warehouse));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> update(@RequestBody Warehouse warehouse) {
        try {
            return ResponseEntity.ok(warehouseService.update(warehouse));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public List<Warehouse> findAll() {
        return warehouseService.findAll();
    }

  /*  @RequestMapping(value = "/findUserSpecific", method = RequestMethod.GET)
    public List<Warehouse> findUserSpecific() {
        return warehouseService.findUserSpecific();
    }*/


    @RequestMapping(value = "/findByCode", method = RequestMethod.GET)
    private ResponseEntity<?> findByCode(@RequestParam("code") int code){
        try {
            return ResponseEntity.ok(warehouseService.findByCode(code));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPage", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPage(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(warehouseService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(warehouseService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> delete(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(warehouseService.delete(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByName", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllByName(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(warehouseService.findAllByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
