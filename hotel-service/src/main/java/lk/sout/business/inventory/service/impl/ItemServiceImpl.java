package lk.sout.business.inventory.service.impl;

import lk.sout.core.entity.*;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.business.inventory.entity.*;
import lk.sout.business.inventory.repository.*;
import lk.sout.business.inventory.service.StockService;
import lk.sout.business.trade.entity.SalesInvoiceRecord;
import lk.sout.business.trade.repository.PurchaseInvoiceRecordRepository;
import lk.sout.business.trade.repository.SalesInvoiceRecordRepository;
import lk.sout.business.inventory.service.ItemCategoryService;
import lk.sout.business.inventory.service.ItemService;
import lk.sout.business.inventory.service.WarehouseService;

import lk.sout.business.trade.entity.PurchaseInvoiceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Created by Madhawa Weerasinghe on 4/21/2018
 */

@Service
public class ItemServiceImpl implements ItemService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemServiceImpl.class);

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    Response response;

    @Autowired
    ItemTypeRepository itemTypeRepository;

    @Autowired
    PurchaseInvoiceRecord purchaseInvoiceRecord;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ActionService actionService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    Action action;

    @Autowired
    Item item;

    @Autowired
    CustomItemRepository customItemRepository;

    @Autowired
    Stock stock;

    @Autowired
    StockService stockService;

    @Autowired
    ItemCategoryService itemCategoryService;

    @Autowired
    WarehouseService warehouseService;

    @Autowired
    StockMovementRepository stockMovementRepository;

    @Autowired
    SalesInvoiceRecordRepository salesInvoiceRecordRepository;

    @Autowired
    PurchaseInvoiceRecordRepository purchaseInvoiceRecordRepository;

    @Autowired
    StockRepository stockRepository;

    @Value("${sout.mainWhCode}")
    int mainWhCode;

    @Override
    @Transactional
    public Response save(Item item) {
        try {
            if (null == item.getId()) {
                if(itemRepository.findByBarcode(item.getBarcode()) != null) {
                    response.setCode(503);
                    response.setMessage("Item Already Available");
                    response.setData("Item Already Available");
                    return response;
                }
                item.setItemCode(String.valueOf(System.currentTimeMillis()));
                if (item.isManageStock()) {
                    Warehouse mainWh = warehouseService.findByCode(0);
                    stock.setId(null);
                    stock.setQuantity(item.getQuantity() != null ? item.getQuantity() : 0);
                    stock.setDeadStockLevel(item.getDeadStockLevel());
                    stock.setItemCode(item.getItemCode());
                    stock.setBarcode(item.getBarcode());
                    stock.setItemName(item.getItemName());
                    stock.setSellingPrice(item.getSellingPrice());
                    stock.setItemCost(item.getItemCost());
                    stock.setItemName(item.getItemName());

                    if (null != item.getItemCategory()) {
                        stock.setCategoryCode(item.getItemCategory().getCode());
                        stock.setCategoryName(item.getItemCategory().getCategoryName());
                    }
                    if (null != item.getModel()) {
                        stock.setModelCode(item.getModel().getCode());
                        stock.setModelName(item.getModel().getName());
                    }
                    if (null != item.getBrand()) {
                        stock.setBrandCode(item.getBrand().getCode());
                        stock.setBrandName(item.getBrand().getName());
                    }
                    if (null != item.getSupplier()) {
                        stock.setSupplierCode(item.getSupplier().getSupplierNo());
                        stock.setSupplierName(item.getSupplier().getName());
                    }

                    stock.setWarehouseCode(mainWhCode);
                    stock.setWarehouseName(mainWh.getName());
                    stock.setActive(true);
                    stockService.save(stock,"Creating Stock by Item Creation", item.getItemCode());
                }
            }
            if (null != item.getId() && item.isManageStock()) {
                Item avlItem = itemRepository.findByItemCode(item.getItemCode());
                List<Stock> stocks = stockService.findByItemCode(item.getItemCode());

                if (!avlItem.getBarcode().equals(item.getBarcode())) {
                    for (Stock stock : stocks) {
                        stock.setBarcode(item.getBarcode());
                        stockService.basicSave(stock);
                    }
                }
                if (!avlItem.getItemName().equals(item.getItemName())) {
                    for (Stock stock : stocks) {
                        stock.setItemName(item.getItemName());
                        stockService.basicSave(stock);
                    }
                }
                if (avlItem.getSellingPrice().compareTo(item.getSellingPrice()) != 0) {
                    for (Stock stock : stocks) {
                        stock.setSellingPrice(item.getSellingPrice());
                        stockService.basicSave(stock);
                    }
                }
                if (avlItem.getItemCost().compareTo(item.getItemCost()) != 0) {
                    for (Stock stock : stocks) {
                        stock.setItemCost(item.getItemCost());
                        stockService.basicSave(stock);
                    }
                }
                if (null != avlItem.getItemCategory() && !avlItem.getItemCategory().getCode().equals(item.getItemCategory().getCode())) {
                    for (Stock stock : stocks) {
                        stock.setCategoryCode(item.getItemCategory().getCode());
                        stockService.basicSave(stock);
                    }
                }
                if (null != avlItem.getBrand() && !avlItem.getBrand().getCode().equals(item.getBrand().getCode())) {
                    for (Stock stock : stocks) {
                        stock.setBrandCode(item.getBrand().getCode());
                        stockService.basicSave(stock);
                    }
                }
                if(null != avlItem.getSupplier() && !avlItem.getSupplier().getSupplierNo().equals(item.getSupplier().getSupplierNo())) {
                    for (Stock stock : stocks) {
                        stock.setSupplierCode(item.getSupplier().getSupplierNo());
                        stockService.basicSave(stock);
                    }
                }
            }

            //because we dont use qty
            item.setQuantity(0.0);
            itemRepository.save(item);
            response.setCode(200);
            response.setMessage("Item Created Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Creating Item Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Item Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    public String findNextItemCode() {
        Sequence sequence = sequenceService.findSequenceByName("ItemCode");
        String itemCodeChk = sequence.getPrefix() + String.valueOf(sequence.getCounter());
        while (findItemCode(itemCodeChk)) {
            sequence.setCounter(sequence.getCounter() + 1);
            itemCodeChk = sequence.getPrefix() + String.valueOf(sequence.getCounter());
            sequenceService.save(sequence);
        }
        return itemCodeChk;
    }

    public boolean findItemCode(String text) {
        boolean check = false;
        if (itemRepository.findByItemCode(text) != null) {
            check = true;
        } else if (itemRepository.findByItemCode(text) == null) {
            check = false;
        }
        return check;
    }

  /*  public void findNextBarcode(){
        if (!findDuplicateBarcode()) {
            return itemCodeChk;
        }
        Sequence sequence = sequenceService.findSequenceByName("ItemCode");
        sequence.setCounter(sequence.getCounter() + 1);
        itemCodeChk = sequence.getPrefix() + String.valueOf(sequence.getCounter());
        sequenceService.save(sequence);

        findNextItemCode(itemCodeChk);

        return itemCodeChk;
    }*/

    @Override
    public boolean remove(String id) {
        try {
            itemRepository.deleteById(id);
            LOGGER.info("Item removed. ");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Item Failed : " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Item> findAll(Pageable pageable) {
        try {
            return customItemRepository.findAllItemForTable(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Items failed");
            return null;
        }
    }

    @Override
    public Item findOne(String id) {
        try {
            return itemRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving an Item failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findAllByItemCodeLike(String code) {
        if (code.equals("0")) {
            List<Item> items = new ArrayList<>();
            items.add(itemRepository.findByItemCode("0"));
            return items;
        }
        return itemRepository.findAllByItemCodeLikeIgnoreCaseAndActive(code, true);
    }

    @Override
    public List<Item> findAllByBarcodeLike(String code) {
        try {
            return customItemRepository.searchBarcodeLikeForSuggestions(code);
        } catch (Exception e) {
            LOGGER.error("Search item Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findByCategory(ItemCategory category) {
        try {
            return itemRepository.findAllByItemCategory(category);
        } catch (Exception e) {
            LOGGER.error("Search item Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findBySubCategory(SubCategory subCategory) {
        try {
            return itemRepository.findAllBySubCategory(subCategory);
        } catch (Exception e) {
            LOGGER.error("Search item by subcategory Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findByBrand(Brand brand) {
        try {
            return itemRepository.findAllByBrand(brand);
        } catch (Exception e) {
            LOGGER.error("Search item Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveByNameLike(String name) {
        try {
            return customItemRepository.searchItemNameLikeForSuggestions(name);
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveByNameLikeForSerialManagement(String name) {
        try {
            return customItemRepository.searchItemNameLikeForSerialManagement(name);
        } catch (Exception ex) {
            LOGGER.error("Search item for serial management Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveByBarcodeLikeForSerialManagement(String barcode) {
        try {
            return customItemRepository.searchBarcodeLikeForSerialManagement(barcode);
        } catch (Exception ex) {
            LOGGER.error("Search item by barcode for serial management Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveServiceByNameLike(String name) {
        try {
            ItemType service = itemTypeRepository.findByName("Service");
            List<Item> items = itemRepository.findAllByItemTypeAndItemNameLikeIgnoreCaseAndActive(service, name, true);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveServiceByCodeLike(String name) {
        try {
            ItemType service = itemTypeRepository.findByName("Service");
            return itemRepository.findAllByItemTypeAndItemCodeLikeIgnoreCaseAndActive(service, name, true);
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findAllByNameLike(String name) {
        try {
            List<Item> items = itemRepository.findTop15ByItemNameLikeIgnoreCase(name);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findByAny(String any) {
        List<Item> itemList = new ArrayList<>();
        try {
            if (itemRepository.findAllByItemNameLikeIgnoreCaseAndActive(any, true).size() > 0) {
                List<Item> items = itemRepository.findAllByItemNameLikeIgnoreCaseAndActive(any, true);
                itemList = items;
            } else if (itemRepository.findAllByItemCodeLikeIgnoreCaseAndActive(any, true).size() > 0) {
                List<Item> items = itemRepository.findAllByItemCodeLikeIgnoreCaseAndActive(any, true);
                itemList = items;
            }
            return itemList;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Item findOneByItemCode(String s) {
        try {
            Item item = itemRepository.findByItemCode(s);
            return item;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Item findOneByBarcode(String s) {
        try {
            Item item = itemRepository.findByBarcode(s);
            return item;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public boolean findDuplicateBarcode(String s) {
        boolean value = true;
        try {
            if (itemRepository.findByBarcode(s) != null) {
                value = true;
            } else {
                value = false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    @Override
    @Transactional
    public Response updateBarcode(String itemId, String oldBarcode, String newBarcode) {
        try {
            // Validate inputs
            if (itemId == null || oldBarcode == null || newBarcode == null) {
                return new Response(400, "Invalid input parameters", false,"");
            }

            // Check if the new barcode already exists
            if (findDuplicateBarcode(newBarcode)) {
                return new Response(409, "Barcode already exists", false,"");
            }

            // Find the item
            Item item = itemRepository.findById(itemId).orElse(null);
            if (item == null) {
                return new Response(404, "Item not found", false,"");
            }

            // Verify the old barcode matches
            if (!item.getBarcode().equals(oldBarcode)) {
                return new Response(400, "Current barcode does not match", false,"");
            }

            // Update the item barcode
            item.setBarcode(newBarcode);
            itemRepository.save(item);

            // Get the item code for finding related records
            String itemCode = item.getItemCode();

            // Update barcode in Stock records
            List<Stock> stockRecords = stockRepository.findByItemCode(itemCode);
            for (Stock stock : stockRecords) {
                stock.setBarcode(newBarcode);
                stockRepository.save(stock);
            }

            // Update barcode in StockMovement records where itemCode matches
            // Since StockMovementRepository doesn't have a findByBarcode method, we need to use a custom query
            // or iterate through all records to find matching ones
            List<StockMovement> allStockMovements = stockMovementRepository.findAll();
            for (StockMovement movement : allStockMovements) {
                if (itemCode.equals(movement.getItemCode()) && oldBarcode.equals(movement.getBarcode())) {
                    movement.setBarcode(newBarcode);
                    stockMovementRepository.save(movement);
                }
            }

            // Update barcode in SalesInvoiceRecord records where itemCode matches
            List<SalesInvoiceRecord> salesRecords = salesInvoiceRecordRepository.findAllByItemCode(itemCode);
            for (SalesInvoiceRecord record : salesRecords) {
                if (oldBarcode.equals(record.getBarcode())) {
                    record.setBarcode(newBarcode);
                    salesInvoiceRecordRepository.save(record);
                }
            }

            // Update barcode in PurchaseInvoiceRecord records where itemCode matches
            List<PurchaseInvoiceRecord> purchaseRecords = purchaseInvoiceRecordRepository.findAllByItemCode(itemCode);
            for (PurchaseInvoiceRecord record : purchaseRecords) {
                if (oldBarcode.equals(record.getBarcode())) {
                    record.setBarcode(newBarcode);
                    purchaseInvoiceRecordRepository.save(record);
                }
            }

            // Log the action
            MetaData barcodeUpdateType = metaDataService.searchMetaData("Updating Barcode", "Action");
            action = new Action();
            action.setType(barcodeUpdateType.getValue());
            action.setReference("Item: " + item.getItemName());
            action.setReference2("ItemCode: " + item.getItemCode());
            action.setRemark("Barcode updated from " + oldBarcode + " to " + newBarcode);
            actionService.save(action);

            return new Response(200, "Barcode updated successfully", item);
        } catch (Exception e) {
            LOGGER.error("Error updating barcode: " + e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new Response(500, "Error updating barcode: " + e.getMessage(), null);
        }
    }

    @Override
    @Transactional
    public Response updateItemProperties(String itemId, Boolean wholesale, Boolean retail, Boolean manageStock, Boolean active) {
        try {
            // Validate input
            if (itemId == null) {
                return new Response(400, "Item ID is required", false, "");
            }

            // Find the item
            Optional<Item> optionalItem = itemRepository.findById(itemId);
            if (!optionalItem.isPresent()) {
                return new Response(404, "Item not found", false, "");
            }

            Item item = optionalItem.get();

            // Update only the checkbox properties
            if (wholesale != null) {
                item.setWholesale(wholesale);
            }

            if (retail != null) {
                item.setRetail(retail);
            }

            if (manageStock != null) {
                item.setManageStock(manageStock);
            }

            if (active != null) {
                item.setActive(active);
            }

            // Save the updated item
            itemRepository.save(item);

            // Log the action
            MetaData updatePropertiesType = metaDataService.searchMetaData("Updating Item Properties", "Action");
            if (updatePropertiesType == null) {
                // If metadata doesn't exist, create a default one
                updatePropertiesType = new MetaData();
                updatePropertiesType.setValue("Updating Item Properties");
            }

            action = new Action();
            action.setType(updatePropertiesType.getValue());
            action.setReference("Item: " + item.getItemName());
            action.setReference2("ItemCode: " + item.getItemCode());
            action.setRemark("Updated properties - Wholesale: " + wholesale + ", Retail: " + retail +
                           ", Manage Stock: " + manageStock + ", Active: " + active);
            actionService.save(action);

            return new Response(200, "Item properties updated successfully", true, "");
        } catch (Exception e) {
            LOGGER.error("Error updating item properties: " + e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new Response(500, "Error updating item properties: " + e.getMessage(), false, null);
        }
    }

    @Override
    public Iterable<Item> findAllFiltered(
            Pageable pageable,
            String categoryId,
            String brandId,
            String modelId,
            String supplierId,
            Boolean wholesale,
            Boolean retail,
            Boolean manageStock,
            Boolean active,
            String sortBy,
            String sortDirection,
            String groupBy) {
        try {
            return customItemRepository.findAllFiltered(
                    pageable,
                    categoryId,
                    brandId,
                    modelId,
                    supplierId,
                    wholesale,
                    retail,
                    manageStock,
                    active,
                    sortBy,
                    sortDirection,
                    groupBy);
        } catch (Exception ex) {
            LOGGER.error("Error finding filtered items: " + ex.getMessage(), ex);
            return null;
        }
    }
}
