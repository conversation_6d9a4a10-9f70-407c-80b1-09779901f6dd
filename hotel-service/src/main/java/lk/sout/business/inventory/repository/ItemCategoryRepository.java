package lk.sout.business.inventory.repository;


import lk.sout.business.inventory.entity.ItemCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ItemCategoryRepository extends MongoRepository<ItemCategory, String> {

    List<ItemCategory> findItemCategoryByCategoryNameLikeIgnoreCaseAndActive(String name, Boolean b);

    ItemCategory findByCategoryNameIgnoreCase(String name);

    Page<ItemCategory> findAll(Pageable pageable);

    List<ItemCategory> findAll();

}
