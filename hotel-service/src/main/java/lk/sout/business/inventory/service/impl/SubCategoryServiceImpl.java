package lk.sout.business.inventory.service.impl;

import lk.sout.business.inventory.entity.ItemCategory;
import lk.sout.business.inventory.entity.SubCategory;
import lk.sout.business.inventory.repository.ItemCategoryRepository;
import lk.sout.business.inventory.repository.SubCategoryRepository;
import lk.sout.business.inventory.service.SubCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SubCategoryServiceImpl implements SubCategoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubCategoryServiceImpl.class);

    @Autowired
    SubCategoryRepository subCategoryRepository;

    @Autowired
    ItemCategoryRepository itemCategoryRepository;

    @Override
    public boolean save(SubCategory subCategory) {
        try {
            subCategory.setCode(String.valueOf(System.currentTimeMillis()));
            subCategoryRepository.save(subCategory);
            LOGGER.info("Sub Category saved. " + subCategory.getSubCategoryName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Sub Category Failed : " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<SubCategory> findAll(Integer page, Integer pageSize) {
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by("id").descending());
        return subCategoryRepository.findAll(pageable);
    }

    @Override
    public boolean remove(String id) {
        try {
            subCategoryRepository.deleteById(id);
            LOGGER.info("Sub Category removed. ");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Sub Category Failed :" + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<SubCategory> findByName(String name) {
        try {
            return subCategoryRepository.findBySubCategoryNameLikeIgnoreCaseAndActive(name, true);
        } catch (Exception e) {
            LOGGER.error("SubCategory Searching failed :" + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SubCategory> findByNameAndParent(String key, String catName) {
        try {
            ItemCategory category = itemCategoryRepository.findByCategoryNameIgnoreCase(catName);
            if (category != null) {
                return subCategoryRepository.findBySubCategoryNameLikeIgnoreCaseAndItemCategoryAndActive(key, category, true);
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("SubCategory Searching by parent failed :" + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SubCategory> findByCategoryId(String categoryId) {
        try {
            ItemCategory category = itemCategoryRepository.findById(categoryId).orElse(null);
            if (category != null) {
                return subCategoryRepository.findByItemCategoryAndActive(category, true);
            }
            return null;
        } catch (Exception e) {
            LOGGER.error("SubCategory Searching by category ID failed :" + e.getMessage());
            return null;
        }
    }

    @Override
    public SubCategory findById(String id) {
        try {
            return subCategoryRepository.findById(id).orElse(null);
        } catch (Exception e) {
            LOGGER.error("SubCategory Searching failed :" + e.getMessage());
            return null;
        }
    }

    @Override
    public int getCount() {
        return (int) subCategoryRepository.count();
    }
}
