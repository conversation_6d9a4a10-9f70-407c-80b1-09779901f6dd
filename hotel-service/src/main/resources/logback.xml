<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- Console Appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- File Appender -->
   <!-- <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/var/log/tomcat/generalWebSmartLife2.log</file> &lt;!&ndash; Log file location &ndash;&gt;
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/var/log/tomcat/archived/generalWebSmartLife2.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>5MB</maxFileSize> &lt;!&ndash; Max size before rolling &ndash;&gt;
            <totalSizeCap>0.05GB</totalSizeCap> &lt;!&ndash; Total size for archived logs &ndash;&gt;
            <maxHistory>30</maxHistory> &lt;!&ndash; Max number of historical logs to keep &ndash;&gt;
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>-->

    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
<!--        <appender-ref ref="FILE" />-->
    </root>

</configuration>
