/* You can add global styles to this file, and also import other style files */
.theme-color {
  color: #667eea;
}

.theme-color-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.theme-color-border {
  border: #667eea solid 2px;
}

.theme-color-border-thin {
  border: #667eea solid 1px;
}

.theme-color-border-bottom {
  border-bottom: #667eea solid 2px;
}

.theme-color-input-border-bottom {
  border-color: #667eea;
  border-width: 0 0 2px;
  outline: 0;
}

.cursor-pointer {
  cursor: pointer;
}

.border-right {
  border-right: #667eea solid 1px;
}

.table tr.active td {
  background-color: #46dad3 !important;
  color: white;
}

.font-helvetica{
  font-family: Helvetica Neue, Helvetica, Arial
}


input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.btn-theme {
  color: #ffffff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.btn-theme:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  color: #ffffff;
  border-color: #5a6fd8;
}

.btn-theme:focus, .btn-theme.focus {
  box-shadow: 0 0 0 .2rem rgba(102, 126, 234, 0.25);
}

.btn-theme.disabled, .btn-theme:disabled {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  opacity: 0.6;
}

.btn-theme:not(:disabled):not(.disabled):active, .btn-theme:not(:disabled):not(.disabled).active, .show > .btn-theme.dropdown-toggle {
  color: #fff;
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border-color: #5a6fd8;
}

.btn-theme:not(:disabled):not(.disabled):active:focus, .btn-theme:not(:disabled):not(.disabled).active:focus, .show > .btn-theme.dropdown-toggle:focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-outline-theme {
  color: #590c30;
  background-color: transparent;
  background-image: none;
  border-color: #590c30
}

.btn-outline-theme:hover {
  color: #fff;
  background-color: #590c30;
  border-color: #590c30
}

.btn-outline-theme:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-outline-theme.disabled, .btn-outline-primary:disabled {
  color: #590c30;
  background-color: transparent
}

.btn-outline-theme:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #590c30;
  border-color: #590c30
}

.btn-outline-theme:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-warning:hover{
  color: #590c30;
  background-color: #fff;
}

.ng-valid[required], .ng-valid.required  {
  border-left: 5px solid #42A948; /* green */
}

.ng-invalid:not(form).form-control {
  border-left: 5px solid #a94442; /* red */
}

/* Global Responsive Button Improvements for Low Resolution Displays */
@media (max-width: 1024px) {
  .btn-lg {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }

  .form-control-lg {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.9rem !important;
    height: auto !important;
  }

  /* Compact input group buttons */
  .input-group-append .btn,
  .input-group-prepend .btn {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.85rem !important;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  h1, .h1 {
    font-size: 1.75rem !important;
  }

  h2, .h2 {
    font-size: 1.5rem !important;
  }

  h3, .h3 {
    font-size: 1.25rem !important;
  }

  h4, .h4 {
    font-size: 1.1rem !important;
  }

  .form-control-lg {
    font-size: 0.85rem !important;
    padding: 0.25rem 0.5rem !important;
    height: auto !important;
  }

  .btn-lg {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
  }

  /* Smaller input group buttons on mobile */
  .input-group-append .btn,
  .input-group-prepend .btn {
    padding: 0.25rem 0.4rem !important;
    font-size: 0.8rem !important;
  }

  /* Navbar adjustments for mobile */
  .navbar {
    padding: 0.25rem 0.5rem;
  }

  .navbar-brand {
    margin-right: 0.5rem;
  }

  /* Modal adjustments for mobile */
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }

  .modal-header {
    padding: 0.5rem 1rem;
  }

  .modal-body {
    padding: 0.5rem;
    max-height: 80vh;
    overflow-y: auto;
  }

  .modal-footer {
    padding: 0.5rem;
  }
}

/* Very Small Screens */
@media (max-width: 480px) {
  .btn-lg {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.8rem !important;
    line-height: 1.2 !important;
  }

  .form-control-lg {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.8rem !important;
  }

  .input-group-append .btn,
  .input-group-prepend .btn {
    padding: 0.2rem 0.3rem !important;
    font-size: 0.75rem !important;
  }
}

/* Custom modal sizes */
.modal-xl {
  max-width: 95% !important;
  width: 95% !important;
  margin: 1.75rem auto; /* Add margin to ensure modal is not too tall */
}

/* Make sure modal content is scrollable */
.modal-xl .modal-content {
  max-height: 85vh;
  overflow-y: auto;
}

/* Ensure modal dialog has proper dimensions */
.modal-dialog {
  max-height: 90vh;
}

/* Fix for Bootstrap modal scrolling */
.modal-body {
  overflow-y: auto;
  max-height: calc(85vh - 120px); /* Adjust for header and footer */
}

/* Prevent nested scrollbars */
.modal-body .table-responsive {
  max-height: calc(85vh - 200px);
}

/* Ensure modal content has proper padding when opened as a modal */
.modal-content {
  padding: 0.5rem;
}

/* Ensure modal body has proper padding */
.modal-body {
  padding: 1rem;
}

/* Prevent body scrolling when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Horizontal checkbox layout */
.checkbox-container .d-flex {
  flex-wrap: wrap;
}

.checkbox-container .form-check {
  margin-right: 1.5rem;
}
