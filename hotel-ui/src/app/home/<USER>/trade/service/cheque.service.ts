import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})
export class ChequeService {

  constructor(private http: HttpClient) { }

  save(cheque) {
    return this.http.post(TradeConstants.SAVE_CHEQUE, cheque);
  }

  findAllPending(page, pageSize) {
    return this.http.get(TradeConstants.FIND_ALL_PENDING_CHEQUES, {
      params:{
        page: page,
        pageSize: pageSize
      }
    });
  }

  findByStatus(chequeStatusId) {
    return this.http.get(TradeConstants.FIND_ALL_CHEQUE_BY_STATUS, {params: {chequeStatusId: chequeStatusId}});
  }

  findByBank(bankId) {
    return this.http.get(TradeConstants.FIND_ALL_CHEQUE_BY_BANK, {params: {bankId: bankId}});
  }

  findAllByCustomer(customerId) {
    return this.http.get(TradeConstants.FIND_ALL_CHEQUE_BY_CUSTOMER, {params: {customerId: customerId}})
  }

  findAllBySupplier(supplierId) {
    return this.http.get(TradeConstants.FIND_ALL_CHEQUE_BY_SUPPLIER, {params: {supplierId: supplierId}})
  }

  findByChequeType(chequeType) {
    return this.http.get(TradeConstants.FIND_ALL_CHEQUE_BY_TYPE, {params: {chequeType: chequeType}})
  }

  findByPurchaseInvoiceNo(purchaseInvoiceNo) {
    return this.http.get(TradeConstants.FIND_ALL_CHEQUE_BY_PI_NO, {params: {purchaseInvoiceNo: purchaseInvoiceNo}})
  }


  updateCheque(id, comment, isDeposit) {
    return  this.http.get(TradeConstants.UPDATE_CHEQUE, {params: {
       id: id,
       comment: comment,
       isDeposit: isDeposit
      }});
  }

  loadAvailableCheques() {
    return this.http.get(TradeConstants.LOAD_AVAILABLE_CHEQUE_QTY);
  }
}
