import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {TradeConstants} from '../trade-constants';


@Injectable({
  providedIn: 'root'
})
export class QuotationService {

  constructor(private http: HttpClient) { }

  public save(quotation) {
    return this.http.post<any>(TradeConstants.SAVE_QUOTATION, quotation);
  }

  public findAll(page, pageSize) {
    return this.http.get(TradeConstants.GET_QUOTATION, {params: {page: page, pageSize: pageSize}});

  }
  public getAll () {
    return this.http.get(TradeConstants.FIND_QUOTATION);
  }

  public findAllByQuotationId(qId) {
    return this.http.get(TradeConstants.FIND_ALL_BY_QUOTATION_ID, {params: {qId: qId}});
  }

  public findByQuotationId(qId) {
    return this.http.get(TradeConstants.FIND_BY_QUOTATION_ID, {params: {qId: qId}});
  }

  public findAllByCustomer(customer) {
    return this.http.post<any>(TradeConstants.FIND_ALL_QUOTATION_BY_CUSTOMER, customer);
  }

  deleteQuotation(id) {
    return this.http.delete(TradeConstants.DELETE_QUOTATION,id);
  }

  convertToInvoice(id){
    return this.http.post(TradeConstants.CONVERT_TO_INVOICE, id);
  }
}
