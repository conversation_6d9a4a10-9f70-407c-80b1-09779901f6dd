import {Injectable} from '@angular/core';
import {TradeConstants} from '../trade-constants';
import {HttpClient} from "@angular/common/http";
import { Observable } from 'rxjs';
import { ItemSaleSummaryAggr } from '../model/itemSaleSummaryAggr';


@Injectable({
  providedIn: 'root'
})
export class SalesInvoiceRecordService {

  constructor(private http: HttpClient) {
  }

  public findBetween(sDate, eDate) {
    return this.http.get(TradeConstants.GET_SALES_INVOICE_RECORD_BETWEEN, {params: {sDate: sDate, eDate: eDate}});
  }

  /**
   * Find profit by range filter with option to filter by payment status
   * @param rangeId Range ID from metadata
   * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
   * @returns Observable of ItemSaleSummaryAggr array
   */
  public findByRangeFilter(rangeId: string, unrealized: boolean = false): Observable<ItemSaleSummaryAggr[]> {
    return this.http.get<ItemSaleSummaryAggr[]>(TradeConstants.GET_SALES_INVOICE_RECORD_BY_RANGE,
      {params: {rangeId: rangeId, unrealized: unrealized.toString()}});
  }

  /**
   * Find sales grouped by date between dates with option to filter by payment status
   * @param sDate Start date
   * @param eDate End date
   * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
   * @returns Observable of ItemSaleSummaryAggr array
   */
  findSalesGroupByDateBetween(sDate: string, eDate: string, unrealized: boolean = false): Observable<ItemSaleSummaryAggr[]> {
    return this.http.get<ItemSaleSummaryAggr[]>
    (TradeConstants.FIND_SALES_GROUP_BY_DATE_BETWEEN, {params: {sDate: sDate, eDate: eDate, unrealized: unrealized.toString()}})
  }

  /**
   * Find profit between dates with option to filter by payment status
   * @param sDate Start date
   * @param eDate End date
   * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
   * @returns Observable of profit amount
   */
  findProfitBetween(sDate: string, eDate: string, unrealized: boolean = false): Observable<ItemSaleSummaryAggr[]> {
    return this.http.get<ItemSaleSummaryAggr[]>
    (TradeConstants.FIND_PROFIT_BETWEEN, {params: {sDate: sDate, eDate: eDate, unrealized: unrealized.toString()}})
  }

  /**
   * Find unrealized profit between dates with option to filter by payment status
   * @param sDate Start date
   * @param eDate End date
   * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
   * @returns Observable of profit amount
   */
  findUnrealizedProfitBetween(sDate: string, eDate: string, unrealized: boolean = true): Observable<number> {
    return this.http.get<number>
    (TradeConstants.FIND_UNREALIZED_PROFIT_BETWEEN, {params: {sDate: sDate, eDate: eDate, unrealized: unrealized.toString()}})
  }

  // Removed findUnrealizedProfitByDateRange method - now using findProfitBetween with unrealized parameter

  findSalesItemGroupByDate(itemId, sDate, eDate){
    return this.http.get
    (TradeConstants.FIND_SALES_ITEM_GROUP_BY_DATE, {params: {itemId: itemId, sDate: sDate, eDate: eDate}})
  }

  /**
   * Find sales records by cashier (cash drawer) with option to filter by payment status
   * @param drawerNo Cash drawer number
   * @param sDate Start date (optional)
   * @param eDate End date (optional)
   * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
   * @returns Observable of ItemSaleSummaryAggr array
   */
  findSalesByCashier(drawerNo: string, sDate?: string, eDate?: string, unrealized: boolean = false): Observable<ItemSaleSummaryAggr[]> {
    const params: any = { drawerNo, unrealized: unrealized.toString() };

    if (sDate && eDate) {
      params.sDate = sDate;
      params.eDate = eDate;
    }

    return this.http.get<ItemSaleSummaryAggr[]>(TradeConstants.FIND_SALES_BY_CASHIER, { params });
  }

  /**
   * Find sales records by cashier user with option to filter by payment status
   * @param username Cashier username
   * @param sDate Start date (optional)
   * @param eDate End date (optional)
   * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
   * @returns Observable of ItemSaleSummaryAggr array
   */
  findSalesByUser(username: string, sDate?: string, eDate?: string, unrealized: boolean = false): Observable<ItemSaleSummaryAggr[]> {
    const params: any = { username, unrealized: unrealized.toString() };

    if (sDate && eDate) {
      params.sDate = sDate;
      params.eDate = eDate;
    }

    return this.http.get<ItemSaleSummaryAggr[]>(TradeConstants.FIND_SALES_BY_USER, { params });
  }

  /**
   * Find sales records by route with option to filter by payment status
   * @param routeNo Route number
   * @param sDate Start date (optional)
   * @param eDate End date (optional)
   * @param unrealized If true, find unrealized profit (non-paid), otherwise find realized profit (paid)
   * @returns Observable of ItemSaleSummaryAggr array
   */
  findSalesByRoute(routeNo: string, sDate?: string, eDate?: string, unrealized: boolean = false): Observable<ItemSaleSummaryAggr[]> {
    const params: any = { routeNo, unrealized: unrealized.toString() };

    if (sDate && eDate) {
      params.sDate = sDate;
      params.eDate = eDate;
    }

    return this.http.get<ItemSaleSummaryAggr[]>(TradeConstants.FIND_SALES_BY_ROUTE, { params });
  }

  /**
   * Find sales records by user and route
   * @param username Cashier username
   * @param routeNo Route number
   * @param sDate Start date (optional)
   * @param eDate End date (optional)
   * @returns Observable of ItemSaleSummaryAggr array
   */
  findSalesByUserAndRoute(username: string, routeNo: string, sDate?: string, eDate?: string): Observable<ItemSaleSummaryAggr[]> {
    const params: any = { username, routeNo };

    if (sDate && eDate) {
      params.sDate = sDate;
      params.eDate = eDate;
    }

    return this.http.get<ItemSaleSummaryAggr[]>(TradeConstants.FIND_SALES_BY_USER_AND_ROUTE, { params });
  }
}
