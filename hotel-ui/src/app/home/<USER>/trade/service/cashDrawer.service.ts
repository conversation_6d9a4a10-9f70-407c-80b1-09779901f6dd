import { Injectable } from '@angular/core';
import { HttpClient } from "@angular/common/http";
import { TradeConstants } from "../trade-constants";
import { Observable } from 'rxjs';
import { CashDrawer } from '../model/cashDrawer';

@Injectable({
  providedIn: 'root'
})
export class CashDrawerService {

  constructor(private http: HttpClient) {
  }

  findCashDrawerByDrawerNo(drawerNo): Observable<CashDrawer> {
    return this.http.get<CashDrawer>(TradeConstants.FIND_CASH_DRAWER_BY_DRAWER_NO, {params: {drawerNo: drawerNo}});
  }

  /**
   * Find all cashier counters
   * @returns Observable of CashDrawer array
   */
  findAllCashDrawers(): Observable<CashDrawer[]> {
    return this.http.get<CashDrawer[]>(TradeConstants.FIND_ALL_CASH_DRAWERS);
  }

  /**
   * Find all active cash drawers
   * @returns Observable of CashDrawer array
   */
  findAllActive(): Observable<CashDrawer[]> {
    return this.findAllCashDrawers();
  }

  /**
   * Find cash drawer status by drawer no
   * @returns Observable of CashDrawer array
   */
  findCashDrawerStatus(drawerNo): Observable<CashDrawer[]> {
    return this.http.get<CashDrawer[]>(TradeConstants.CHECK_CASH_DRAWER_NO_STATUS, {params: {drawerNo: drawerNo}});
  }
}
