<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">Payment History</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
    <div class="row mt-2">
      <div class="col-md-12">
        <label class="font-weight-bold">Invoice Created Date : </label>
        <label class="ml-3">{{invoice.date | date}}</label>
      </div>
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Date</th>
          <th scope="col">Invoice No</th>
          <th scope="col">Amount</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let tr of transactions,let i = index" align="center">
          <td>{{tr.date | date:'short': '+530'}}</td>
          <td>{{tr.refNo}}</td>
          <td>{{tr.amount | number : '1.2-2'}}</td>
        </tr>
        </tbody>
      </table>
  </div>
