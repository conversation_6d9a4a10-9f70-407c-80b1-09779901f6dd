import { Component, OnInit } from '@angular/core';
import { InvoiceModeService } from '../../service/invoice-mode.service';

@Component({
  selector: 'app-invoice-router',
  template: '<div class="text-center mt-5">' +
    '<div class="spinner-border" role="status">' +
    '<span class="sr-only">Loading...</span>' +
    '</div><' +
    'p class="mt-3">Redirecting to invoice creation...</p>' +
    '</div>'
})
export class InvoiceRouterComponent implements OnInit {
  constructor(private invoiceModeService: InvoiceModeService) { }

  ngOnInit(): void {
    // Redirect to the appropriate invoice creation component
    this.invoiceModeService.navigateToInvoiceCreation();
  }
}
