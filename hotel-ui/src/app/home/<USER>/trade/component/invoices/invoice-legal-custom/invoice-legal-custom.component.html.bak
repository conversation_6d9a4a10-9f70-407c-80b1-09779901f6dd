<div id="print-section" style="width: 215.9mm; height: auto">
  <div style="margin-top: 23mm; right: 4mm; font-family: Courier; font-size: 7pt; width: 100%">
    <table style="width: 100%">
      <tr>
        <td colspan="18">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="19" style="text-align: right"> {{invoice.invoiceNo}}</td>
      </tr>
      <tr>
        <td *ngIf="invoice.customerName !== 'Default Customer'" colspan="18"> {{invoice.customerName}}</td>
        <td *ngIf="invoice.customerName == 'Default Customer'" colspan="18"> {{'Default'}}</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="19" style="text-align: right"> {{invoice.date | date:'medium': '+530'}}</td>
      </tr>
      <tr>
        <td colspan="18">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="19" style="text-align: right">
          {{invoice.reference}}
        </td>
      </tr>
      <tr>
        <td *ngIf="invoice.customerName !== 'Default Customer'" colspan="18">
          {{'Tel - ' +invoice.customer.telephone1}}
        </td>
        <td *ngIf="invoice.customerName == 'Default Customer'" colspan="18"></td>
        <td colspan="6">&nbsp;</td>
        <td colspan="19">&nbsp;</td>
      </tr>
      <tr>
        <td colspan="18">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="19"></td>
      </tr>
      <tr *ngFor="let rec of invoice.salesInvoiceRecords; index as i">
        <td [colSpan]="18" style="text-align: left">{{rec.barcode != 0 ? rec.itemName : rec.itemName}}</td>
        <td [colSpan]="10" style="text-align: right">{{rec.quantity}}</td>
        <td [colSpan]="6" style="text-align: right">{{rec.unitPrice | number : '1.2-2'}}</td>
        <td [colSpan]="9" style="text-align: right">{{rec.price | number : '1.2-2'}}</td>
      </tr>
    </table>

  </div>

  <div style="position:fixed; bottom: 12mm; right: 4mm; font-family: Courier; font-size: 7pt; width: 100%;">
    <table style="width: 100%">
      <tr>
        <td colspan="18">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="4">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="9" style="text-align: right">{{invoice.totalAmount | number : '1.2-2'}}</td>
      </tr>
      <tr>
        <td colspan="18">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="4">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="9" style="text-align: right">{{invoice.payment | number : '1.2-2'}}</td>
      </tr>
      <tr>
        <td colspan="18">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="4">&nbsp;</td>
        <td colspan="6">&nbsp;</td>
        <td colspan="9" style="text-align: right">{{invoice.balance | number : '1.2-2'}}</td>
      </tr>
    </table>
  </div>
</div>

<div>
  <button *ngIf="!useSilentPrint" class="btn btn-primary text-right m-4" printSectionId="print-section" ngxPrint>
    Print
  </button>
  <button class="btn btn-theme text-right m-4" (click)="printSilently()">
    Silent Print
  </button>
</div>
