import {AfterViewInit, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {SalesInvoice} from "../../../model/sales-invoice";
import {SalesInvoiceService} from "../../../service/sales-invoice.service";
import {Customer} from "../../../model/customer";
import {Company} from "../../../../../core/model/company";
import {CompanyService} from "../../../../../core/service/company.service";
import {DomSanitizer} from "@angular/platform-browser";
import {SilentPrintService} from "../../../service/silent-print.service";
import {environment} from "../../../../../../../environments/environment";
import {BsModalRef} from "ngx-bootstrap/modal";

@Component({
  selector: 'app-invoice-58',
  templateUrl: './invoice58-en.component.html',
  styleUrls: ['./invoice58-en.component.css']
})
export class Invoice58EnComponent implements OnInit {

  invoice: SalesInvoice;
  invoiceNo: string;
  date: Date;
  existingCss: boolean;
  paymentStatus: string;
  company: Company;
  imageFile: any;
  user: string;
  pastBill: boolean;
  environment = environment;
  customFooterText: string = '';

  // Modal reference for when this component is opened as a modal
  modalRef: BsModalRef;

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  @ViewChild('printBtn') printBtn: ElementRef;

  constructor(private salesInvoiceService: SalesInvoiceService,
              private companyService: CompanyService,
              private sanitizer: DomSanitizer,
              private silentPrintService: SilentPrintService) {
  }

  ngOnInit(): void {
    this.invoice = new SalesInvoice();
    this.company = new Company();
    // Initialize customer-related properties
    this.invoice.customerNo = '';
    this.invoice.customerName = '';
    this.date = new Date(Date.now());
    this.existingCss = true;
    this.pastBill = false;
    this.user = JSON.parse(localStorage.getItem('currentUser')).user.firstName;
    this.loadCustomFooterText();
    this.findCompany();

    // Check if this component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  /**
   * Load custom footer text from localStorage
   */
  loadCustomFooterText(): void {
    try {
      const storedSettings = localStorage.getItem('app_settings');
      if (storedSettings) {
        const settings = JSON.parse(storedSettings);
        this.customFooterText = settings.customInvoiceFooter || '';
      }
    } catch (error) {
      console.error('Error loading custom footer text:', error);
      this.customFooterText = '';
    }
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
      this.imageFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + this.company.logo);
    });
  }

  findInvoice() {
    this.findCompany();
    this.salesInvoiceService.findByInvoiceNo(this.invoiceNo).subscribe((result: SalesInvoice) => {
      this.invoice = result;
      this.date = new Date(this.invoice.date);
      if (this.invoice.status.value == 'Paid') {
        this.paymentStatus = 'Cash';
      } else {
        this.paymentStatus = 'Credit';
      }

      // If not viewing a past bill, print automatically without showing dialog
      if (!this.pastBill) {
        setTimeout(() => {
          this.printSilently();
        }, 500);
      }
    })
  }

  /**
   * Print the invoice silently without showing the print dialog
   * Note: This requires Chrome to be launched with --kiosk-printing flag
   */
  printSilently() {
    this.silentPrintService.printElementSilently('print-section');
  }
}
