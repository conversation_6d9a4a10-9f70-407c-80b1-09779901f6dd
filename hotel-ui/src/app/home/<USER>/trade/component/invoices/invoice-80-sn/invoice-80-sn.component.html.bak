<!-- Modal close button when in modal mode -->
<div *ngIf="isModal" class="d-flex justify-content-end mb-2">
  <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
    <i class="fa fa-times"></i>
  </button>
</div>

<div id="print-section" style="padding-left:2vw; padding-right:2vw; font-family: Tahoma !important;
     width: 80mm; height: auto">

  <address style="text-align: center; padding-top: 0px">
    <p style="font-weight: bold; font-size: 20px; padding: 0px;
    margin-bottom: 10px; font-style: normal;">{{ company.name }}</p>
    <p
      style="font-size: 15px; margin-bottom: 10px;  font-style: normal;">{{ company.fullAddress }}</p>
    <p
      style="font-size: 15px; margin-bottom: 10px;  font-style: normal;">{{ company.telephone1 + ' / ' + company.telephone2 }}</p>
  </address>

  <div style="width: 100%">
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 10px; margin-bottom: 10px; ">
    <table style="font-size: 15px; width: 100%">
      <td style="text-align: left">{{ date | date:'dd/MM/yyyy, hh:mm aa' }}</td>
      <td style="text-align: right">{{ invoice.invoiceNo }}</td>
    </table>
    <table style="font-size: 15px; width: 100%">
      <tr>
        <td style="text-align: left">{{ user }}</td>
        <td style="text-align: right">{{ invoice.customerName }}</td>
      </tr>
    </table>
  </div>

  <div>
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 10px; margin-top: 0px;">
    <table style="width: 100%; font-size: 11px;">
      <thead>
      <tr style="padding: 0px; margin: 0px">
        <td style="padding: 0.2rem;padding: 0px; margin: 0px">#</td>
        <th style="padding: 0.2rem;padding: 0px; margin: 0px; text-align: left;"></th>
        <th style="padding: 0.2rem">සඳහන් මිල</th>
        <th style="padding: 0.2rem">අපේ මිල</th>
        <th style="padding: 0.2rem">ප්‍රමාණය</th>
        <th style="padding: 0.2rem">එකතුව</th>
      </tr>
      </thead>
    </table>
    <div *ngFor="let rec of invoice.salesInvoiceRecords; index as i"
         style="font-size: 13px; width: 100%; display: inline-block;margin: 0.3rem; margin-left: 0px !important;">
      <div style="text-align: left;width: 5%;float: left">{{ i + 1 }}.</div>
      <div style="text-align: left;width: 95%;float: left;">{{ rec.itemName }}</div>
      <div
        style="text-align: right; margin-right: 5%;width: 20%;float: left;">{{ rec.unitPriceOriginal | number : '1.2-2' }}
      </div>
      <div style="text-align: right;width: 25%;float: left;">{{ rec.unitPrice | number : '1.2-2' }}</div>
      <div style="text-align: right;width: 20%;float: left;">{{ rec.quantity }}</div>
      <div style="text-align: right;width: 28%;float: left;">{{ rec.price | number : '1.2-2' }}</div>
    </div>
  </div>

  <hr style="background-color: #fff; border-top: 2px dashed #000000;">

  <div style="margin-top: 0px; width: 100%">
    <div style="font-size: 13px; display: grid; grid-template-columns: 8fr 4fr; grid-template-rows: 1fr">
      <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
        <li style="margin-bottom: 5px;  font-weight: bold;">ගෙවීම්</li>
        <li style="margin-bottom: 5px;  font-weight: bold;">එකතුව</li>
        <li style="margin-bottom: 5px;  font-weight: bold;" *ngIf="invoice.totalDiscount > 0">වට්ටම</li>
        <li style="margin-bottom: 5px;  font-weight: bold;" *ngIf="invoice.totalDiscount > 0">මුළු මුදල</li>
        <li *ngIf="invoice.balance <= 0" style="margin-bottom: 5px;  font-weight: bold;">ඉතිරි මුදල</li>
        <li *ngIf="invoice.balance > 0" style="margin-bottom: 5px;  font-weight: bold;">ඉතිරි මුදල</li>
      </ul>
      <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
        <li
          style="margin-bottom: 5px; font-weight: bold;">{{
            (invoice.cashBalance > 0 ? invoice.payment +
              invoice.cashBalance : invoice.payment) | number : '1.2-2'
          }}
        </li>
        <li
          style="margin-bottom: 5px; font-weight: bold;">{{ invoice.totalAmount | number : '1.2-2' }}
        </li>
        <li
          style="margin-bottom: 5px; font-weight: bold;"
          *ngIf="invoice.totalDiscount > 0">{{ invoice.totalDiscount | number : '1.2-2' }}
        </li>
        <li
          style="margin-bottom: 5px; font-weight: bold;"
          *ngIf="invoice.totalDiscount > 0">{{ invoice.totalAmount | number : '1.2-2' }}
        </li>
        <li *ngIf="invoice.balance == 0"
            style="margin-bottom: 5px; font-weight: bold;">{{ invoice.cashBalance | number : '1.2-2' }}
        </li>
        <li *ngIf="invoice.balance > 0"
            style="margin-bottom: 5px; font-weight: bold;">{{ invoice.balance | number : '1.2-2' }}
        </li>
      </ul>
    </div>
  </div>
  <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 0px; margin-top: 0px;">
  <div style="margin-top: 1px; width: 100%; text-align: center">
    <p style="font-weight: bold; font-size: 18px; margin: 3px;">ඔබේ ලාභය රු: {{ invRecDiscount | number : '1.2-2' }} යි</p>
    <p style="text-align: center; font-style: normal; font-size: 0.6rem;
             font-weight: bold; margin-bottom: 5px;">ස්තුතියි. නැවත එන්න</p>
  </div>
  <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 0px; margin-top: 0px;">
  <div style="overflow: hidden;">
    <span style="float: left; font-style: normal; font-size: 0.5rem; margin-top: 1px; font-weight: bold;">viganana.com</span>
    <span style="float: right; font-style: normal; font-size: 0.5rem; margin-top: 1px; font-weight: bold;">071 97 98 99 9</span>
  </div>

</div>

<div>
  <button *ngIf="!useSilentPrint" class="btn btn-primary text-right m-4" printSectionId="print-section" #printBtn
          ngxPrint="useExistingCss">Print
  </button>
  <button class="btn btn-theme text-right m-4" (click)="printSilently()">
    Silent Print
  </button>
</div>
