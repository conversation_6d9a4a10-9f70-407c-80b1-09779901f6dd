<!-- Modal close button when in modal mode -->
<div *ngIf="isModal" class="d-flex justify-content-end mb-2">
  <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
    <i class="fa fa-times"></i>
  </button>
</div>

<div id="print-section" style="padding-left: 3%; padding-right: 3%; font-family: Tahoma !important;
     width: 58mm; height: auto">

  <address style="text-align: center; padding-top: 0px">
    <p style="font-weight: bold; font-size: 16px; padding: 0px;
    margin-bottom: 8px; font-style: normal;">{{company.name}}</p>
    <p
      style="font-size: 12px; margin-bottom: 8px; font-style: normal;">{{company.fullAddress}}</p>
    <p
      style="font-size: 12px; margin-bottom: 8px; font-style: normal;">{{company.telephone1 + ' / ' + company.telephone2}}</p>
  </address>

  <div style="width: 100%">
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 8px; margin-bottom: 8px; ">
    <table style="font-size: 12px; width: 100%">
      <td style="text-align: left">{{date | date:'dd/MM/yyyy, hh:mm aa'}}</td>
      <td style="text-align: right">{{invoice.invoiceNo}}</td>
    </table>
    <table style="font-size: 12px; width: 100%">
      <tr>
        <td style="text-align: left">{{user}}</td>
        <td style="text-align: right">{{invoice.customerName}}</td>
      </tr>
    </table>
  </div>

  <div>
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 8px; margin-top: 0px;">
    <table style="width: 100%; font-size: 11px;">
      <thead>
      <tr style="padding: 0px; margin: 0px">
        <th style="padding: 0.1rem; width: 10%"></th>
        <th style="padding: 0.1rem; width: 20%">Qty</th>
        <th style="padding: 0.1rem; width: 23%">L Pri</th>
        <th style="padding: 0.1rem; width: 23%">Pri</th>
        <th style="padding: 0.1rem; width: 34%;text-align: right">Amount</th>
      </tr>
      </thead>
    </table>
    <div *ngFor="let rec of invoice.salesInvoiceRecords; index as i"
         style="font-size: 12px; width: 100%; display: inline-block; margin: 0.2rem; margin-left: 0px !important;">
      <div style="text-align: left; width: 8%; float: left;">#</div>
      <div style="text-align: left; width: 92%; float: left;">{{rec.itemName}}</div>
      <div style="text-align: right; width: 20%; float: left;">{{rec.quantity}}</div>
      <div style="text-align: right; width: 23%; float: left;">{{(rec.unitPriceOriginal) | number : '1.2-2'}}</div>
      <div style="text-align: right; width: 23%; float: left;">{{(rec.unitPrice) | number : '1.2-2'}}</div>
      <div style="text-align: right; width: 34%; float: left;">{{rec.price | number : '1.2-2'}}</div>
    </div>
  </div>

  <hr style="background-color: #fff; border-top: 2px dashed #000000;">

  <div style="margin-top: 8px; width: 100%">
    <!-- Using a table instead of grid for better alignment -->
    <table style="width: 100%; font-size: 11px;">
      <tr>
        <td style="text-align: right; padding-right: 8px; font-weight: bold; white-space: nowrap;">Sub Total</td>
        <td style="text-align: right; font-weight: bold;">{{invoice.subTotal | number : '1.2-2'}}</td>
      </tr>
      <tr>
        <td style="text-align: right; padding-right: 8px; font-weight: bold; white-space: nowrap;">Discount</td>
        <td style="text-align: right; font-weight: bold;">{{invoice.totalDiscount | number : '1.2-2'}}</td>
      </tr>
      <tr>
        <td style="text-align: right; padding-right: 8px; font-weight: bold; white-space: nowrap;">Total</td>
        <td style="text-align: right; font-weight: bold;">{{invoice.totalAmount | number : '1.2-2'}}</td>
      </tr>
      <tr>
        <td style="text-align: right; padding-right: 8px; font-weight: bold; white-space: nowrap;">Payment</td>
        <td style="text-align: right; font-weight: bold;">{{invoice.cashBalance > 0 ? invoice.payment + invoice.cashBalance : invoice.payment | number : '1.2-2'}}</td>
      </tr>
      <tr *ngIf="invoice.balance <= 0">
        <td style="text-align: right; padding-right: 8px; font-weight: bold; white-space: nowrap;">Cash Balance</td>
        <td style="text-align: right; font-weight: bold;">{{invoice.cashBalance | number : '1.2-2'}}</td>
      </tr>
      <tr *ngIf="invoice.balance > 0">
        <td style="text-align: right; padding-right: 8px; font-weight: bold; white-space: nowrap;">Balance Due</td>
        <td style="text-align: right; font-weight: bold;">{{invoice.balance | number : '1.2-2'}}</td>
      </tr>
    </table>
  </div>
  <p style="text-align: center; font-style: normal; font-size: 0.45rem;
             font-weight: bold; margin-bottom: 4px; margin-top: 8px;">Thank You. Come Again</p>

  <!-- Custom Footer Text (only show if there's content) -->
  <p *ngIf="customFooterText && customFooterText.trim()"
     style="text-align: center; font-style: normal; font-size: 0.4rem;
            font-weight: normal; margin-bottom: 4px; margin-top: 4px;">{{customFooterText}}</p>

  <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 0px; margin-top: 0px;">
  <div style="overflow: hidden; margin-top: 1vh">
    <span style="float: left; font-style: normal; font-size: 0.04rem; margin-top: 1px; font-weight: bold;">viganana.com</span>
    <span style="float: right; font-style: normal; font-size: 0.04rem; margin-top: 1px; font-weight: bold;">071 97 98 99 9</span>
  </div>

</div>

<div>
  <button class="btn btn-primary text-right m-4" printSectionId="print-section" #printBtn
          ngxPrint="useExistingCss">Print
  </button>
</div>
