<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">Day Close</h2>
    <button *ngIf="isModal" type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
      <i class="fa fa-times"></i>
    </button>
  </div>
    <div class="row">
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Date</label>
        <input class="form-control" name="date" [(ngModel)]="date" >
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Opening Amount</label>
        <input class="form-control" [ngModel]="cashier.openingBalance" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Current Amount</label>
        <input class="form-control" [ngModel]="cashier.currentBalance" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Actual Amount</label>
        <input type="number" class="form-control" name="actualAmount" [(ngModel)]="actualAmount" (keyup)="calculateBalance()" >
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Withdrawal Amount</label>
        <input type="number" class="form-control" name="withdrawalAmount" [(ngModel)]="withdrawalAmount"
               (keyup)="calculateBalance()">
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Balance</label>
        <input type="number" class="form-control" [ngModel]="balance" readonly>
      </div>
    </div>

  <!-- Desktop buttons -->
  <div class="row mt-3 text-right d-none d-md-flex">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger ml-2">
          Clear
        </button>
        <button type="button" class="btn btn-theme ml-2" mwlConfirmationPopover (confirm)="save()">
          Save
        </button>
      </div>
  </div>

  <!-- Mobile buttons -->
  <div class="d-md-none mt-3">
    <div class="row">
      <div class="col-6">
        <button type="button" class="btn btn-danger btn-block">
          <i class="fa fa-refresh"></i> Clear
        </button>
      </div>
      <div class="col-6">
        <button type="button" class="btn btn-theme btn-block" mwlConfirmationPopover (confirm)="save()">
          <i class="fa fa-save"></i> Save
        </button>
      </div>
    </div>
  </div>

