import {Component, OnInit} from '@angular/core';
import {CashDrawer} from "../../../model/cashDrawer";
import {User} from "../../../../../admin/model/user";
import {CashDrawerService} from "../../../service/cashDrawer.service";
import {CashRecordService} from "../../../service/cash-record.service";
import {NotificationService} from "../../../../../core/service/notification.service";
import {BsModalRef} from "ngx-bootstrap/modal";

@Component({
  selector: 'app-day-start',
  templateUrl: './day-start.component.html',
  styleUrls: ['./day-start.component.css']
})
export class DayStartComponent implements OnInit {

  cashier: CashDrawer;
  counterNo: string;
  date: Date;
  user: User;
  addingAmount: number;
  totalAmount: number;
  modalRef: BsModalRef;
  disableStartButton: boolean;
  isModal: boolean = false;


  constructor(private cashierService: CashDrawerService,
              private cashRecordService: CashRecordService,
              private notificationService: NotificationService,
              private modalService: BsModalRef) {
  }

  ngOnInit(): void {
    this.cashier = new CashDrawer();
    this.date = new Date();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.disableStartButton = false;
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  findCashier(counterNo: string) {
    this.cashierService.findCashDrawerByDrawerNo(counterNo).subscribe((data: CashDrawer) => {
      this.cashier = data;
    });
  }

  calculateTotalAmount() {
    if(this.addingAmount >= 0 && this.addingAmount !== null){
      this.disableStartButton = true;
    }
    this.totalAmount = this.cashier.currentBalance + this.addingAmount;
  }

  save() {
    this.cashRecordService.dayStart(this.addingAmount).subscribe(val => {
      if (val) {
        this.notificationService.showSuccess("Day started, Have a Great One..!");
        this.modalRef.hide();
      } else {
        this.notificationService.showError("Something Went Wrong");
      }
    })
  }

  clear() {
    this.modalRef.hide()
  }
}
