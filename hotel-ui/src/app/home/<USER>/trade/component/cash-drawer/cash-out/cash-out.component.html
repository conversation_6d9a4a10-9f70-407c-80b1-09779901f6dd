<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">Cash Out</h2>
    <button *ngIf="isModal" type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
      <i class="fa fa-times"></i>
    </button>
  </div>
    <div class="row">
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Counter</label>
        <input class="form-control" [ngModel]="cashier.drawerNo" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Available Amount</label>
        <input type="number" class="form-control" [ngModel]="cashier.currentBalance" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Withdraw Amount</label>
        <input type="number" class="form-control" name="withdrawAmount" [(ngModel)]="withdrawingAmount">
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Purpose</label>
        <div class="input-group">
          <select class="form-control" (change)="setSelectedPurpose()" name="withdrawPurposeSelect"
                  [(ngModel)]="purposeId" required #withdrawPurposeSelect="ngModel"
                  [class.is-invalid]="withdrawPurposeSelect.invalid && withdrawPurposeSelect.touched">
            <option [value]="undefined">-Select Purpose-</option>
            <option *ngFor="let typ of purposes" [value]="typ.id">
              {{typ.value}}
            </option>
          </select>
        </div>
      </div>

    </div>  <!-- Desktop buttons -->
  <div class="row mt-3 text-right d-none d-md-flex">
      <div class="col-md-12">
        <button type="button" class="btn btn-warning ml-2" (click)="clear()">
          Clear
        </button>
        <button type="button" class="btn btn-danger ml-2" (click)="withdraw()" [disabled]="disableWithdraw">
          Withdraw
        </button>
      </div>
  </div>

  <!-- Mobile buttons -->
  <div class="d-md-none mt-3">
    <div class="row">
      <div class="col-6">
        <button type="button" class="btn btn-warning btn-block" (click)="clear()">
          <i class="fa fa-refresh"></i> Clear
        </button>
      </div>
      <div class="col-6">
        <button type="button" class="btn btn-danger btn-block" (click)="withdraw()" [disabled]="disableWithdraw">
          <i class="fa fa-minus-circle"></i> Withdraw
        </button>
      </div>
    </div>
  </div>

