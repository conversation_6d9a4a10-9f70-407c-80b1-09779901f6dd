import {Component, OnInit} from '@angular/core';
import {CashDrawer} from "../../../model/cashDrawer";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {DayStartComponent} from "../day-start/day-start.component";
import {CashDrawerService} from "../../../service/cashDrawer.service";
import {User} from "../../../../../admin/model/user";
import {CashInComponent} from "../cash-in/cash-in.component";
import {CashOutComponent} from "../cash-out/cash-out.component";
import {DayCloseComponent} from "../day-close/day-close.component";

@Component({
  selector: 'app-view-cash-drawer',
  templateUrl: './view-cash-drawer.component.html',
  styleUrls: ['./view-cash-drawer.component.css']
})
export class ViewCashDrawerComponent implements OnInit {

  cashier: CashDrawer;
  modalRefDayStart: BsModalRef;
  modalRefAddCash: BsModalRef;
  modalWithdrawCash: BsModalRef;
  modalDayEnd: BsModalRef;

  // Modal reference for when this component is opened as a modal
  modalRef: BsModalRef;

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  user: User;
  date: Date;
  disableDayStart: boolean;
  disableDayClose: boolean;
  disableAddCash: boolean;
  disableWithdrawCash: boolean;
  counter: string;

  constructor(private modalService: BsModalService,
              private cashierService: CashDrawerService) {
  }

  ngOnInit(): void {
    this.disableDayStart = true;
    this.disableAddCash = true;
    this.disableWithdrawCash = true;
    this.disableDayClose = true;
    this.cashier = new CashDrawer();
    this.cashier.drawerNo = "1";
    this.user = new User();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.date = new Date();
    this.findCashier();
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  findCashier() {
    this.cashierService.findCashDrawerByDrawerNo(this.cashier.drawerNo).subscribe((data: CashDrawer) => {
      this.cashier = data;
      this.handleButtons();
    });
  }

  handleButtons() {
    if ((this.cashier.active) && (new Date(this.cashier.lastStartedDate).toLocaleDateString() === new Date().toLocaleDateString())) {
      this.disableWithdrawCash = false;
      this.disableAddCash = false;
    } else {
      this.disableWithdrawCash = true;
      this.disableAddCash = true;
    }

    if ((this.cashier.active) && (new Date(this.cashier.lastClosedDate) < new Date())) {
      this.disableDayClose = false;
    } else {
      this.disableDayClose = true;
    }

    if ((!this.cashier.active) && (new Date(this.cashier.lastClosedDate) < new Date())) {
      this.disableDayStart = false;
    } else {
      this.disableDayStart = true;
    }
  }

  openDayStart() {
    const initialState = {
      isModal: true
    };
    this.modalRefDayStart = this.modalService.show(DayStartComponent, <ModalOptions>{
      class: 'modal-md',
      initialState: initialState
    });
    this.modalRefDayStart.content.modalRef = this.modalRefDayStart;
    this.modalRefDayStart.content.findCashier(this.cashier.drawerNo);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }

  addCash() {
    const initialState = {
      isModal: true
    };
    this.modalRefAddCash = this.modalService.show(CashInComponent, <ModalOptions>{
      class: 'modal-md',
      initialState: initialState
    });
    this.modalRefAddCash.content.modalRef = this.modalRefAddCash;
    this.modalRefAddCash.content.findCashier(this.cashier.drawerNo);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }

  withdrawCash() {
    const initialState = {
      isModal: true
    };
    this.modalWithdrawCash = this.modalService.show(CashOutComponent, <ModalOptions>{
      class: 'modal-md',
      initialState: initialState
    });
    this.modalWithdrawCash.content.modalRef = this.modalWithdrawCash;
    this.modalWithdrawCash.content.findCashier(this.cashier.drawerNo);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }


  dayEnd() {
    const initialState = {
      isModal: true
    };
    this.modalDayEnd = this.modalService.show(DayCloseComponent, <ModalOptions>{
      class: 'modal-md',
      initialState: initialState
    });
    this.modalDayEnd.content.modalRef = this.modalDayEnd;
    this.modalDayEnd.content.findCashier(this.cashier.drawerNo);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }


}
