<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">MANAGE SUPPLIERS</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="row">
    <div class="col-12 col-md-6 mb-4 mb-md-0">
      <div class="row mb-3">
        <div class="col-12">
          <label class="form-label d-block d-md-none">Search Supplier</label>
          <input [(ngModel)]="keySupplier"
                 [typeahead]="suppliers"
                 (typeaheadLoading)="loadSuppliers()"
                 (typeaheadOnSelect)="setSelectedSupplier($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="Search Supplier"
                 autocomplete="off"
                 required
                 class="form-control" name="supplier">
        </div>
      </div>
      <div class="row">
        <div class="col-12 table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-light text-center">
            <tr>
              <th scope="col">Supplier Name</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let supplier of suppliers,let i=index" (click)="supplierDetail(supplier)"
                [class.active]="i === selectedRow" class="text-center">
              <td>{{ supplier.name }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <pagination class="pagination-sm justify-content-center" [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-6">
      <form (ngSubmit)="saveSupplier(); manageSuppliers.reset()" #manageSuppliers="ngForm">
        <div class="row g-3">
          <div class="col-12 col-sm-6 form-group">
            <label>Supplier Name</label>
            <input type="text" required #sName="ngModel" [class.is-invalid]="sName.invalid && sName.touched"
                   class="form-control" id="sName" [(ngModel)]="supplier.name" name="sName"
                   placeholder="Supplier Name">
            <div *ngIf="sName.errors && (sName.invalid || sName.touched)">
              <small class="text-danger" [class.d-none]="sName.valid || sName.untouched">*Supplier Name is required
              </small>
            </div>
          </div>
          <div class="col-12 col-sm-6 form-group">
            <label>Registration Number</label>
            <input type="text" #reg="ngModel"
                   class="form-control" id="reg" [(ngModel)]="supplier.regNo" name="reg"
                   placeholder="Registration Number (Optional)">
          </div>
        </div>
        <div class="row g-3 mt-1">
          <div class="col-12 form-group">
            <label>Address</label>
            <input type="text"
                   class="form-control" id="address1" name="address1" #address1="ngModel"
                   placeholder="Address 1" [(ngModel)]="supplier.address"
                   [class.is-invalid]="address1.invalid && address1.touched">
            <div *ngIf="address1.errors && (address1.invalid || address1.touched)">
              <small class="text-danger" [class.d-none]="address1.valid || address1.untouched">*address is required
              </small>
            </div>
          </div>
        </div>
        <div class="row g-3 mt-1">
          <div class="col-12 col-sm-6 form-group">
            <label>Telephone Number 1</label>
            <input type="text" class="form-control" placeholder="Telephone 1" required #phone1="ngModel" name="phone1"
                   id="phone1"
                   [(ngModel)]="supplier.telephone1" class="form-control" pattern="^\d{10}$"
                   [class.is-invalid]="phone1.invalid && phone1.touched">
            <div *ngIf="phone1.errors && (phone1.invalid || phone1.touched)">
              <small class="text-danger" [class.d-none]="phone1.valid || phone1.untouched">*Telephone number is
                required
              </small>
            </div>

          </div>
          <div class="col-12 col-sm-6 form-group">
            <label>Telephone Number 2</label>
            <input type="text" class="form-control" placeholder="Telephone Number" name="tel"
                   id="tel" class="form-control" pattern="^\d{10}$" [(ngModel)]="supplier.telephone2">
          </div>
        </div>
        <div class="row g-3 mt-1">
          <div class="col-12 col-sm-6 form-group">
            <label>E-mail</label>
            <input type="text" class="form-control" id="email" placeholder="Enter Email"
                   [(ngModel)]="supplier.email" name="email"
                   pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$">
          </div>
          <div class="col-12 col-sm-6 form-group">
            <label>Balance</label>
            <input type="text" #bal="ngModel" [class.is-invalid]="bal.invalid && bal.touched"
                   class="form-control" id="bal" [(ngModel)]="supplier.balance" name="bal"
                   placeholder="Balance">
          </div>
        </div>
        <div class="row g-3 mt-1">
          <div class="col-12 col-sm-6 form-group">
            <label>Contact Person Name</label>
            <input type="text"
                   class="form-control" id="pName" name="pName"
                   placeholder="Contact Person Name" [(ngModel)]="supplier.contactPersonName">
          </div>
          <div class="col-12 col-sm-6 form-group">
            <label> Contact Person Telephone</label>
            <input type="tel" class="form-control" placeholder=" Contact Person Telephone"
                   name="CPphone" id="CPphone" class="form-control" pattern="^\d{10}$"
                   [(ngModel)]="supplier.contactPersonTelephone">
          </div>
        </div>
        <div class="row g-3 mt-3">
          <div class="col-12 col-sm-6">
            <div class="form-check checkbox">
              <input class="form-check-input" id="check" name="check" type="checkbox" [(ngModel)]="supplier.active">
              <label class="form-check-label ms-2" for="check">Is Active</label>
            </div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-12 text-right">
            <button type="button" class="btn btn-warning mr-2" [disabled]="!manageSuppliers.form.valid"
                    (click)="Clear()">
              Clear
            </button>
            <button type="submit" [disabled]="!manageSuppliers.form.valid" class="btn btn-primary">Save</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

