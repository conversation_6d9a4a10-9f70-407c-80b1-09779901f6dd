import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, FormArray, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {BsDatepickerConfig} from 'ngx-bootstrap/datepicker';
import {QuotationService} from '../../service/quotation.service';
import {Quotation} from '../../model/quotation';
import {Item} from '../../../inventory/model/item';
import {ItemService} from '../../../inventory/service/item.service';
import {debounceTime, distinctUntilChanged, switchMap} from 'rxjs/operators';
import {Subject} from 'rxjs';
import {Customer} from "../../model/customer";
import {CustomerService} from "../../service/customer.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {environment} from "../../../../../../environments/environment";

@Component({
  selector: 'app-create-quotation',
  templateUrl: './create-quotation.component.html',
  styleUrls: ['./create-quotation.component.css']
})
export class CreateQuotationComponent implements OnInit {
  quotationForm: FormGroup;
  quotation: Quotation = new Quotation();
  customers: Customer[] = [];
  items: Item[] = [];
  loading = false;
  bsConfig: Partial<BsDatepickerConfig>;
  customerSearchTerm = new Subject<string>();

  ngOnInit() {

  }

  constructor(
    private fb: FormBuilder,
    private quotationService: QuotationService,
    private customerService: CustomerService,
    private itemService: ItemService,
    private router: Router,
    private notificationService: NotificationService
  ) {
    this.initForm();
    this.bsConfig = {
      dateInputFormat: 'YYYY-MM-DD',
      containerClass: 'theme-dark-blue'
    };
    this.setupCustomerSearch();
  }

  private setupCustomerSearch() {
    this.customerSearchTerm.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(term => this.customerService.findByNameLike(term))
    ).subscribe((results: Customer[]) => {
      this.customers = results;
    });
  }

  onCustomerSearch(event: any) {
    const searchTerm = event.target.value;
    if (searchTerm && searchTerm.length > 2) {
      this.customerService.findByNameLike(searchTerm).subscribe({
        next: (customers: Customer[]) => {
          this.customers = customers;
        },
        error: (error) => {
          this.notificationService.showError('Failed to fetch customers');
        }
      });
    }
  }

  private initForm() {
    this.quotationForm = this.fb.group({
      quotationNo: [{value: '', disabled: true}],
      customerId: ['', Validators.required],
      total: [{value: 0, disabled: true}],
      discount: [0],
      subTotal: [{value: 0, disabled: true}],
      date: [new Date(), Validators.required],
      validUntil: [null],
      status: ['PENDING'],
      remarks: [''],
      warehouseCode: [environment.warehouseCode],
      counter: [''],
      records: this.fb.array([])
    });
  }

  get quotationRecords() {
    return this.quotationForm.get('records') as FormArray;
  }

  addRecord() {
    const record = this.fb.group({
      itemCode: [''],
      itemName: [''],
      item: [null],
      quantity: [1],
      unitPrice: [0],
      unitPriceOriginal: [0],
      subTotal: [0],
      discount: [0],
      price: [0],
      date: [new Date()],
      recodeType: [null],
      warehouseCode: [environment.warehouseCode],
      counter: ['']
    });
    this.quotationRecords.push(record);
    this.calculateTotals();
  }

  removeRecord(index: number) {
    this.quotationRecords.removeAt(index);
    this.calculateTotals();
  }

  calculateTotals() {
    let total = 0;
    const records = this.quotationRecords.value;
    records.forEach(record => {
      record.subTotal = (record.quantity * record.unitPrice);
      record.price = record.subTotal - record.discount;
      total += record.price;
    });

    const discount = this.quotationForm.get('discount').value || 0;
    const subTotal = total - discount;

    this.quotationForm.patchValue({
      total: total,
      subTotal: subTotal
    });
  }

  onItemSelect(event: any, index: number) {
    const record = this.quotationRecords.at(index);
    record.patchValue({
      itemCode: event.itemCode,
      itemName: event.name,
      item: event,
      unitPrice: event.sellingPrice,
      unitPriceOriginal: event.sellingPrice
    });
    this.calculateTotals();
  }

  saveQuotation() {
    if (this.quotationForm.valid && this.quotationRecords.length > 0) {
      this.loading = true;
      const quotation: Quotation = this.quotationForm.getRawValue(); // Use getRawValue() to include disabled fields

      this.quotationService.save(quotation).subscribe({
        next: (response: Quotation) => {
          this.quotation = response;
          this.loading = false;
          this.notificationService.showSuccess('Quotation saved successfully');
          this.router.navigate(['/home/<USER>/manage_quotation']);
        },
        error: (error) => {
          console.error('Failed to save quotation:', error);
          this.loading = false;
          this.notificationService.showError('Failed to save quotation');
        }
      });
    } else {
      this.notificationService.showWarning('Please fill all required fields and add at least one item');
    }
  }
}
