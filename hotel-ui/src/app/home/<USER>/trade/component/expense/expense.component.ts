import {Component, OnInit} from '@angular/core';
import {Expense} from "../../model/expense";
import {ExpenseType} from "../../model/expense-type";
import {ExpenseService} from "../../service/expense.service";
import {ExpenseTypeService} from "../../service/expense-type.service";
import {Employee} from "../../../hr/model/employee";
import {EmployeeService} from "../../../hr/service/employee.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {NgForm} from "@angular/forms";

@Component({
  selector: 'app-expense',
  templateUrl: './expense.component.html',
  styleUrls: ['./expense.component.css']
})
export class ExpenseComponent implements OnInit {

  expense: Expense;

  expenseTypeList: Array<ExpenseType>;
  keyExpenseType: string;

  keyEmpSearch: string;
  empSearchList: Array<Employee>;

  constructor(private expenseService: ExpenseService, private expenseTypeService: ExpenseTypeService,
              private employeeService: EmployeeService, private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.expense = new Expense();
    this.expense.date = new Date();
    this.expenseTypeList = [];
    this.empSearchList = [];
    this.expense.type = new ExpenseType();
  }

  loadExpenseTypes() {
    this.expenseTypeService.findByName(this.keyExpenseType).subscribe((data: Array<ExpenseType>) => {
      return this.expenseTypeList = data;
    });
  }

  setSelectedExpType(event) {
    this.expense.type.id = event.item.id;
  }

  searchEmployee() {
    this.employeeService.findByEmployeeNameLike(this.keyEmpSearch).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  setSelectedEmp(event) {
    this.expense.responsiblePerson = new Employee();
    this.expense.responsiblePerson.id = event.item.id;
  }

  save(form: NgForm) {
    this.expenseService.save(this.expense).subscribe((result: any) => {
      if (result == true) {
        this.notificationService.showSuccess("Expense saved");
        form.resetForm();
        this.ngOnInit();
      } else {
        this.notificationService.showError("Expense saving failed");
        console.log(result.data);
      }
    })
  }

}
