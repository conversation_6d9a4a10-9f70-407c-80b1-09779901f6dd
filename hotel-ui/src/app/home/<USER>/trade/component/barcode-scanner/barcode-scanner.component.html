<div class="card" [ngClass]="{'p-0': !isModal, 'p-3': isModal}">
  <div class="card-header d-flex justify-content-between align-items-center">
    <strong>Scan Barcode</strong>
    <button type="button" class="btn btn-sm btn-outline-secondary" aria-label="Close" (click)="close()">
      <i class="fa fa-times"></i>
    </button>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12 mb-3" *ngIf="availableDevices.length > 1">
        <label>Select Camera</label>
        <select class="form-control" (change)="onDeviceSelectChange($event.target.value)">
          <option *ngFor="let device of availableDevices" [value]="device.deviceId">
            {{ device.label || 'Camera ' + (availableDevices.indexOf(device) + 1) }}
          </option>
        </select>
      </div>

      <div class="col-md-12">
        <div class="video-container">
          <video id="video-element" style="width: 100%; max-height: 300px; border: 1px solid #ddd;"></video>
        </div>
        <div class="text-center mt-3">
          <p class="text-muted">Position the barcode within the camera view</p>
          <p class="text-muted">Only Code 128 barcodes are supported</p>
        </div>
      </div>
    </div>

    <!-- Desktop buttons -->
    <div class="row mt-3 d-none d-md-flex">
      <div class="col-md-12 text-right">
        <button class="btn btn-secondary mr-2" (click)="close()">Cancel</button>
        <button class="btn btn-primary" (click)="stopScanning(); startScanning()">
          <i class="fa fa-refresh mr-1"></i> Restart Scan
        </button>
      </div>
    </div>

    <!-- Mobile buttons -->
    <div class="row mt-3 d-md-none">
      <div class="col-6">
        <button class="btn btn-secondary btn-block" (click)="close()">
          <i class="fa fa-times mr-1"></i> Cancel
        </button>
      </div>
      <div class="col-6">
        <button class="btn btn-primary btn-block" (click)="stopScanning(); startScanning()">
          <i class="fa fa-refresh mr-1"></i> Restart
        </button>
      </div>
  </div>
