import { Component, OnInit } from '@angular/core';
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {SalesInvoice} from "../../model/sales-invoice";
import {SalesInvoiceService} from "../../service/sales-invoice.service";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {MetaData} from "../../../../core/model/metaData";
import {Invoice80Component} from "../invoices/invoice-80-en/invoice-80.component";
import {Invoice80SnComponent} from "../invoices/invoice-80-sn/invoice-80-sn.component";
import {Invoice76EnComponent} from "../invoices/invoice-76-en/invoice-76-en.component";
import {InvoiceLegalComponent} from "../invoices/invoice-legal/invoice-legal.component";
import {InvoiceLegalCustomComponent} from "../invoices/invoice-legal-custom/invoice-legal-custom.component";
import {SettingsService} from "../../../../core/service/settings.service";
import {SilentPrintService} from "../../service/silent-print.service";
import {CompanyService} from "../../../../core/service/company.service";
import {NotificationService} from "../../../../core/service/notification.service";

@Component({
  selector: 'app-view-si',
  templateUrl: './view-si.component.html',
  styleUrls: ['./view-si.component.css']
})
export class ViewSiComponent implements OnInit {

  salesInvoice: SalesInvoice;
  invoiceId: string; // ID of the invoice to view/edit
  editMode: boolean = false; // Flag to indicate if we're in edit mode

  cashPayment: boolean;
  cash: MetaData;

  modalRef: BsModalRef;
  modalRefInvoice: BsModalRef;

  constructor(private modalService: BsModalService,
              private salesInvoiceService: SalesInvoiceService,
              private metaDataService: MetaDataService,
              private silentPrintService: SilentPrintService,
              private companyService: CompanyService,
              private notificationService: NotificationService,
              private settingsService: SettingsService) { }

  // Settings for printing
  useSilentPrint: boolean = false;
  printPage: string = 'legal';

  ngOnInit(): void {
    this.salesInvoice = new SalesInvoice();
    this.salesInvoice.paymentMethod = new MetaData();
    this.salesInvoice.status = new MetaData();
    this.cashPayment = true;
    this.loadCashPayment();

    // Load settings from localStorage
    this.useSilentPrint = this.settingsService.useSilentPrint();
    this.printPage = this.settingsService.getPrintPage();

    // If an invoice ID was provided, load the invoice
    if (this.invoiceId) {
      this.salesInvoiceService.findByInvoiceNo(this.invoiceId).subscribe(
        (invoice: SalesInvoice) => {
          if (invoice) {
            this.salesInvoice = invoice;
            this.checkPaymentMethod();
          }
        }
      );
    }
  }

  findSi(invNo){
    this.salesInvoiceService.findByInvoiceNo(invNo).subscribe((data: SalesInvoice)=>{
      this.salesInvoice = data;
      this.checkPaymentMethod();
    })
  }

  loadCashPayment(){
    this.metaDataService.findByValueAndCategory("Cash", "PaymentMethod").subscribe((data: MetaData)=>{
      this.cash = data;
    });
  }

  checkPaymentMethod() {
    if (this.salesInvoice.paymentMethod.id === this.cash.id){
      this.cashPayment = false;
    }else{
      this.cashPayment = true;
    }
  }

  close() {
    this.modalRef.hide();
  }

  /**
   * Update the invoice (only available in edit mode)
   */
  updateInvoice() {
    if (!this.editMode) {
      return;
    }

    this.salesInvoiceService.save(this.salesInvoice).subscribe(
      (result: any) => {
        if (result === true) {
          this.notificationService.showSuccess('Invoice updated successfully');
          this.modalRef.hide();
        } else {
          this.notificationService.showError('Failed to update invoice');
        }
      },
      error => {
        console.error('Error updating invoice:', error);
        this.notificationService.showError('Failed to update invoice');
      }
    );
  }

  /**
   * Print the invoice using the appropriate method based on environment settings
   */
  print() {
    if (!this.salesInvoice || !this.salesInvoice.invoiceNo) {
      return;
    }

    // Check if silent printing is enabled in settings
    if (this.useSilentPrint) {
      // Use silent printing
      this.printSilently();
    } else {
      // Use the traditional modal approach
      let invoiceModal;

      // Make sure we have the latest settings
      this.useSilentPrint = this.settingsService.useSilentPrint();
      this.printPage = this.settingsService.getPrintPage();

      switch (this.printPage) {
        case "80en":
          invoiceModal = this.modalService.show(Invoice80Component, { class: 'modal-sm' });
          break;
        case "80sn":
          invoiceModal = this.modalService.show(Invoice80SnComponent, { class: 'modal-sm' });
          break;
        case "76en":
          invoiceModal = this.modalService.show(Invoice76EnComponent, { class: 'modal-sm' });
          break;
        case "legal":
          invoiceModal = this.modalService.show(InvoiceLegalComponent, { class: 'modal-xl' });
          break;
        case "halfLetter":
          invoiceModal = this.modalService.show(InvoiceLegalCustomComponent, { class: 'modal-xl' });
          break;
        default:
          // Default to legal if no valid print page is specified
          invoiceModal = this.modalService.show(InvoiceLegalComponent, { class: 'modal-xl' });
          break;
      }
      invoiceModal.content.invoiceNo = this.salesInvoice.invoiceNo;
      invoiceModal.content.pastBill = true;
      invoiceModal.content.findInvoice();
    }
  }

  /**
   * Print the invoice silently without showing the print dialog
   * Note: This requires Chrome to be launched with --kiosk-printing flag
   */
  printSilently() {
    if (!this.salesInvoice || !this.salesInvoice.invoiceNo) {
      return;
    }

    // Fetch the invoice data
    this.salesInvoiceService.findByInvoiceNo(this.salesInvoice.invoiceNo).subscribe((invoice: SalesInvoice) => {
      // Also fetch company data
      this.companyService.findCompany().subscribe((company) => {
        // Create a direct print using the silent print service
        this.generateInvoiceHtmlAndPrint(invoice, company);
      });
    });
  }

  /**
   * Generates invoice HTML content and prints it silently
   * @param invoice The invoice data
   * @param company The company data
   */
  private generateInvoiceHtmlAndPrint(invoice: SalesInvoice, company: any) {
    // Format the date
    const date = new Date(invoice.date);
    const formattedDate = date.toLocaleDateString() + ', ' + date.toLocaleTimeString();

    // Determine payment status
    const paymentStatus = invoice.status.value === 'Paid' ? 'Cash' : 'Credit';

    // Get the current user
    const user = JSON.parse(localStorage.getItem('currentUser')).user.firstName;

    // Generate HTML based on the invoice type
    let invoiceHtml = '';

    // Make sure we have the latest settings
    this.useSilentPrint = this.settingsService.useSilentPrint();
    this.printPage = this.settingsService.getPrintPage();

    switch (this.printPage) {
      case "80en":
      case "80sn":
      case "76en":
        invoiceHtml = this.generate80mmInvoiceHtml(invoice, company, formattedDate, user, paymentStatus);
        break;
      case "legal":
      case "halfLetter":
        invoiceHtml = this.generateLegalInvoiceHtml(invoice, company, formattedDate, user, paymentStatus);
        break;
      default:
        invoiceHtml = this.generate80mmInvoiceHtml(invoice, company, formattedDate, user, paymentStatus);
    }

    // Print the generated HTML
    const printStyles = `
      @media print {
        body { margin: 0; padding: 0; }
        @page { size: auto; margin: 0mm; }
      }
    `;

    this.silentPrintService.printHtmlContentSilently(invoiceHtml, printStyles);
  }

  /**
   * Generates HTML for 80mm receipt format
   */
  private generate80mmInvoiceHtml(invoice: SalesInvoice, company: any, formattedDate: string, user: string, paymentStatus: string): string {
    // Calculate totals
    let invRecDiscount = 0;
    for (let rec of invoice.salesInvoiceRecords) {
      invRecDiscount += (rec.discount * rec.quantity);
    }

    // Generate the HTML for the 80mm receipt
    return `
      <div style="padding-left: 6%; padding-right: 6%; font-family: Tahoma !important; width: 80mm; height: auto">
        <address style="text-align: center; padding-top: 0px">
          <p style="font-weight: bold; font-size: 20px; padding: 0px; margin-bottom: 10px; font-style: normal;">${company.name}</p>
          <p style="font-size: 15px; margin-bottom: 10px; font-style: normal;">${company.fullAddress}</p>
          <p style="font-size: 15px; margin-bottom: 10px; font-style: normal;">${company.telephone1 + ' / ' + company.telephone2}</p>
        </address>

        <div style="width: 100%">
          <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 10px; margin-bottom: 10px;">
          <table style="font-size: 15px; width: 100%">
            <tr>
              <td style="text-align: left">${formattedDate}</td>
              <td style="text-align: right">${invoice.invoiceNo}</td>
            </tr>
          </table>
          <table style="font-size: 15px; width: 100%">
            <tr>
              <td style="text-align: left">${user}</td>
              <td style="text-align: right">${invoice.customerName}</td>
            </tr>
          </table>
        </div>

        <div>
          <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 10px; margin-top: 0px;">
          <table style="width: 100%; font-size: 14px;">
            <thead>
              <tr style="padding: 0px; margin: 0px">
                <td style="padding: 0.2rem;padding: 0px; margin: 0px">#</td>
                <th style="padding: 0.2rem">Qty</th>
                <th style="padding: 0.2rem">List Price</th>
                <th style="padding: 0.2rem">Sale Price</th>
                <th style="padding: 0.2rem">Amount</th>
              </tr>
            </thead>
          </table>
          ${invoice.salesInvoiceRecords.map((rec, i) => `
            <div style="font-size: 16px; width: 100%; display: inline-block;margin: 0.3rem; margin-left: 0px !important;">
              <div style="text-align: left; width: 8%;float: left">${i + 1}.</div>
              <div style="text-align: left;width: 92%;float: left;">${rec.itemName}</div>
              <div style="text-align: right; width: 15%;float: left;">${rec.quantity}</div>
              <div style="text-align: right;width: 27%;float: left;">${this.formatNumber(rec.unitPrice)}</div>
              <div style="text-align: right;width: 27%;float: left;">${this.formatNumber(rec.unitPrice - rec.discount)}</div>
              <div style="text-align: right;width: 31%;float: left;">${this.formatNumber(rec.price)}</div>
            </div>
          `).join('')}
        </div>

        <hr style="background-color: #fff; border-top: 2px dashed #000000;">

        <div style="margin-top: 0px; width: 100%">
          <div style="font-size: 16px; display: grid; grid-template-columns: 6fr 4fr; grid-template-rows: 1fr">
            <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
              <li style="margin-bottom: 5px; font-weight: bold;">Sub Total</li>
              <li style="margin-bottom: 5px; font-weight: bold;">Discount</li>
              <li style="margin-bottom: 5px; font-weight: bold;">Total</li>
              <li style="margin-bottom: 5px; font-weight: bold;">Payment</li>
              <li style="margin-bottom: 5px; font-weight: bold;">${invoice.balance <= 0 ? 'Cash Balance' : 'Balance Due'}</li>
            </ul>
            <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
              <li style="margin-bottom: 5px; font-weight: bold;">${this.formatNumber(invoice.subTotal)}</li>
              <li style="margin-bottom: 5px; font-weight: bold;">${this.formatNumber(invoice.totalDiscount)}</li>
              <li style="margin-bottom: 5px; font-weight: bold;">${this.formatNumber(invoice.totalAmount)}</li>
              <li style="margin-bottom: 5px; font-weight: bold;">${this.formatNumber(invoice.cashBalance > 0 ? invoice.payment + invoice.cashBalance : invoice.payment)}</li>
              <li style="margin-bottom: 5px; font-weight: bold;">${invoice.balance == 0 ? this.formatNumber(invoice.cashBalance) : this.formatNumber(invoice.balance)}</li>
            </ul>
          </div>
        </div>

        <p style="text-align: center; font-style: normal; font-size: 0.6rem; font-weight: bold; margin-bottom: 5px;">Thank You. Come Again</p>
        <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 0px; margin-top: 0px;">
        <div style="overflow: hidden; margin-top: 1vh">
          <span style="float: left; font-style: normal; font-size: 0.5rem; margin-top: 1px; font-weight: bold;">Software by S-OUT solutions</span>
          <span style="float: right; font-style: normal; font-size: 0.5rem; margin-top: 1px; font-weight: bold;">071 97 98 99 9</span>
        </div>
      </div>
    `;
  }

  /**
   * Generates HTML for legal size invoice format
   */
  private generateLegalInvoiceHtml(invoice: SalesInvoice, company: any, formattedDate: string, user: string, paymentStatus: string): string {
    return `
      <div style="padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2>${company.name}</h2>
          <p>${company.fullAddress}</p>
          <p>Tel: ${company.telephone1} / ${company.telephone2}</p>
        </div>

        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
          <div>
            <p><strong>Invoice #:</strong> ${invoice.invoiceNo}</p>
            <p><strong>Date:</strong> ${formattedDate}</p>
          </div>
          <div>
            <p><strong>Customer:</strong> ${invoice.customerName}</p>
            <p><strong>Payment:</strong> ${paymentStatus}</p>
          </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f2f2f2;">
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">#</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Item</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">Qty</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">Unit Price</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">Discount</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.salesInvoiceRecords.map((rec, i) => `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${i + 1}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${rec.itemName}</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${rec.quantity}</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${this.formatNumber(rec.unitPrice)}</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${this.formatNumber(rec.discount)}</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${this.formatNumber(rec.price)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div style="display: flex; justify-content: flex-end;">
          <table style="width: 300px;">
            <tr>
              <td style="padding: 8px;"><strong>Sub Total:</strong></td>
              <td style="padding: 8px; text-align: right;">${this.formatNumber(invoice.subTotal)}</td>
            </tr>
            <tr>
              <td style="padding: 8px;"><strong>Discount:</strong></td>
              <td style="padding: 8px; text-align: right;">${this.formatNumber(invoice.totalDiscount)}</td>
            </tr>
            <tr>
              <td style="padding: 8px;"><strong>Total:</strong></td>
              <td style="padding: 8px; text-align: right;">${this.formatNumber(invoice.totalAmount)}</td>
            </tr>
            <tr>
              <td style="padding: 8px;"><strong>Payment:</strong></td>
              <td style="padding: 8px; text-align: right;">${this.formatNumber(invoice.payment)}</td>
            </tr>
            <tr>
              <td style="padding: 8px;"><strong>${invoice.balance <= 0 ? 'Cash Balance:' : 'Balance Due:'}</strong></td>
              <td style="padding: 8px; text-align: right;">${invoice.balance == 0 ? this.formatNumber(invoice.cashBalance) : this.formatNumber(invoice.balance)}</td>
            </tr>
          </table>
        </div>

        <div style="margin-top: 40px; display: flex; justify-content: space-between;">
          <div style="text-align: center;">
            <p>____________________</p>
            <p>Customer Signature</p>
          </div>
          <div style="text-align: center;">
            <p>____________________</p>
            <p>Authorized Signature</p>
          </div>
        </div>

        <div style="margin-top: 20px; text-align: center;">
          <p style="font-size: 12px;">Thank you for your business!</p>
        </div>
      </div>
    `;
  }

  /**
   * Format number with 2 decimal places
   */
  private formatNumber(value: number): string {
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
}
