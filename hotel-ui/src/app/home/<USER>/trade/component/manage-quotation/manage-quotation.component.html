<div class="container-fluid px-0">
  <h2 class="component-title">Manage Quotations</h2>
    <div class="table-responsive">
      <table class="table table-bordered table-hover">
        <thead>
          <tr>
            <th>Quotation No</th>
            <th>Date</th>
            <th>Valid Until</th>
            <th>Customer</th>
            <th>Total</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let quotation of quotations">
            <td>{{quotation.quotationNo}}</td>
            <td>{{quotation.date | date}}</td>
            <td>{{quotation.validUntil | date}}</td>
            <td>{{quotation.customerId}}</td>
            <td>{{quotation.total | number:'1.2-2'}}</td>
            <td>{{quotation.status?.name}}</td>
            <td>
              <button class="btn btn-sm btn-primary me-1" (click)="convertToInvoice(quotation.id)">
                <i class="fas fa-file-invoice"></i> Convert to Invoice
              </button>
              <button class="btn btn-sm btn-danger" (click)="deleteQuotation(quotation.id)">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>  <div class="row d-flex">
      <div class="col-md-12">
        <pagination class="pagination-sm justify-content-center"
                    [totalItems]="collectionSize"
                    [maxSize]="maxSize"
                    [boundaryLinks]="true"
                    [(ngModel)]="page"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
  </div>

