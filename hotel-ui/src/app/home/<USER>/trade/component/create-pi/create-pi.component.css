.table tr.active td {
  background-color: #0e012d !important;
  color: white;
}

.table-height{
  height: 55vh;
  overflow-y: auto;
}

.header-section {
  border-radius: 4px;
  margin-bottom: 1rem;
}

.content-section {
  padding: 1rem 0;
}

/* Fix for horizontal overflow */
.container-fluid {
  max-width: 100%;
  overflow-x: hidden;
}

.row {
  margin-left: 0;
  margin-right: 0;
}

/* Button styling for consistent appearance */
.btn-lg {
  min-width: 120px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
}

/* Ensure all buttons have consistent height */
.btn {
  min-height: 38px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Add some spacing between icon and text */
.btn i {
  margin-right: 0.5rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .form-group {
    padding-left: 5px;
    padding-right: 5px;
  }

  .form-control,
  input[type="text"],
  input[type="number"],
  select {
    font-size: 0.9rem;
    height: 38px !important;
  }

  .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.85rem;
    min-width: 70px;
    height: 38px;
    min-height: 38px;
  }

  /* Smaller buttons for top and bottom action rows */
  .d-md-none .btn {
    padding: 0.25rem 0.4rem;
    font-size: 0.8rem;
    min-width: 40px;
  }

  /* Full width Add button in mobile view */
  .add-item-btn {
    margin-top: 0.5rem;
    margin-bottom: 1rem !important;
    font-weight: 600;
    font-size: 0.9rem !important;
    padding: 0.375rem !important;
    height: 38px !important;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: 100% !important; /* Ensure full width */
  }

  /* Adjust spacing for mobile action buttons */
  .d-md-none .mx-1 {
    margin-left: 0.15rem !important;
    margin-right: 0.15rem !important;
  }

  /* Ensure top navigation buttons fit in one row */
  .d-md-none .btn-sm.px-2 {
    padding-left: 0.4rem !important;
    padding-right: 0.4rem !important;
    min-width: auto;
  }

  /* Fix input group button alignment */
  .input-group-append .btn {
    height: 38px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Ensure input and button heights match in input groups */
  .input-group .form-control,
  .input-group .input-group-append,
  .input-group .input-group-append .btn {
    height: 38px !important;
  }

  .table-height {
    height: 40vh;
  }

  /* Ensure buttons don't overflow */
  .d-flex.justify-content-between {
    flex-wrap: wrap;
  }

  /* Smaller margins between buttons */
  .btn {
    margin-right: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  /* Adjust input groups */
  .input-group {
    width: 100%;
  }

  /* Fix for card view in mobile */
  .card {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}

