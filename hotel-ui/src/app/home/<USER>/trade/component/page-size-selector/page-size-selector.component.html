<div class="page-size-selector d-flex align-items-center">
  <span class="mr-2">Rows per page:</span>
  
  <!-- Predefined sizes -->
  <div class="btn-group btn-group-sm mr-3">
    <button *ngFor="let size of predefinedSizes" 
            type="button" 
            class="btn" 
            [ngClass]="{'btn-primary': pageSize === size, 'btn-outline-secondary': pageSize !== size}"
            (click)="setPageSize(size)">
      {{ size }}
    </button>
  </div>
  
  <!-- Custom size input -->
  <div class="input-group input-group-sm custom-size">
    <input type="number" class="form-control" [(ngModel)]="customPageSize" min="1" max="500">
    <div class="input-group-append">
      <button class="btn btn-outline-secondary" type="button" (click)="applyCustomPageSize()">
        Apply
      </button>
    </div>
  </div>
</div>
