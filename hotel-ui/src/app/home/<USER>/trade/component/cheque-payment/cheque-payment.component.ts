import {Component, OnInit} from '@angular/core';
import {BsModalRef} from "ngx-bootstrap/modal";
import {NotificationService} from "../../../../core/service/notification.service";
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {Cheque} from "../../model/cheque";
import {ChequeService} from "../../service/cheque.service";
import {Customer} from "../../model/customer";
import {CustomerService} from "../../service/customer.service";
import {Supplier} from "../../model/supplier";
import {SupplierService} from "../../service/supplier.service";

@Component({
  selector: 'app-expense-type',
  templateUrl: './cheque-payment.component.html',
  styleUrls: ['./cheque-payment.component.css']
})
export class ChequePaymentComponent implements OnInit {

  modalRef: BsModalRef;

  // Flag to indicate if this component is opened as a modal
  isModal: boolean = false;

  keyCustomer: string;
  customerList: Array<Customer>;

  keySupplier: string;
  supplierList: Array<Supplier>;

  totalAmount: number;
  cashAmount: number;
  chequeNo: string;
  chequeDate: Date;
  bankList: Array<MetaData>;
  bankId: string;
  cheque: Cheque;

  // Type of cheque: 'RECEIVED' (from customer) or 'GIVEN' (to supplier)
  chequeType: string = 'RECEIVED'; // Default to received

  // Flag to hide the cheque type selection UI
  hideTypeSelection: boolean = false;

  constructor(private notificationService: NotificationService,
              private metaDataService: MetaDataService,
              private chequeService: ChequeService,
              private customerService: CustomerService,
              private supplierService: SupplierService) {
  }

  ngOnInit() {
    this.chequeDate = new Date();
    this.loadBanks();
    this.cheque = new Cheque();
    this.cheque.chequeType = this.chequeType;
  }

  setPayment() {
    // Validate based on cheque type
    if (this.chequeType === 'RECEIVED') {
      if (!this.cheque.customer || !this.cheque.customer.id) {
        this.notificationService.showError('Cannot create a Cheque without a Real Customer');
        return;
      }
    } else { // GIVEN
      if (!this.cheque.supplier || !this.cheque.supplier.id) {
        this.notificationService.showError('Cannot create a Cheque without a Real Supplier');
        return;
      }
    }

    if ((this.cheque.chequeAmount + this.cashAmount) < this.totalAmount) {
      this.notificationService.showError("This will not close the bill");
    }

    // Set the cheque type before closing
    this.cheque.chequeType = this.chequeType;
    this.modalRef.hide();
  }

  closeWindow() {
    this.modalRef.hide();
    this.totalAmount = 0;
    this.cashAmount = 0;
    this.chequeNo = '';
  }

  setBank(event) {
    this.cheque.bank = new MetaData();
    this.cheque.bank.id = event.target.value;
  }

  loadBanks() {
    this.metaDataService.findByCategory("Bank").subscribe((res: Array<MetaData>) => {
      this.bankList = res;
    })
  }

  setChequeNo() {
    this.chequeNo = this.cheque.chequeNo;
  }

  loadCustomer() {
    this.customerService.findByNameLike(this.keyCustomer).subscribe((data: Array<Customer>) => {
      this.customerList = data;
    })
  }

  setSelectedCustomer(event) {
    this.cheque.customer = new Customer();
    this.cheque.customer.id = event.item.id;
    this.cheque.customer.name = event.item.name;
  }

  loadSupplier() {
    this.supplierService.findByNameLike(this.keySupplier).subscribe((data: Array<Supplier>) => {
      this.supplierList = data;
    })
  }

  setSelectedSupplier(event) {
    this.cheque.supplier = new Supplier();
    this.cheque.supplier.id = event.item.id;
    this.cheque.supplier.name = event.item.name;
  }

  // Set the cheque type (RECEIVED or GIVEN)
  setChequeType(type: string) {
    this.chequeType = type;
    this.cheque.chequeType = type;
  }

}
