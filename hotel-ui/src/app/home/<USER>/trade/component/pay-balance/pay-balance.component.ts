import {Component, Input, OnInit} from '@angular/core';
import {SalesInvoice} from "../../model/sales-invoice";
import {SalesInvoiceService} from "../../service/sales-invoice.service";
import {Response} from "../../../../core/model/response";
import {NotificationService} from "../../../../core/service/notification.service";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {MetaData} from "../../../../core/model/metaData";
import {ChequePaymentComponent} from "../cheque-payment/cheque-payment.component";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {PayBalance} from "../../model/pay-balance";
import {CustomerService} from "../../service/customer.service";
import {Customer} from "../../model/customer";
import {PurchaseInvoiceService} from "../../service/purchase-invoice.service";
import {Supplier} from "../../model/supplier";

@Component({
  selector: 'app-pay-si-balance',
  templateUrl: './pay-balance.component.html',
  styleUrls: ['./pay-balance.component.css']
})
export class PayBalanceComponent implements OnInit {

  si: SalesInvoice;
  bsModalRef: BsModalRef;
  isModal: boolean = false;

  paymentMethods: Array<MetaData> = [];
  chequeId: string;

  payBalance: PayBalance;

  totalAmount: number;
  balance: number;
  invoiceNo: string;

  // Flag to indicate if this is a purchase invoice
  isPurchaseInvoice: boolean = false;

  // Purchase invoice specific properties
  supplier: Supplier;

  isProcessing: boolean;

  constructor(private siService: SalesInvoiceService, private notificationService: NotificationService,
              private metaDataService: MetaDataService, private modalService: BsModalService,
              private customerService: CustomerService, private piService: PurchaseInvoiceService) {
  }

  ngOnInit(): void {
    this.si = new SalesInvoice();
    this.payBalance = new PayBalance();
    this.isProcessing = false;
    this.findPaymentMethods();

    // If bsModalRef is set, then component is opened as a modal
    this.isModal = !!this.bsModalRef;

    // If invoiceNo is set, use it for the payment
    if (this.invoiceNo) {
      this.payBalance.siNo = this.invoiceNo;
      this.si.invoiceNo = this.invoiceNo;
      this.si.totalAmount = this.totalAmount;
      this.si.balance = this.balance;
    }
  }

  pay() {
    if (this.payBalance.paymentMethodId) {
      this.isProcessing = true;

      if (this.isPurchaseInvoice) {
        // Handle purchase invoice payment
        if (this.payBalance.paymentMethodId == this.chequeId && this.payBalance.cheque) {
          // If payment method is cheque and cheque data is available, use the new method
          this.payBalance.siNo = this.invoiceNo; // Set the invoice number in the payBalance object

          this.piService.payPIBalanceWithCheque(this.payBalance).subscribe((result: Response) => {
            if (result.code === 200) {
              this.notificationService.showSuccess("Purchase invoice paid successfully with cheque");
              this.bsModalRef.hide();
            } else {
              this.notificationService.showError(result.message || "Failed to pay purchase invoice with cheque");
            }
            this.isProcessing = false;
          }, error => {
            console.error('Error paying purchase invoice with cheque:', error);
            this.notificationService.showError("Failed to pay purchase invoice with cheque: " + (error.message || "Unknown error"));
            this.isProcessing = false;
          });
        } else {
          // Use the existing method for non-cheque payments
          const amount = this.payBalance.amount;
          const purchaseInvoiceNo = this.invoiceNo;

          this.piService.payPIBalance(purchaseInvoiceNo, amount).subscribe((result: Response) => {
            if (result.code === 200) {
              this.notificationService.showSuccess("Purchase invoice paid successfully");
              this.bsModalRef.hide();
            } else {
              this.notificationService.showError(result.message || "Failed to pay purchase invoice");
            }
            this.isProcessing = false;
          }, error => {
            console.error('Error paying purchase invoice:', error);
            this.notificationService.showError("Failed to pay purchase invoice: " + (error.message || "Unknown error"));
            this.isProcessing = false;
          });
        }
      } else {
        // Handle sales invoice payment
        this.payBalance.siNo = this.si.invoiceNo;
        this.siService.payBalance(this.payBalance).subscribe((result: Response) => {
          if (result.code === 200) {
            this.notificationService.showSuccess(result.message || "Sales invoice paid successfully");
            this.bsModalRef.hide();
          } else {
            this.notificationService.showError(result.message || "Failed to pay sales invoice");
          }
          this.isProcessing = false;
        }, error => {
          console.error('Error paying sales invoice:', error);
          this.notificationService.showError("Failed to pay sales invoice: " + (error.message || "Unknown error"));
          this.isProcessing = false;
        });
      }
    }
  }

  findPaymentMethods() {
    this.metaDataService.findByCategory('PaymentMethod').subscribe((result: Array<MetaData>) => {
      for (let meta of result) {
        if (meta.value === 'Cheque') {
          this.chequeId = meta.id;
        }
      }
      return this.paymentMethods = result;
    })
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
  }

  setPaymentMethod(event) {
    this.payBalance.paymentMethodId = event.target.value;

    // For purchase invoices, handle cheques similar to sales invoices
    if (this.isPurchaseInvoice) {
      if (this.payBalance.paymentMethodId == this.chequeId) {
        this.payBalance.amount = 0;
        let chequeModal = this.modalService.show(ChequePaymentComponent, <ModalOptions>{
          class: 'modal-lg',
          ignoreBackdropClick: true,
          backdrop: 'static',
          initialState: {
            isModal: true,
            chequeType: 'GIVEN', // Always set to GIVEN for purchase invoices
            hideTypeSelection: true // Hide the type selection UI
          }
        });
        chequeModal.content.modalRef = chequeModal;
        chequeModal.content.totalAmount = this.totalAmount;
        chequeModal.content.cashAmount = 0; // No previous payment for PI

        // If supplier is available, set it as the customer in the cheque component
        if (this.supplier) {
          // Create a Customer object from Supplier for the cheque component
          const customer = new Customer();
          customer.name = this.supplier.name;
          customer.customerNo = this.supplier.supplierNo;
          customer.address = this.supplier.address;
          customer.telephone1 = this.supplier.telephone1;

          chequeModal.content.cheque.customer = customer;
          chequeModal.content.keyCustomer = this.supplier.name;
        }

        this.modalService.onHide.subscribe(event => {
          if (chequeModal.content.cheque.chequeNo && chequeModal.content.cheque.chequeAmount > 0 &&
            chequeModal.content.cheque.customer.name != 'Default Customer') {
            this.payBalance.cheque = chequeModal.content.cheque;
          }
        });
      }
      return;
    }

    // For sales invoices, handle as before
    this.si.paymentMethod = new MetaData();
    this.si.paymentMethod.id = event.target.value;

    if (this.si.paymentMethod != null && this.si.paymentMethod.id == this.chequeId) {
      this.payBalance.amount = 0;
      let chequeModal = this.modalService.show(ChequePaymentComponent, <ModalOptions>{
        class: 'modal-lg',
        ignoreBackdropClick: true,
        initialState: {
          isModal: true,
          chequeType: 'RECEIVED', // Always set to RECEIVED for sales invoices
          hideTypeSelection: true // Hide the type selection UI
        }
      });
      chequeModal.content.modalRef = chequeModal;
      chequeModal.content.totalAmount = this.si.totalAmount;
      chequeModal.content.cashAmount = this.si.payment;
      if (this.si.customerNo != null) {
        // Find customer by customerNo
        this.customerService.findByCustomerNo(this.si.customerNo).subscribe(customer => {
          if (customer) {
            chequeModal.content.cheque.customer = customer as Customer;
            chequeModal.content.keyCustomer = this.si.customerName;
          }
        });
      }
      this.modalService.onHide.subscribe(event => {
        if (chequeModal.content.cheque.chequeNo && chequeModal.content.cheque.chequeAmount > 0 &&
          chequeModal.content.cheque.customer.name != 'Default Customer') {
          this.payBalance.cheque = chequeModal.content.cheque;
        }
      })
    }
  }

}
