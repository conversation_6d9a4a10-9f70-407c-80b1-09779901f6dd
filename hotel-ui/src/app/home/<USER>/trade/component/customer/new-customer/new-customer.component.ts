import {Component, OnInit, ViewChild} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../../core/service/notification.service';
import {Customer} from '../../../model/customer';
import {MetaDataService} from '../../../../../core/service/metaData.service';
import {CustomerService} from "../../../service/customer.service";
import {Response} from "../../../../../core/model/response";

@Component({
  selector: 'app-new-customer',
  templateUrl: './new-customer.component.html',
  styleUrls: ['./new-customer.component.css']
})

export class NewCustomerComponent implements OnInit {
  @ViewChild('photo', {static: true}) photo;

  customer: Customer;
  customerSearchList: Array<Customer> = [];
  nicAvailability = false;
  modalRef: BsModalRef;
  isEdit: boolean;
  isModal = false;
  type: string;
  salutations: Array<string>;

  constructor(public customerService: CustomerService,
              public notificationService: NotificationService,
              public metaDataService: MetaDataService) {
  }

  ngOnInit() {
    // Only initialize a new customer if we're not in edit mode
    if (!this.customer || !this.customer.id) {
      this.customer = new Customer();
      this.customer.active = true;
    }

    this.salutations = ['Mr.', 'Mrs.', 'Ms.', 'BM.', 'AD.', 'CC.'];
  }



  gotoTop() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }

  checkNic() {
    this.customerService.checkNic(this.customer.nicBr).subscribe((res: boolean) => {
      this.nicAvailability = res;
    });
  }

  savePerson(form: NgForm) {
    this.customerService.save(this.customer).subscribe((result: Response) => {
      if (result.success) {
        this.notificationService.showSuccess('Customer Saved Successfully');
        if (this.isModal) {
          this.modalRef.hide();
        }
        form.reset();
        this.gotoTop();
        this.ngOnInit();
      } else if (!result.success) {
        this.notificationService.showError(result.message);
        this.gotoTop();
        form.reset();
        this.ngOnInit();
        this.customer.active = true;
      }
    })
  }

  clear() {
    // If in edit mode, keep the customer data
    if (this.isEdit && this.customer && this.customer.id) {
      // Keep existing customer data
    } else {
      // Otherwise, create a new customer
      this.customer = new Customer();
      this.customer.active = true;
    }
  }

  setSelectedCustomer(event) {
    this.customer = event.item;
  }

  loadCustomerByNic() {
    if (this.customer.nicBr !== '') {
      this.customerService.findByNicLike(this.customer.nicBr).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
      });
    } else {
      this.ngOnInit();
    }
  }
}

