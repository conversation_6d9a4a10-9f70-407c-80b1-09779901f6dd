import {Compo<PERSON>, OnInit, OnDestroy, TemplateRef} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ModalOptions} from 'ngx-bootstrap/modal';
import {Customer} from '../../../model/customer';
import {NotificationService} from '../../../../../core/service/notification.service';
import {MetaDataService} from '../../../../../core/service/metaData.service';
import {NewCustomerComponent} from '../new-customer/new-customer.component';
import {CustomerService} from "../../../service/customer.service";

@Component({
  selector: 'app-customer-detail',
  templateUrl: './manage-customer.component.html',
  styleUrls: ['./manage-customer.component.css']
})
export class ManageCustomerComponent implements OnInit, On<PERSON><PERSON>roy {

  modalRef: BsModalRef;
  customers: Array<Customer> = [];
  customerSearchList: Array<Customer> = [];

  selectedRow: number;
  setClickedRow: Function;

  keyName: string;
  keyNic: string;
  keyTp: string;

  page;
  collectionSize;
  pageSize;
  maxSize: number = 5; // Maximum number of page links to display

  customer: Customer;

  active: boolean = false;
  disableSetCustomer: boolean = true;
  isModal: boolean = false;

  // Store reference to resize handler
  private resizeHandler: any;

  constructor(private notificationService: NotificationService, private modalService: BsModalService,
              private customerService: CustomerService, public metaDataService: MetaDataService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;

    // Set maxSize based on screen width
    this.setResponsiveMaxSize();

    // Listen for window resize events
    this.resizeHandler = this.setResponsiveMaxSize.bind(this);
    window.addEventListener('resize', this.resizeHandler);

    this.findAll();
    this.keyName = null;
  }

  /**
   * Set the maxSize property based on screen width
   */
  setResponsiveMaxSize() {
    const width = window.innerWidth;
    if (width < 400) {
      this.maxSize = 1; // On very small screens, show only the current page
    } else if (width < 576) {
      this.maxSize = 2; // On small mobile screens
    } else if (width < 768) {
      this.maxSize = 3; // On larger mobile screens
    } else if (width < 992) {
      this.maxSize = 5; // On tablets
    } else {
      this.maxSize = 10; // On desktops
    }
  }


  /**
   * Clean up event listeners when component is destroyed
   */
  ngOnDestroy() {
    // Remove the resize event listener
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  findAll() {
    this.customerService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.customers = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  setFilteredCustomer(event) {
    this.customer = event.item;
    this.customers = [];
    this.customers.push(this.customer);
  }

  customerDetail(selectedItem: any, index) {
    this.selectedRow = index;
    this.customer = selectedItem;
  }

  loadCustomer() {
    if (this.keyName !== '') {
      this.customerService.findByNameLike(this.keyName).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyNic = '';
        this.keyTp = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  loadCustomerByNic() {
    if (this.keyNic !== '') {
      this.customerService.findByNicLike(this.keyNic).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyName = '';
        this.keyTp = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  loadCustomerByTp() {
    if (this.keyTp !== '') {
      this.customerService.findByTpLike(this.keyTp).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyNic = '';
        this.keyName = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  openModal(isEdit: boolean) {
    // Ensure a customer is selected
    if (!this.customer) {
      this.notificationService.showError('Please select a customer first');
      return;
    }

    const initialState = {
      isModal: true,
      isEdit: isEdit
    };

    this.modalRef = this.modalService.show(NewCustomerComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: initialState
    });

    // Make a copy of the customer to avoid reference issues
    this.modalRef.content.customer = {...this.customer};

    this.modalRef.content.modalRef = this.modalRef;

    // Unsubscribe from previous subscriptions to avoid memory leaks
    const subscription = this.modalService.onHide.subscribe(() => {
      this.findAll();
      subscription.unsubscribe(); // Clean up subscription when done
    });
  }

  searchActiveResult(e) {
    if (e.target.checked) {
      this.active = true;
    } else {
      this.active = false;
      this.findAll();
    }
  }

  setSelectedCustomer() {
    this.modalRef.hide();
  }

}
