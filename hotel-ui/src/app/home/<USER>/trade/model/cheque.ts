import {MetaData} from '../../../core/model/metaData';
import {Customer} from "./customer";
import {Supplier} from "./supplier";

export class Cheque {

  id: string;

  chequeNo: string;

  invoiceNo: string;

  // For purchase invoices (when chequeType is GIVEN)
  purchaseInvoiceNo: string;

  bank: MetaData;

  chequeDate: Date;

  chequeAmount: number;

  comment: string;

  status: MetaData;

  customer: Customer;

  supplier: Supplier;

  // Type of cheque: 'RECEIVED' (from customer) or 'GIVEN' (to supplier)
  chequeType: string;

  createdDate: Date;

  createdBy: string;

  lastModifiedDate: Date;

  lastModifiedBy: string;

}
