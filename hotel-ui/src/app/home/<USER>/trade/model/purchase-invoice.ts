import {Supplier} from './supplier';
import {PurchaseInvoiceRecord} from './purchase-invoice-record';
import {MetaData} from '../../../core/model/metaData';
import {Cheque} from './cheque';

export class PurchaseInvoice {

  id: string;

  purchaseInvoiceNo: string;

  invoiceNo: string;

  date: Date;

  supplier: Supplier;

  purchaseInvoiceRecords: Array<PurchaseInvoiceRecord>;

  totalAmount: number;

  payment: number;

  balance: number;

  dueDate: Date;

  status: MetaData;

  paymentMethod: MetaData;

  cheque: Cheque;

  cashlessAmount: number;

  cashAmount: number;

  cardOrVoucherNo: string;

  discount: number;

}
