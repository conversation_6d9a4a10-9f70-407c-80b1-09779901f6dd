import { QuotationRecord } from "./quotation-record";
import {MetaData} from "../../../core/model/metaData";
import {environment} from "../../../../../environments/environment";

export class Quotation {
    id: string;
    quotationNo: string;
    customerId: string;
    total: number = 0;
    discount: number = 0;
    subTotal: number = 0;
    date: Date = new Date();
    validUntil: Date;
    status: MetaData;
    remarks: string;
    records: QuotationRecord[] = [];
    warehouseCode: number = environment.warehouseCode;
    counter: string;

    createdDate: Date;
    createdBy: string;
    lastModifiedDate: Date;
    lastModifiedBy: string;
}
