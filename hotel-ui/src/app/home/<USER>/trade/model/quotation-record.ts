import { Item } from "../../inventory/model/item";
import {MetaData} from "../../../core/model/metaData";
import {environment} from "../../../../../environments/environment";

export class QuotationRecord {
    id: string;
    itemCode: string;
    itemName: string;
    quotationNo: string;
    item: Item;
    quantity: number = 0;
    unitPrice: number = 0;
    unitPriceOriginal: number = 0;
    subTotal: number = 0;
    discount: number = 0;
    price: number = 0;
    date: Date = new Date();
    recodeType: MetaData;
    warehouseCode: number = environment.warehouseCode;
    counter: string;

    createdDate: Date;
    createdBy: string;
    lastModifiedDate: Date;
    lastModifiedBy: string;
}
