import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CreatePiComponent} from './component/create-pi/create-pi.component';
import {SupplierComponent} from './component/supplier/supplier.component';
import {CreateSiComponent} from './component/create-si/create-si.component';
import {ManageSiComponent} from './component/manage-si/manage-si.component';
import {ManagePiComponent} from './component/manage-pi/manage-pi.component';
import {Invoice80Component} from "./component/invoices/invoice-80-en/invoice-80.component";
import {PayBalanceComponent} from "./component/pay-balance/pay-balance.component";
import {NewCustomerComponent} from "./component/customer/new-customer/new-customer.component";
import {ManageCustomerComponent} from "./component/customer/manage-customer/manage-customer.component";
import {ExpenseComponent} from "./component/expense/expense.component";
import {ChequePaymentComponent} from "./component/cheque-payment/cheque-payment.component";
import {ViewCashDrawerComponent} from "./component/cash-drawer/view-cash-drawer/view-cash-drawer.component";
import {CashInComponent} from "./component/cash-drawer/cash-in/cash-in.component";
import {CashOutComponent} from "./component/cash-drawer/cash-out/cash-out.component";
import {DayCloseComponent} from "./component/cash-drawer/day-close/day-close.component";
import {DayStartComponent} from "./component/cash-drawer/day-start/day-start.component";
import {ManageChequeComponent} from "./component/manage-cheque/manage-cheque.component";
import {PaymentHistoryComponent} from "./component/payment-history/payment-history.component";
import {ViewSiComponent} from "./component/view-si/view-si.component";
import {PiPaymentMethodComponent} from "./component/pi-payment-method/pi-payment-method.component";
import {ViewPiComponent} from "./component/view-pi/view-pi.component";
import {InvoiceLegalComponent} from "./component/invoices/invoice-legal/invoice-legal.component";
import {InvoiceLegalCustomComponent} from "./component/invoices/invoice-legal-custom/invoice-legal-custom.component";
import {CardPaymentComponent} from "./component/card-payment/card-payment.component";
import {Invoice80SnComponent} from "./component/invoices/invoice-80-sn/invoice-80-sn.component";
import {ManageQuotationComponent} from "./component/manage-quotation/manage-quotation.component";
import {CreateQuotationComponent} from "./component/create-quotation/create-quotation.component";
import {Invoice58EnComponent} from "./component/invoices/invoice-58-en/invoice58-en.component";
import {Invoice76EnComponent} from "./component/invoices/invoice-76-en/invoice-76-en.component";
import {InvoiceRouterComponent} from "./component/invoice-router/invoice-router.component";
import {ColumnSelectorComponent} from "./component/column-selector/column-selector.component";
import {ManageSiMoreFiltersModalComponent} from "./component/manage-si-more-filters-modal/manage-si-more-filters-modal.component";
import {PageSizeSelectorComponent} from "./component/page-size-selector/page-size-selector.component";
import {NewOrderComponent} from "./component/new-order/new-order.component";
import {ManageOrdersComponent} from "./component/manage-orders/manage-orders.component";

const routes: Routes = [
  {
    path: 'new_purchase_invoice',
    component: CreatePiComponent
  },
  {
    path: 'manage_purchase_invoices',
    component: ManagePiComponent
  },
  {
    path: 'new_sales_invoice',
    component: CreateSiComponent
  },
  {
    path: 'new_quotation',
    component: CreateQuotationComponent
  },
  {
    path: 'new_order',
    component: NewOrderComponent
  },
  {
    path: 'manage_orders',
    component: ManageOrdersComponent
  },
  {
    path: 'manage_sales_invoices',
    component: ManageSiComponent
  },
  {
    path: 'manage_quotation',
    component: ManageQuotationComponent
  },
  {
    path: 'invoice',
    component: Invoice80Component
  },
  {
    path: 'payBalance',
    component: PayBalanceComponent
  },
  {
    path: 'new_customer',
    component: NewCustomerComponent
  },
  {
    path: 'manage_customer',
    component: ManageCustomerComponent
  },
  {
    path: 'manage_supplier',
    component: SupplierComponent
  },
  {
    path: 'new_expense',
    component: ExpenseComponent
  },
  {
    path: 'manage_cheque',
    component: ManageChequeComponent
  },
  {
    path: 'cashier',
    component: ViewCashDrawerComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class TradeRoutingModule {
}

export const tradeRouteParams = [CreatePiComponent, SupplierComponent, PayBalanceComponent,
  CreatePiComponent, ManagePiComponent, CreateSiComponent, ManageSiComponent, ExpenseComponent,
  CardPaymentComponent, ManageChequeComponent, ViewCashDrawerComponent, CashInComponent,
  CashOutComponent, DayCloseComponent, DayStartComponent, NewCustomerComponent, ManageCustomerComponent,
  PaymentHistoryComponent, ExpenseComponent, ViewSiComponent, PiPaymentMethodComponent, ViewPiComponent,
  Invoice80Component, InvoiceLegalComponent, InvoiceLegalCustomComponent, ChequePaymentComponent,
  Invoice80SnComponent, CreateQuotationComponent, ManageQuotationComponent, Invoice58EnComponent,
  Invoice76EnComponent, InvoiceRouterComponent, ColumnSelectorComponent,
  ManageSiMoreFiltersModalComponent, PageSizeSelectorComponent, NewOrderComponent, ManageOrdersComponent];
