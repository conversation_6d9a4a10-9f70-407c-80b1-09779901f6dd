<div class="container-fluid px-0">
  <h2 class="component-title">Profit Report</h2>
  <div class="row mb-3">
    <div class="col-12 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">Duration</label>
      <select type="text" required #duration="ngModel" [class.is-invalid]="duration.invalid && duration.touched"
              class="form-control" id="duration" [(ngModel)]="selectedDuration.id" name="duration"
              (ngModelChange)="filterByDuration()">
        <option *ngFor="let filter of durationFilter" [value]="filter.id">{{filter.value}}</option>
      </select>
    </div>
    <div class="col-12 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">Profit Type</label>
      <select class="form-control" id="profitType" [(ngModel)]="selectedProfitType" name="profitType"
              (ngModelChange)="onProfitTypeChange()">
        <option value="realized">Realized Profit</option>
        <option value="unrealized">Unrealized Profit</option>
      </select>
    </div>
    <div class="col-6 col-sm-6 col-md-3 mb-2">
      <label class="form-label d-block d-md-none">Start Date</label>
      <input required #startDate="ngModel" type="text" name="startDate" id="startDate"
             [(ngModel)]="sDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             class="form-control" placeholder="Enter Start Date">
    </div>
    <div class="col-6 col-sm-6 col-md-3 mb-2">
      <label class="form-label d-block d-md-none">End Date</label>
      <input required #endDate="ngModel" type="text" name="endDate" id="endDate"
             [(ngModel)]="eDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             class="form-control" placeholder="Enter End Date">
    </div>
    <div class="col-12 col-sm-6 col-md-2 d-flex">
      <div class="form-group mr-2 flex-grow-1">
        <button class="btn btn-primary btn-block" (click)="openMoreFiltersModal()" title="More Filters">
          <i class="fa fa-filter"></i>
        </button>
      </div>
      <div class="form-group mr-2 flex-grow-1">
        <button class="btn btn-outline-secondary btn-block" (click)="clearFilters()">
          <i class="fa fa-times"></i>
        </button>
      </div>
      <div class="form-group flex-grow-1">
        <button class="btn btn-primary btn-block" (click)="searchBetweenDates()">
          <i class="fa fa-search"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Active Additional Filters Display -->
  <div class="row mt-2" *ngIf="selectedCashDrawer || selectedCashierUser || selectedRoute">
    <div class="col-12">
      <div class="alert alert-info py-2">
        <strong>Active Filters:</strong>
        <span *ngIf="selectedCashDrawer" class="badge badge-primary ml-2 mr-2">
          Cash Drawer: {{ selectedCashDrawer.drawerNo }} - {{ selectedCashDrawer.userName || 'Unknown' }}
        </span>
        <span *ngIf="selectedCashierUser" class="badge badge-primary ml-2 mr-2">
          User: {{ selectedCashierUser.username }}
        </span>
        <span *ngIf="selectedRoute" class="badge badge-primary ml-2 mr-2">
          Route: {{ selectedRoute.name + '-' + selectedRoute.routeNo }}
        </span>
        <span class="text-muted ml-2">Click Search to apply filters</span>
      </div>
    </div>
  </div>

  <!-- Main content area -->
  <div class="row mt-2" id="print-income-div">
    <!-- Print Header - Only visible when printing -->
    <div class="col-12 d-none d-print-block mb-4">
      <div class="text-center mb-3">
        <h3 class="mb-0">Profit Report</h3>
        <p class="mb-1" *ngIf="sDate && eDate">
          {{ formatDateForDisplay(sDate) }} - {{ formatDateForDisplay(eDate) }}
        </p>
        <p class="mb-3">{{ selectedProfitType === 'realized' ? 'Realized Profit' : 'Unrealized Profit' }}</p>

        <!-- Filter details -->
        <div class="row justify-content-center">
          <div class="col-auto">
            <p class="mb-0" *ngIf="selectedCashDrawer">
              <strong>Cash Drawer:</strong> {{ selectedCashDrawer.drawerNo }} - {{ selectedCashDrawer.userName || 'Unknown' }}
            </p>
            <p class="mb-0" *ngIf="selectedCashierUser">
              <strong>User:</strong> {{ selectedCashierUser.username }}
            </p>
            <p class="mb-0" *ngIf="selectedRoute">
              <strong>Route:</strong> {{ selectedRoute.name + '-' + selectedRoute.routeNo }}
            </p>
          </div>
        </div>
      </div>
      <hr class="mb-4">
    </div>

    <div class="col-12 table-responsive" style="max-height: 60vh; overflow-y: auto;">
      <table class="table table-striped table-hover">
        <thead class="table-light text-center sticky-top bg-white">
        <tr>
          <th scope="col">Barcode</th>
          <th scope="col">Name</th>
          <th scope="col">Quantity</th>
          <th scope="col">Profit</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let rec of itemSaleSummary,let i = index" class="text-center">
          <td>{{rec.barcode}}</td>
          <td>{{rec.itemName}}</td>
          <td>{{rec.quantity || 0}}</td>
          <td>{{rec.profit | number : '1.2-2'}}</td>
        </tr>
        <tr *ngIf="!itemSaleSummary || itemSaleSummary.length === 0">
          <td colspan="4" class="text-center">No profit data available for the selected period</td>
        </tr>
        </tbody>
      </table>
    </div>

    <!-- Print Footer - Only visible when printing -->
    <div class="col-12 d-none d-print-block mt-4">
      <div class="row">
        <div class="col-12">
          <p class="mb-0"><strong>Total {{ selectedProfitType === 'realized' ? 'Realized' : 'Unrealized' }} Profit:</strong> {{ totalAmount | number : '1.2-2' }}</p>
          <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <span class="font-weight-bold mb-0">Total {{ selectedProfitType === 'realized' ? 'Realized' : 'Unrealized' }} Profit: </span>
                <span class="font-weight-bold mb-0 text-primary">{{ totalAmount | number : '1.2-2' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-danger" printSectionId="print-income-div" ngxPrint
                  [useExistingCss]="true" printTitle="Profit Report">
            <i class="fa fa-print"></i> Print
          </button>
        </div>
      </div>
    </div>
  </div>

  <ngx-loading
    [show]="loading"
    [config]="{ backdropBorderRadius: '3px', fullScreenBackdrop:true }">
  </ngx-loading>
</div>
