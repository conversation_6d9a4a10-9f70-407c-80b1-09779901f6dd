/* Import common report styles */
@import '../../report-common.css';

.component-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #007bff;
  padding-bottom: 0.5rem;
}

.card {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: transform 0.15s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-title {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.card h3 {
  font-size: 1.5rem;
  font-weight: 700;
}

.table {
  margin-bottom: 0;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
  padding: 0.75rem 0.5rem;
}

.table td {
  padding: 0.75rem 0.5rem;
  vertical-align: middle;
  font-size: 0.875rem;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.badge-sm {
  font-size: 0.65rem;
  padding: 0.2rem 0.4rem;
}

.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.alert {
  border: none;
  border-radius: 0.5rem;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.spinner-border {
  width: 2rem;
  height: 2rem;
}

.content-section {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 1rem;
}

.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-muted {
  color: #6c757d !important;
}

/* Print styles */
@media print {
  .d-print-none {
    display: none !important;
  }
  
  .component-title {
    color: #000 !important;
    border-bottom: 2px solid #000 !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  .table th {
    background-color: #f0f0f0 !important;
    color: #000 !important;
  }
  
  .badge {
    border: 1px solid #000 !important;
  }
  
  .alert {
    border: 1px solid #000 !important;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .component-title {
    font-size: 1.5rem;
  }
  
  .card h3 {
    font-size: 1.25rem;
  }
  
  .table-responsive {
    font-size: 0.8rem;
  }
  
  .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.8rem;
  }
}
