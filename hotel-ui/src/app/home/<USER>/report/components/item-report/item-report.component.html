<div class="container-fluid">
  <h2 class="text-left mb-4">ITEM REPORT</h2>

  <!-- Filter Section -->
  <div class="row mb-3">
    <div class="col-md-2">
      <label for="categorySearch">Category</label>
      <input type="text"
             id="categorySearch"
             class="form-control"
             [(ngModel)]="keyCategorySearch"
             [typeahead]="categorySearchResults"
             (typeaheadLoading)="loadCategories()"
             (typeaheadOnSelect)="setSelectedCategory($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="categoryName"
             placeholder="Search category..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
    <div class="col-md-2">
      <label for="brandSearch">Brand</label>
      <input type="text"
             id="brandSearch"
             class="form-control"
             [(ngModel)]="keyBrandSearch"
             [typeahead]="brandSearchResults"
             (typeaheadLoading)="loadBrands()"
             (typeaheadOnSelect)="setSelectedBrand($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="name"
             placeholder="Search brand..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
    <div class="col-md-2">
      <label for="modelSearch">Model</label>
      <input type="text"
             id="modelSearch"
             class="form-control"
             [(ngModel)]="keyModelSearch"
             [typeahead]="modelSearchResults"
             (typeaheadLoading)="loadModels()"
             (typeaheadOnSelect)="setSelectedModel($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="name"
             placeholder="Search model..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
    <div class="col-md-2">
      <label for="supplierSearch">Supplier</label>
      <input type="text"
             id="supplierSearch"
             class="form-control"
             [(ngModel)]="keySupplierSearch"
             [typeahead]="supplierSearchResults"
             (typeaheadLoading)="loadSuppliers()"
             (typeaheadOnSelect)="setSelectedSupplier($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="name"
             placeholder="Search supplier..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
    <div class="col-md-3 d-flex align-items-end">
      <button type="button" class="btn btn-primary mr-2" (click)="applyFilters()">
        <i class="fa fa-search"></i> Search
      </button>
      <button type="button" class="btn btn-secondary mr-2" (click)="clearFilters()">
        <i class="fa fa-times"></i> Clear
      </button>
      <button type="button" class="btn btn-primary mr-2" (click)="openFilterModal()">
        <i class="fa fa-filter"></i> More
      </button>
    </div>
  </div>

  <!-- Active Filters Display -->
  <div class="row mb-3" *ngIf="activeFilters.length > 0">
    <div class="col-12">
      <div class="alert alert-info py-2">
        <strong>Active Filters:</strong>
        <span *ngFor="let filter of activeFilters" class="badge badge-primary ml-2">
          {{ filter }}
        </span>
      </div>
    </div>
  </div>

  <!-- Items Table -->
  <div id="print-item-report" class="content-section p-0">
    <div class="table-responsive">
      <table class="table table-striped table-hover">
        <thead class="table-light">
        <tr class="text-center">
          <th scope="col">Barcode</th>
          <th scope="col">Item Name</th>
          <th scope="col">Category</th>
          <th scope="col">Brand</th>
          <th scope="col">Model</th>
          <th scope="col">Supplier</th>
          <th scope="col">Cost (LKR)</th>
          <th scope="col">Selling Price (LKR)</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of items; let i = index" class="text-center">
          <td>{{ item.barcode }}</td>
          <td class="text-left">{{ item.itemName }}</td>
          <td>{{ item.itemCategory?.categoryName || '-' }}</td>
          <td>{{ item.brand?.name || '-' }}</td>
          <td>{{ item.model?.name || '-' }}</td>
          <td>{{ item.supplier?.name || '-' }}</td>
          <td>{{ item.itemCost | number:'1.2-2' }}</td>
          <td>{{ item.sellingPrice | number:'1.2-2' }}</td>
        </tr>
        <tr *ngIf="items.length === 0 && !isLoading">
          <td colspan="9" class="text-center text-muted py-4">
            <i class="fa fa-inbox fa-2x mb-2"></i>
            <br>
            No items found. Try adjusting your filters or refresh the data.
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <!-- Print Footer - Only visible when printing -->
    <div class="col-12 d-none d-print-block mt-4">
      <div class="row">
        <div class="col-12">
          <p class="mb-0"><strong>Total Items:</strong> {{ totalItems }}</p>
          <p class="mb-0"><strong>Total Value:</strong> {{ totalValue | number : '1.2-2' }} LKR</p>
          <p class="mb-0"><strong>Total Cost:</strong> {{ totalCost | number : '1.2-2' }} LKR</p>
          <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading items...</p>
  </div>

  <!-- Error Message -->
  <div *ngIf="hasError" class="alert alert-danger mt-3">
    <i class="fa fa-exclamation-triangle"></i>
    {{ errorMessage }}
    <button class="btn btn-outline-danger btn-sm ml-2" (click)="refreshData()">
      <i class="fa fa-retry"></i> Retry
    </button>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <span class="font-weight-bold mb-0">Total Items: </span>
                <span class="font-weight-bold mb-0 text-primary">{{ totalItems }}</span>
                <span class="font-weight-bold mb-0 ml-3">Total Value: </span>
                <span class="font-weight-bold mb-0 text-success">{{ totalValue | number : '1.2-2' }} LKR</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-danger" printSectionId="print-item-report" ngxPrint
                  [useExistingCss]="true" printTitle="Item Report">
            <i class="fa fa-print"></i> Print
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
