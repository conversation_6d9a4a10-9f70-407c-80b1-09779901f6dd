import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {SalesInvoice} from "../../../trade/model/sales-invoice";
import {SalesInvoiceService} from "../../../trade/service/sales-invoice.service";
import {Invoice80Component} from "../../../trade/component/invoices/invoice-80-en/invoice-80.component";
import {PayBalanceComponent} from "../../../trade/component/pay-balance/pay-balance.component";
import {PaymentHistoryComponent} from "../../../trade/component/payment-history/payment-history.component";
import {Customer} from "../../../trade/model/customer";
import {CustomerService} from "../../../trade/service/customer.service";

@Component({
  selector: 'app-manage-si',
  templateUrl: './credit-report.component.html',
  styleUrls: ['./credit-report.component.css']
})
export class CreditReportComponent implements OnInit {

  sis: Array<SalesInvoice> = [];
  selectedSi: SalesInvoice;

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];
  keyCustomer: string;

  selectedRow: number;
  modalRef: BsModalRef;

  public loading = false;

  totalAmount: number;

  constructor(private siService: SalesInvoiceService, private customerService: CustomerService,
              private modalService: BsModalService) {
  }

  ngOnInit() {
    this.selectedSi = new SalesInvoice();
    this.findAllSis();
  }

  findAllSis() {
    this.loading = true;
    this.siService.findAllIncomplete().subscribe((result: any) => {
      this.sis = result;
      this.calculateTotalAmount();
      this.loading = false;
    });
  }

  filterByCustomer() {
    this.loading = true;
    this.siService.findPendingByCustomer(this.keyCustomer).subscribe((result: any) => {
      this.sis = result;
      this.calculateTotalAmount();
      this.loading = false;
    });
  }

  selectSi(si, index) {
    this.selectedRow = index;
    this.selectedSi = si;
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  setSelectedCustomer(event) {
    this.keyCustomer = event.item.nicBr;
  }

  print() {
    this.modalRef = this.modalService.show(Invoice80Component, <ModalOptions>{class: 'modal-sm'});
    this.modalRef.content.invoiceNo = this.selectedSi.invoiceNo;
    this.modalRef.content.pastBill = true;
    this.modalRef.content.findInvoice();
  }

  paymentHistory() {
    this.modalRef = this.modalService.show(PaymentHistoryComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.invoice = this.selectedSi;
    this.modalRef.content.findAllTransactions();
  }

  payBalance() {
    this.modalRef = this.modalService.show(PayBalanceComponent, <ModalOptions>{
      class: 'modal-md',
      initialState: {
        isModal: true
      }
    });
    this.modalRef.content.si = this.selectedSi;
    this.modalRef.content.bsModalRef = this.modalRef;
    // Ensure isModal is set to true
    this.modalRef.content.isModal = true;

    // Use a subscription variable to properly clean up
    const subscription = this.modalService.onHide.subscribe(() => {
      this.findAllSis();
      // Clean up subscription to avoid memory leaks
      subscription.unsubscribe();
    });
  }

  calculateTotalAmount() {
    this.totalAmount = 0;
    for (let tr of this.sis) {
      if (null != tr.balance)
        this.totalAmount = this.totalAmount + tr.balance;
    }
  }

  /**
   * Get current date for print footer
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString();
  }
}
