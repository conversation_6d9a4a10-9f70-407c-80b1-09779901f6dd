<div class="container-fluid px-0">
  <h2 class="component-title">Cash In Out Report</h2>
    <div class="row g-2 mb-3">
      <div class="col-12 col-sm-6 col-md-3 mb-2">
        <label class="form-label d-block d-md-none">Record Type</label>
        <select type="text" required #duration="ngModel" [class.is-invalid]="duration.invalid && duration.touched"
                class="form-control" id="duration" [(ngModel)]="selectedCashRecordTypeId" name="duration"
                (change)="setSelectedType()">
          <option [value]="undefined" disabled>Select a Type</option>
          <option *ngFor="let type of cashRecordTypes" [value]="type.id">{{type.value}}</option>
        </select>
      </div>  <div class="col-6 col-sm-6 col-md-3 mb-2">
        <label class="form-label d-block d-md-none">Start Date</label>
        <input required #startDateFor="ngModel" type="text" name="startDate" id="startDate"
               [(ngModel)]="startDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Enter Start Date">
      </div>  <div class="col-6 col-sm-6 col-md-3 mb-2">
        <label class="form-label d-block d-md-none">End Date</label>
        <input required #endDateFor="ngModel" type="text" name="endDate" id="endDate"
               [(ngModel)]="endDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Enter End Date">
      </div>  <div class="col-12 col-sm-6 col-md-3">
        <button class="btn btn-primary btn-block" (click)="search()">Search</button>
      </div>
    </div>  <div class="row mt-2" id="print-cashInOUt-div">
      <div class="col-12">
        <div class="table-responsive" style="max-height: calc(100vh - 350px); overflow-y: auto;">
          <table class="table table-striped table-hover">
            <thead class="table-light text-center sticky-top">
            <tr>
              <th scope="col" class="d-none d-md-table-cell">Date</th>
              <th scope="col" class="d-none d-md-table-cell">User</th>
              <th scope="col">Type</th>
              <th scope="col">Reason</th>
              <th scope="col">Amount</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let record of cashRecords,let i = index"
                (click)="selectRecord(record,i)"
                [class.active]="i === selectedRow" class="text-center">
              <td class="d-none d-md-table-cell">{{record.date | date:'short'}}</td>
              <td class="d-none d-md-table-cell">{{record.createdBy}}</td>
              <td>{{record.type ? record.type.value : "N/A"}}</td>
              <td>{{record.purpose ? record.purpose.value : "N/A"}}</td>
              <td>{{record.amount| number : '1.2-2'}}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Print Footer - Only visible when printing -->
      <div class="col-12 d-none d-print-block mt-4">
        <div class="row">
          <div class="col-12">
            <p class="mb-0"><strong>Total Records:</strong> {{ cashRecords?.length || 0 }}</p>
            <p class="mb-0"><strong>Total Amount:</strong> {{ getTotalAmount() | number : '1.2-2' }} LKR</p>
            <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile view for selected record details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedRow !== undefined && cashRecords && cashRecords.length > 0 && selectedRow !== null">
      <div class="card bg-light">
        <div class="card-body">
          <h5 class="card-title">Selected Record Details</h5>
          <div class="row">
            <div class="col-6">
              <p class="mb-1 font-weight-bold">Date:</p>
              <p>{{cashRecords[selectedRow].date | date:'short'}}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 font-weight-bold">User:</p>
              <p>{{cashRecords[selectedRow].createdBy}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Spacer for fixed footer - ensures enough space for all labels -->
    <div style="margin-bottom: 80px;"></div>

    <!-- Fixed Footer with Actions - Hidden in print -->
    <div class="fixed-bottom bg-white border-top py-2 d-print-none"
         style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
      <div class="container-fluid">
        <div class="row align-items-start">
          <!-- Summary Information -->
          <div class="col-12 col-md-6 mb-1 mb-md-0">
            <div class="d-flex flex-column">
              <div class="row">
                <div class="col-12 text-left">
                  <span class="font-weight-bold mb-0">Total Records: </span>
                  <span class="font-weight-bold mb-0 text-primary">{{ cashRecords?.length || 0 }}</span>
                  <span class="font-weight-bold mb-0 ml-3">Total Amount: </span>
                  <span class="font-weight-bold mb-0 text-success">{{ getTotalAmount() | number : '1.2-2' }} LKR</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="col-12 col-md-6 text-right">
            <button type="button" class="btn btn-danger" printSectionId="print-cashInOUt-div" ngxPrint
                    [useExistingCss]="true" printTitle="Cash In Out Report">
              <i class="fa fa-print"></i> Print
            </button>
          </div>
        </div>
      </div>
    </div>

