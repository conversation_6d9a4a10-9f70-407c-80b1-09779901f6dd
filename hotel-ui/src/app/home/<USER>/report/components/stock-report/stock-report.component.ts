import { Component, OnInit, OnDestroy } from '@angular/core';
import { StockService } from "../../../inventory/service/stock.service";
import { ItemService } from "../../../inventory/service/item.service";
import { StockSummary } from "../../../inventory/model/stockSummary";
import { Subscription } from 'rxjs';
import { finalize, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { StockReportFilterModalComponent } from './filter-modal/filter-modal.component';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-stock-report',
  templateUrl: './stock-report.component.html',
  styleUrls: ['./stock-report.component.css']
})
export class StockReportComponent implements OnInit, OnDestroy {
  // Data properties
  stockSummaryObs: Array<StockSummary> = [];

  // Summary statistics
  noOfItems: number = 0;
  totalQuantity: number = 0;
  totalCostValue: number = 0;
  totalPriceValue: number = 0;

  // UI state properties
  isLoading: boolean = false;
  hasError: boolean = false;
  errorMessage: string = '';

  // Modal reference
  modalRef: BsModalRef;

  // Group by option
  groupByOption: string = '';

  // Subscription management
  private subscriptions: Subscription = new Subscription();

  constructor(
    private stockService: StockService,
    private modalService: BsModalService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.findStockSummary();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.unsubscribe();
  }

  /**
   * Fetches stock summary data from the service
   */
  findStockSummary(): void {
    // Reset values before new data fetch
    this.resetValues();
    this.isLoading = true;

    const stockSummarySubscription = this.stockService.findStockSummary()
      .pipe(
        catchError(error => {
          this.hasError = true;
          this.errorMessage = 'Failed to load stock summary. Please try again later.';
          console.error('Error fetching stock summary:', error);
          return of([] as Array<StockSummary>);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((result: Array<StockSummary>) => {
        this.stockSummaryObs = result;
        if (result.length > 0) {
          this.calculateCumulativeVals(result);
        }
      });

    this.subscriptions.add(stockSummarySubscription);
  }

  /**
   * Calculates cumulative values from stock summaries
   * @param summaries Array of stock summary items
   */
  calculateCumulativeVals(summaries: Array<StockSummary>): void {
    // More efficient than updating in a loop (reduces potential reflows/repaints)
    this.noOfItems = summaries.reduce((sum, item) => sum + item.noOfItems, 0);
    this.totalQuantity = summaries.reduce((sum, item) => sum + item.totalQuantity, 0);
    this.totalCostValue = summaries.reduce((sum, item) => sum + item.totalCostValue, 0);
    this.totalPriceValue = summaries.reduce((sum, item) => sum + item.totalPriceValue, 0);
  }

  /**
   * Resets all cumulative values and state
   */
  resetValues(): void {
    this.stockSummaryObs = [];
    this.noOfItems = 0;
    this.totalQuantity = 0;
    this.totalCostValue = 0;
    this.totalPriceValue = 0;
    this.hasError = false;
    this.errorMessage = '';
  }

  /**
   * Open filter modal for grouping stock items
   */
  openFilterModal(): void {
    // Create modal with current group by settings
    const modalRef = this.modalService.show(StockReportFilterModalComponent, <ModalOptions>{ class: 'modal-md' });

    // Set initial values
    modalRef.content.groupByOption = this.groupByOption;

    // Subscribe to modal close event to get selected filters
    modalRef.content.modalRef = modalRef;

    // When modal is closed, update filters
    const modalSubscription = this.modalService.onHide.subscribe(() => {
      if (modalRef && modalRef.content) {
        // Get selected filters from modal
        this.groupByOption = modalRef.content.groupByOption;

        // Update active filters display
        this.updateActiveFilters();
      }
    });

    // Add the subscription to our collection for cleanup
    this.subscriptions.add(modalSubscription);
  }

  /**
   * Update active filters display and apply filters
   */
  updateActiveFilters(): void {
    // This method will be called after filters are applied
    // For now, we'll just log the selected filters
    console.log('Group by option:', this.groupByOption);

    // In a real implementation, we would call the API with these filters
    // For now, we'll just show a message
    if (this.groupByOption) {
      this.toastr.info(`Group by ${this.groupByOption} applied. In a real implementation, this would be sent to the API.`, 'Group By');
    }
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.groupByOption = '';

    // Refresh data
    this.refreshData();
  }

  /**
   * Generates a detailed stock report as PDF
   */
  getDetailReport(): void {
    this.isLoading = true;

    // Log the grouping option being used
    console.log('Generating report with groupBy:', this.groupByOption);

    // Show a notification about the grouping
    if (this.groupByOption) {
      this.toastr.info(`Generating report grouped by ${this.groupByOption}`, 'Report');
    }

    this.stockService.getDetailReport(this.groupByOption)
      .pipe(
        catchError(error => {
          this.hasError = true;
          this.errorMessage = 'Failed to generate PDF report. Please try again later.';
          console.error('Error generating PDF report:', error);
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe(response => {
        if (response && response.body) {
          // Create blob from response
          const blob = new Blob([response.body], { type: 'application/pdf' });
          const url = window.URL.createObjectURL(blob);

          // Get filename from content-disposition header or use default
          const contentDisposition = response.headers.get('content-disposition');
          let filename = 'stock-report.pdf';
          if (contentDisposition) {
            const filenameMatch = /filename[^;=]*=((['"]).*?\2|[^;]*)/.exec(contentDisposition);
            if (filenameMatch && filenameMatch[1]) {
              filename = filenameMatch[1].replace(/['"]/g, '');
            }
          }

          // Create link and trigger download
          const link = document.createElement('a');
          link.href = url;
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }
      });
  }

  /**
   * Refreshes the stock data
   */
  refreshData(): void {
    this.findStockSummary();
  }

  // ngOnDestroy is already defined above
}
