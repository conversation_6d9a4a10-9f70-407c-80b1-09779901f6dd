<div class="container-fluid px-0">
  <h2 class="component-title">STOCK REPORT</h2>
    <div class="card mb-4" *ngFor="let summary of stockSummaryObs">
      <div class="card-body">
        <h5 class="card-title">{{summary.warehouseName}}</h5>
        <div class="row">
          <div class="col-6 col-md-4 mb-2">
            <p class="mb-1 font-weight-bold">Total No Of Items:</p>
            <p>{{summary.noOfItems | number:'1.2-2'}}</p>
          </div>  <div class="col-6 col-md-4 mb-2">
            <p class="mb-1 font-weight-bold">Total Quantity:</p>
            <p>{{summary.totalQuantity | number:'1.2-2'}}</p>
          </div>  <div class="col-6 col-md-4 mb-2">
            <p class="mb-1 font-weight-bold">Total Stock Price Value:</p>
            <p>{{summary.totalPriceValue | number:'1.2-2'}} LKR</p>
          </div>  <div class="col-6 col-md-4 mb-2">
            <p class="mb-1 font-weight-bold">Total Stock Cost Value:</p>
            <p>{{summary.totalCostValue | number:'1.2-2'}} LKR</p>
          </div>
        </div>
      </div>
    </div>  <div class="card mb-4">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Cumulative Values</h5>
      </div>  <div class="card-body">
        <div class="row">
          <div class="col-6 col-md-3 mb-2">
            <p class="mb-1 font-weight-bold">Total No Of Items:</p>
            <p>{{noOfItems | number:'1.2-2'}}</p>
          </div>  <div class="col-6 col-md-3 mb-2">
            <p class="mb-1 font-weight-bold">Total Quantity:</p>
            <p>{{totalQuantity | number:'1.2-2'}}</p>
          </div>  <div class="col-6 col-md-3 mb-2">
            <p class="mb-1 font-weight-bold">Total Stock Price Value:</p>
            <p>{{totalPriceValue | number:'1.2-2'}} LKR</p>
          </div>  <div class="col-6 col-md-3 mb-2">
            <p class="mb-1 font-weight-bold">Total Stock Cost Value:</p>
            <p>{{totalCostValue | number:'1.2-2'}} LKR</p>
          </div>
        </div>
      </div>
    </div>  <!-- Active Group By Option Display -->
  <div class="row mt-2" *ngIf="groupByOption">
    <div class="col-12">
      <div class="alert alert-info py-2">
        <strong>Group By:</strong>
        <span class="badge badge-primary ml-2 mr-2">
          {{ groupByOption | titlecase }}
        </span>
      </div>
    </div>
  </div>

  <div class="row mt-3 mb-5"> <!-- Added mb-5 for bottom margin -->
      <div class="col-12 d-flex justify-content-between align-items-center">
        <div>
          <button class="btn btn-primary mr-2" (click)="openFilterModal()" title="Group By Filters">
            <i class="fa fa-filter"></i> Group By
          </button>
          <button class="btn btn-outline-secondary mr-2" (click)="clearFilters()" *ngIf="groupByOption">
            <i class="fa fa-times"></i> Clear Filters
          </button>
          <span class="text-muted ml-2" *ngIf="!groupByOption">
            Use Group By to filter items by Category, Brand, Supplier or Model
          </span>
        </div>
        <div>
          <button class="btn btn-danger mr-2" (click)="getDetailReport()">
            <i class="fa fa-file-pdf"></i> Detail Report
          </button>
          <button class="btn btn-primary" (click)="refreshData()">
            <i class="fa fa-sync"></i> Refresh
          </button>
        </div>
      </div>
  </div>
