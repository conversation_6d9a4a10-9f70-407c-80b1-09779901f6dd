import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-filter-modal',
  templateUrl: './filter-modal.component.html',
  styleUrls: ['./filter-modal.component.css']
})
export class StockReportFilterModalComponent implements OnInit {
  // Form
  filterForm: FormGroup;

  // Loading state
  loading: boolean = false;

  // Group by option
  groupByOption: string = '';

  constructor(
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private toastr: ToastrService
  ) {
    this.filterForm = this.formBuilder.group({
      groupByOption: [''] // Empty string means no grouping
    });
  }

  ngOnInit(): void {
    // Initialize form values from component properties
    this.filterForm.patchValue({
      groupByOption: this.groupByOption
    });
  }

  /**
   * Apply filters and close modal
   */
  applyFilters(): void {
    // Get values from form
    this.groupByOption = this.filterForm.get('groupByOption').value;

    // Close modal
    this.modalRef.hide();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.filterForm.reset({
      groupByOption: ''
    });

    this.groupByOption = '';
  }
}
