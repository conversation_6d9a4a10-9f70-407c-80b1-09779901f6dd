<div class="modal-header">
  <h4 class="modal-title pull-left">Group By Filters</h4>
  <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form [formGroup]="filterForm">
    <!-- Loading indicator -->
    <div *ngIf="loading" class="text-center mb-3">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <p class="mt-2">Loading data...</p>
    </div>

    <h5>Group By Options</h5>
    <p class="text-muted">Select one option to group items in the stock report.</p>

    <div class="form-group">
      <div class="custom-control custom-radio">
        <input type="radio" class="custom-control-input" id="groupByNone" name="groupByOption" [value]="''" formControlName="groupByOption">
        <label class="custom-control-label" for="groupByNone">No Grouping</label>
      </div>
    </div>

    <div class="form-group">
      <div class="custom-control custom-radio">
        <input type="radio" class="custom-control-input" id="groupByCategory" name="groupByOption" value="category" formControlName="groupByOption">
        <label class="custom-control-label" for="groupByCategory">Group by Category</label>
      </div>
    </div>

    <div class="form-group">
      <div class="custom-control custom-radio">
        <input type="radio" class="custom-control-input" id="groupByBrand" name="groupByOption" value="brand" formControlName="groupByOption">
        <label class="custom-control-label" for="groupByBrand">Group by Brand</label>
      </div>
    </div>

    <div class="form-group">
      <div class="custom-control custom-radio">
        <input type="radio" class="custom-control-input" id="groupByModel" name="groupByOption" value="model" formControlName="groupByOption">
        <label class="custom-control-label" for="groupByModel">Group by Model</label>
      </div>
    </div>

    <div class="form-group">
      <div class="custom-control custom-radio">
        <input type="radio" class="custom-control-input" id="groupBySupplier" name="groupByOption" value="supplier" formControlName="groupByOption">
        <label class="custom-control-label" for="groupBySupplier">Group by Supplier</label>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-outline-secondary" (click)="clearFilters()">
    Clear
  </button>
  <button type="button" class="btn btn-primary" (click)="applyFilters()">
    Apply Filters
  </button>
</div>
