<div class="modal-header">
  <h4 class="modal-title pull-left">Additional Filters</h4>
  <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form [formGroup]="filterForm">
    <!-- Loading indicator -->
    <div *ngIf="loading" class="text-center mb-3">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <p class="mt-2">Loading data...</p>
    </div>

    <h5>Filter by Device or User</h5>

    <!-- Cash Drawer Filter -->
    <div class="form-group">
      <label for="cashDrawerNo">Cash Drawer (Device)</label>
      <select class="form-control" id="cashDrawerNo" formControlName="cashDrawerNo">
        <option [ngValue]="null">-- Select Cash Drawer --</option>
        <option *ngFor="let cashDrawer of cashDrawers" [value]="cashDrawer.drawerNo">
          {{ cashDrawer.drawerNo }} - {{ cashDrawer.userName || 'No User Assigned' }}
        </option>
      </select>
      <small class="text-muted" *ngIf="cashDrawers.length === 0">
        No cash drawers found.
      </small>
    </div>

    <!-- User with Cashier Role Filter -->
    <div class="form-group">
      <label for="cashierUserId">Cashier (User)</label>
      <select class="form-control" id="cashierUserId" formControlName="cashierUserId">
        <option [ngValue]="null">-- Select User --</option>
        <option *ngFor="let user of cashierUsers" [value]="user.username">
          {{ user.username }} ({{ user.firstName }} {{ user.lastName }})
        </option>
      </select>
      <small class="text-muted" *ngIf="cashierUsers.length === 0">
        No users with cashier role found.
      </small>
    </div>

    <small class="text-muted">
      Note: You can filter by either Cash Drawer or User, not both at the same time.
    </small>



    <!-- Date range fields removed as they are already available in the main report interface -->
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-outline-secondary" (click)="clearFilters()">
    Clear
  </button>
  <button type="button" class="btn btn-primary" (click)="applyFilters()">
    Apply Filters
  </button>
</div>
