tr.active {
  background-color: #e9ecef !important;
}

tr:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.badge {
  font-size: 90%;
}

.table {
  margin-bottom: 0;
}

.table th, .table td {
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px; /* Limit column width */
}

/* Prevent horizontal scrolling */
.table-responsive {
  overflow-x: visible !important;
  width: 100%;
  max-width: 100%;
}

/* Set specific column widths */
.invoice-no-col {
  width: 100px;
}

.customer-col {
  max-width: 150px;
}

.amount-col {
  width: 80px;
}

.status-col {
  width: 90px;
}

/* Ensure the table fits within its container */
.table {
  width: 100%;
  table-layout: fixed;
}
