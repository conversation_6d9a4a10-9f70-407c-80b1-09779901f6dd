import {Component, OnInit} from '@angular/core';
import {Item} from "../../../inventory/model/item";
import {Stock} from "../../../inventory/model/stock";
import {Warehouse} from "../../../inventory/model/warehouse";
import {ItemCategory} from "../../../inventory/model/item-category";
import {Brand} from "../../../inventory/model/brand";
import {User} from "../../../../admin/model/user";
import {StockService} from "../../../inventory/service/stock.service";
import {WarehouseService} from "../../../inventory/service/warehouse.service";
import {ItemCategoryService} from "../../../inventory/service/item-category.service";
import {BrandService} from "../../../inventory/service/brand.service";
import {ItemService} from "../../../inventory/service/item.service";
import {StockAggr} from "../../model/stockAggr";
import {Supplier} from "../../../trade/model/supplier";
import {SupplierService} from "../../../trade/service/supplier.service";
import {ToastrService} from "ngx-toastr";

@Component({
  selector: 'app-reorder-report',
  templateUrl: './reorder-report.component.html',
  styleUrls: ['./reorder-report.component.css']
})
export class ReorderReportComponent implements OnInit {

  subStocks: Array<Stock> = [];
  warehouses: Array<Warehouse> = [];

  selectedRow: number;

  // Item search properties
  keyItemSearch: string;
  keyItemName: string;
  itemSearched: Array<Item> = [];
  itemsSearched: Array<Item> = [];

  selectedStock = new Stock();
  barcode: string;
  selectedWarehouse: Warehouse;

  // Category filter properties
  keyItemCategory: string;
  itemCategories: Array<ItemCategory> = [];
  catCode = "";

  // Brand filter properties
  keyBrand: string;
  brands: Array<Brand> = [];
  brandCode = "";

  // Supplier filter properties
  keySupplier: string;
  suppliers: Array<Supplier> = [];
  supplierCode = "";

  threshold: number;
  loading: boolean;

  constructor(private stockService: StockService,
              private warehouseService: WarehouseService,
              private itemCategoryService: ItemCategoryService,
              private brandService: BrandService,
              private itemService: ItemService,
              private supplierService: SupplierService,
              private toastr: ToastrService) {
  }

  ngOnInit() {
    this.loadWarehouses();
    this.selectedWarehouse = new Warehouse();
    this.findReorderList();
  }

  loadWarehouses() {
    this.warehouseService.findAllActive().subscribe((result: Array<Warehouse>) => {
      if (result.length === 1) {
        this.selectedWarehouse = result[0];
      }
      return this.warehouses = result;
    })
  }

  loadItemCategories() {
    return this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.itemCategories = data;
    });
  }

  setSelectedItemCategory(event) {
    this.catCode = event.item.code;
    this.findReorderList();
  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  setSelectedBrand(event) {
    this.findReorderList();
  }

  /**
   * Load items by name for typeahead
   */
  loadItemsByName() {
    if (this.keyItemName && this.keyItemName.length >= 2) {
      return this.itemService.findAllActiveByNameLike(this.keyItemName).subscribe((data: Array<Item>) => {
        return this.itemsSearched = data;
      });
    }
  }

  /**
   * Load items by search term
   */
  loadItems() {
    return this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  /**
   * Load items by barcode
   */
  loadItemByCode() {
    return this.itemService.findAllByBarcodeLike(this.barcode).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  /**
   * Search for items by barcode
   */
  searchByBarcode() {
    if (this.barcode && this.barcode.trim() !== '') {
      this.loading = true;
      this.stockService.findByBarcodeLike(this.barcode).subscribe(
        (result: Array<Stock>) => {
          if (result && result.length > 0) {
            this.subStocks = result;
            this.loading = false;
          } else {
            this.toastr.info('No items found with this barcode', 'Information');
            this.loading = false;
          }
        },
        error => {
          this.toastr.error('Error searching by barcode', 'Error');
          this.loading = false;
        }
      );
    }
  }

  /**
   * Load suppliers for typeahead
   */
  loadSuppliers() {
    if (this.keySupplier && this.keySupplier.length >= 2) {
      return this.supplierService.findByNameLike(this.keySupplier).subscribe((data: Array<Supplier>) => {
        return this.suppliers = data;
      });
    }
  }

  /**
   * Set selected supplier and filter items
   */
  setSelectedSupplier(event) {
    if (event && event.item) {
      this.supplierCode = event.item.supplierNo || event.item.id;
      this.findItemsBySupplier();
    }
  }

  /**
   * Find items by supplier with threshold
   */
  findItemsBySupplier() {
    if (this.supplierCode) {
      this.loading = true;
      this.stockService.findBySupplier(this.supplierCode, this.threshold).subscribe(
        (result: Array<Stock>) => {
          if (result && result.length > 0) {
            this.subStocks = result;
            this.loading = false;
          } else {
            this.toastr.info('No items found for this supplier with the current threshold', 'Information');
            this.loading = false;
          }
        },
        error => {
          this.toastr.error('Error searching by supplier', 'Error');
          this.loading = false;
        }
      );
    }
  }

  findReorderListByWhCode() {
    this.subStocks = [];
    this.stockService.findReorderListByWarehouse(this.selectedWarehouse.code, this.threshold).subscribe((result: Array<Stock>) => {
      this.subStocks = result;
      this.keyItemSearch = "";
    });
  }

  findReorderList() {
    if (this.threshold > 0) {
      this.loading = true;
      this.subStocks = [];
      this.stockService.findReorderList(this.threshold, this.catCode, this.brandCode).subscribe((result: Array<Stock>) => {
        this.subStocks = result;
        this.keyItemSearch = "";
        this.loading = false;
      });
    }
  }

  setSelectedItem(event) {
    this.selectedStock = event.item;
    this.searchSubStock();
  }

  searchSubStock() {
    this.subStocks = [];
    this.stockService.findByBarcodeAndWarehouse(this.selectedStock.barcode, this.selectedWarehouse.code)
      .subscribe((data: Array<Stock>) => {
        this.subStocks = data;
        this.keyItemSearch = "";
      });
  }

  /**
   * Get current date for print footer
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString();
  }

}
