import {Component, OnInit} from '@angular/core';
import {MetaData} from "../../../../core/model/metaData";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {ExpenseService} from "../../../trade/service/expense.service";
import {ExpenseTypeService} from "../../../trade/service/expense-type.service";
import {EmployeeService} from "../../../hr/service/employee.service";
import {Expense} from "../../../trade/model/expense";
import {DatePipe} from "@angular/common";
import {ExpenseType} from "../../../trade/model/expense-type";
import {Employee} from "../../../hr/model/employee";
import {ExpenseReportService} from "../../service/expense-report.service";

@Component({
  selector: 'app-expense-report',
  templateUrl: './expense-report.component.html',
  styleUrls: ['./expense-report.component.css']
})
export class ExpenseReportComponent implements OnInit {

  sDate: Date;
  eDate: Date;
  durationFilter: Array<MetaData>;
  selectedDuration: MetaData;

  expenseCatList: Array<MetaData>;
  keyExpenseCat: string;
  selectedExpType: ExpenseType;

  expenseTypeList: Array<ExpenseType>;
  keyExpenseType: string;
  selectedExpCat: MetaData;

  expenses: Array<Expense>;
  selectedExp: Expense;

  keyEmpSearch: string;
  empSearchList: Array<Employee>;
  selectedEmp: Employee;

  selectedRow: number;
  totalAmount: number;
  modalRef: BsModalRef;

  page;
  pageSize;
  collectionSize;
  maxSize;

  constructor(private expenseService: ExpenseService, private expenseReportService: ExpenseReportService,
              private metaDataService: MetaDataService, private modalService: BsModalService,
              private expenseTypeService: ExpenseTypeService, private employeeService: EmployeeService, private datePipe: DatePipe) {
  }

  ngOnInit(): void {
    this.selectedDuration = new MetaData();
    this.sDate = new Date();
    this.eDate = new Date();
    this.page = 0;
    this.pageSize = 20;
    this.maxSize = 10;
    this.keyEmpSearch = "";
    this.keyExpenseType = "";
    this.selectedEmp = new Employee();
    this.selectedExpType = new ExpenseType();
    this.selectedExpCat = new MetaData();
    this.findDurationFilterData();
    this.findTodayExpenses();
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findTodayExpenses();
  }

  findExpenses(fromDate: Date, toDate: Date) {
    if (this.keyEmpSearch.length > 0 && this.keyExpenseType.length == 0) {
      this.expenseReportService.findByEmployeeAndBetweenDates(this.selectedEmp.id, this.datePipe.transform(fromDate, 'yyyy-MM-dd'),
        this.datePipe.transform(toDate, 'yyyy-MM-dd')).subscribe(
        (result: any) => {
          this.expenses = result;
          this.calculateTotalAmount();
        })
    }
    if (this.keyExpenseType.length > 0 && this.keyEmpSearch.length == 0) {
      this.expenseReportService.findByTypeAndBetweenDates(this.selectedExpType.id, this.datePipe.transform(fromDate, 'yyyy-MM-dd'),
        this.datePipe.transform(toDate, 'yyyy-MM-dd')).subscribe(
        (result: any) => {
          this.expenses = result;
          this.calculateTotalAmount();
        })
    }
    if (this.keyExpenseType.length > 0 && this.keyEmpSearch.length > 0) {
      this.expenseReportService.findByTypeAndEmployeeAndBetweenDates(this.selectedExpType.id, this.selectedEmp.id,
        this.datePipe.transform(fromDate, 'yyyy-MM-dd'),
        this.datePipe.transform(toDate, 'yyyy-MM-dd')).subscribe(
        (result: any) => {
          this.expenses = result;
          this.calculateTotalAmount();
        })
    }
    if (this.keyExpenseType.length == 0 && this.keyEmpSearch.length == 0) {
      this.expenseReportService.findByDateBetween(this.datePipe.transform(fromDate, 'yyyy-MM-dd'),
        this.datePipe.transform(toDate, 'yyyy-MM-dd'), this.page, this.pageSize).subscribe(
        (result: any) => {
          this.expenses = result.content;
          this.calculateTotalAmount();
        })
    }
  }

  findTodayExpenses() {
    this.findExpenses(new Date(), new Date());
  }

  findDurationFilterData() {
    this.metaDataService.findByCategory('ReportFilterDuration').subscribe((result: Array<MetaData>) => {
      this.durationFilter = result;
    });
  }

  searchEmployee() {
    this.employeeService.findByEmployeeNameLike(this.keyEmpSearch).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  loadExpenseCat() {
    this.metaDataService.findByCategory("ExpenseCategory").subscribe((data: Array<MetaData>) => {
      this.expenseCatList = data;
    });
  }

  loadExpenseTypes() {
    this.expenseTypeService.findByName(this.keyExpenseType).subscribe((data: Array<ExpenseType>) => {
      return this.expenseTypeList = data;
    });
  }

  filterByExpenseCat(event) {
    this.selectedExpCat.id = event.item.id;
    this.expenseReportService.findByTypeCategory(this.selectedExpCat.id, this.page, this.pageSize).subscribe((result: any) => {
      return this.expenses = result.content;
    });
  }

  filterByExpenseType(event) {
    this.selectedExpType.id = event.item.id;
    this.expenseReportService.findByType(this.selectedExpType.id, this.page, this.pageSize).subscribe((result: any) => {
      return this.expenses = result.content;
    });
  }

  filterByEmployee(event) {
    this.selectedEmp.id = event.item.id;
    this.expenseReportService.findByEmployee(this.selectedEmp.id, this.page, this.pageSize).subscribe((data: any) => {
      return this.expenses = data.content;
    });
  }

  filterByDuration() {
    this.expenseReportService.findByRangeFilter(this.selectedDuration.id, this.page, this.pageSize).subscribe(
      (result: any) => {
        this.expenses = result.content;
        this.calculateTotalAmount();
      })
  }

  selectExp(tr, index) {
    this.selectedExp = tr;
    this.selectedRow = index;
  }

  viewDetail() {
    this.modalRef = this.modalService.show(Expense, <ModalOptions>{class: 'modal-xl'});
  }

  calculateTotalAmount() {
    this.totalAmount = 0;
    for (let tr of this.expenses) {
      if (null != tr.amount)
        this.totalAmount = this.totalAmount + tr.amount
    }
  }

  /**
   * Get current date for print footer
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString();
  }

}
