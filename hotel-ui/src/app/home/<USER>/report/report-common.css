/* Common styles for report components */

/* Fixed height table container with vertical scrolling only */
.report-table-container {
  height: 60vh; /* Fixed height of 60% of viewport height */
  overflow-y: auto; /* Vertical scroll only */
  overflow-x: hidden; /* No horizontal scroll */
  margin-bottom: 1rem;
  border: 1px solid #dee2e6;
}

/* Make table headers sticky when scrolling */
.report-table-container thead th {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  z-index: 1;
  border-bottom: 2px solid #dee2e6;
}

/* Ensure table takes full width of container but doesn't overflow */
.report-table-container table {
  width: 100%;
  table-layout: fixed;
}

/* Bottom action buttons container */
.report-actions {
  margin-top: 1rem;
  margin-bottom: 2rem;
  text-align: right;
}

/* Summary information styles */
.report-summary {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.report-summary .summary-label {
  font-weight: bold;
  margin-bottom: 0;
}

.report-summary .summary-value {
  font-weight: bold;
  margin-left: 0.5rem;
  margin-bottom: 0;
}

.report-summary .summary-value.success {
  color: #28a745;
}

/* Mobile adjustments */
@media (max-width: 767.98px) {
  .report-actions {
    text-align: center;
  }

  .report-table-container {
    height: 50vh; /* Smaller height on mobile */
  }
}
