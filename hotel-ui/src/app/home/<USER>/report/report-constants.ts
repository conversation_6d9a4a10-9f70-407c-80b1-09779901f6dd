import {environment} from '../../../../environments/environment';

export class ReportApiConstants {

  public static API_URL = environment.apiUrl;

  public static GET_REORDER_LIST = ReportApiConstants.API_URL + 'item/getReorderList';

  public static FIND_ALL_COMMISSION = ReportApiConstants.API_URL + 'commission/findAll';
  public static FIND_COMMISSION_JOB_NO = ReportApiConstants.API_URL + 'commission/findByJobNo';
  public static FIND_COMMISSION_EMP_ID_DATE_BETWEEN = ReportApiConstants.API_URL + 'commission/findByEmpIdAndDateBetween';

  public static GET_EXPENSE = ReportApiConstants.API_URL + 'expense/findAllPage';
  public static SEARCH_EXPENSE_BY_TYPE = ReportApiConstants.API_URL + 'expense/findByExpenseType';
  public static SEARCH_EXPENSE_BY_EMPLOYEE = ReportApiConstants.API_URL + 'expense/findByEmployee';
  public static SEARCH_EXPENSE_BY_DATE_RANGE = ReportApiConstants.API_URL + 'expense/findByDateRangeFilter';
  public static SEARCH_EXPENSE_BY_TYPE_CATEGORY = ReportApiConstants.API_URL + 'expense/findByTypeCategory';
  public static SEARCH_EXPENSE_BETWEEN_DATES = ReportApiConstants.API_URL + 'expense/findBetweenDates';
  public static SEARCH_EXPENSE_BY_TYPE_AND_EMPLOYEE = ReportApiConstants.API_URL + 'expense/findByTypeAndEmployee';
  public static SEARCH_EXPENSE_BY_TYPE_AND_EMPLOYEE_AND_BETWEEN_DAYS = ReportApiConstants.API_URL + 'expense/findByTypeAndEmployeeAndBetweenDates';
  public static SEARCH_EXPENSE_BY_EMPLOYEE_AND_BETWEEN_DAYS = ReportApiConstants.API_URL + 'expense/findByEmployeeAndBetweenDates';
  public static SEARCH_EXPENSE_BY_TYPE_AND_BETWEEN_DAYS = ReportApiConstants.API_URL + 'expense/findByTypeAndBetweenDates';

  public static FIND_STOCK_MOVEMENT = ReportApiConstants.API_URL + 'stock/findStockMovement';

  // Sales Invoice Report API endpoints
  public static SALES_INVOICE_REPORT_FIND_ALL = ReportApiConstants.API_URL + 'salesInvoiceReport/findAll';
  public static SALES_INVOICE_REPORT_FIND_BY_DATE_RANGE = ReportApiConstants.API_URL + 'salesInvoiceReport/findByDateRange';
  public static SALES_INVOICE_REPORT_FIND_BY_CUSTOMER = ReportApiConstants.API_URL + 'salesInvoiceReport/findByCustomer';
  public static SALES_INVOICE_REPORT_FIND_BY_STATUS = ReportApiConstants.API_URL + 'salesInvoiceReport/findByStatus';
  public static SALES_INVOICE_REPORT_FIND_BY_USER = ReportApiConstants.API_URL + 'salesInvoiceReport/findByUser';
  public static SALES_INVOICE_REPORT_FIND_BY_USER_AND_DATE_RANGE = ReportApiConstants.API_URL + 'salesInvoiceReport/findByUserAndDateRange';
  public static SALES_INVOICE_REPORT_FIND_BY_USER_AND_ROUTE = ReportApiConstants.API_URL + 'salesInvoiceReport/findByUserAndRoute';
  public static SALES_INVOICE_REPORT_EXPORT_TO_EXCEL = ReportApiConstants.API_URL + 'salesInvoiceReport/exportToExcel';
  public static SALES_INVOICE_REPORT_EXPORT_TO_PDF = ReportApiConstants.API_URL + 'salesInvoiceReport/exportToPdf';

  // Action Report API endpoints
  public static ACTION_REPORT_FIND_ALL = ReportApiConstants.API_URL + 'actionReport/findAll';
  public static ACTION_REPORT_GET_ACTION_TYPES = ReportApiConstants.API_URL + 'actionReport/getActionTypes';
  public static ACTION_REPORT_FIND_WITH_FILTERS = ReportApiConstants.API_URL + 'actionReport/findWithFilters';

  // Item Report API endpoints
  public static ITEM_REPORT_FIND_ALL = ReportApiConstants.API_URL + 'itemReport/findAll';
  public static ITEM_REPORT_FIND_FILTERED = ReportApiConstants.API_URL + 'itemReport/findFiltered';
  public static ITEM_REPORT_EXPORT_TO_PDF = ReportApiConstants.API_URL + 'itemReport/exportToPdf';
}
