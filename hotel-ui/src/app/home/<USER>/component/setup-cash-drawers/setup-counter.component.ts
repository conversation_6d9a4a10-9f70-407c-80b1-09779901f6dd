import { Component, OnInit } from '@angular/core';
import {Counter} from "../../model/counter";
import {User} from "../../model/user";
import {CounterService} from "../../service/counter.service";
import {UserService} from "../../service/user.service";
import {NotificationService} from "../../../core/service/notification.service";

@Component({
  selector: 'app-setup-counter',
  templateUrl: './setup-counter.component.html',
  styleUrls: ['./setup-counter.component.css']
})
export class SetupCounterComponent implements OnInit {

  counterId: string;
  counters: Counter[];
  user: User;
  isCounterSelected: boolean;
  currentUser;
  userId: string;
  users: User[] = [];

  constructor(private counterService: CounterService,
              private userService: UserService,
              private notificationService: NotificationService) { }

  ngOnInit(): void {
    this.user = new User();
    this.currentUser = JSON.parse(localStorage.getItem('currentUser'));
    this.user = this.currentUser.user;
    this.findAllCounters();
    this.findAllUsers();
    this.isCounterSelected = false;
  }

  setSelectedCounter(event) {
    this.isCounterSelected = true;
    this.user.counter = event.item;
  }

  findAllCounters(){
    this.counterService.findAll().subscribe((data: Counter[])=>{
      this.counters = data;
      console.log(data);
    });
  }

  setSelectedUser($event: Event) {
    console.log(this.userId);
  }

  findAllUsers(){
    this.userService.findAll().subscribe((data: User[])=>{
      this.users = data;
    })
  }

  setupCounter(){
    this.userService.setupCounter(this.counterId, this.userId).subscribe((val)=>{
      if(val){
        this.notificationService.showSuccess("User Updated Successfully");
      }else{
        this.notificationService.showError("User Updated Failed");
      }
    })
  }
}
