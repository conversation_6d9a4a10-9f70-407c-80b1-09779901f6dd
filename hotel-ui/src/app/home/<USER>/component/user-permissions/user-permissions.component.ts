import {Component, OnInit} from '@angular/core';
import {User} from '../../../admin/model/user';
import {PermissionService} from '../../../admin/service/permission.service';
import {Permission} from "../../../admin/model/permission";

@Component({
  selector: 'app-user-permissions',
  templateUrl: './user-permissions.component.html',
  styleUrls: ['./user-permissions.component.css']
})
export class UserPermissionsComponent implements OnInit {

  user: User;
  permissionList: Array<Permission> = [];

  constructor(private permissionService: PermissionService) {
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.findUserPermissions();
  }

  ngOnInit() {
  }

  findUserPermissions() {
    this.permissionService.findEnabledPermissions(this.user.username).subscribe((result: Array<Permission>) => {
      this.permissionList = result;
    })
  }

}
