<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">
      {{ 'USER_PERMISSIONS.TITLE' | translate }}
      <span *ngIf="userName" class="text-primary ml-2">- {{ userName }}</span>
    </h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="row">
    <div class="col-md-4 form-group">
      <label for="enableModules">{{ 'USER_PERMISSIONS.ENABLED_MODULES' | translate }}</label>
      <select class="form-control" id="enableModules" name="module" [(ngModel)]="module"
              (change)="getPermsForModule(module)">
        <option></option>
        <option *ngFor="let module of modules" [ngValue]="module">{{ module.name }}</option>
      </select>
    </div>
    <div class="col-md-4 form-group">
      <label for="addedPerms">{{ 'USER_PERMISSIONS.ENABLED_PERMISSIONS' | translate }}</label>
      <select class="form-control" id="addedPerms" [(ngModel)]="selectedPerm">
        <option></option>
        <option *ngFor="let perm of availablePerms" [ngValue]="perm">{{ perm.name }}</option>
      </select>
    </div>
    <div class="col-md-4 form-group">
      <label>&nbsp;</label>
      <button class="btn btn-theme float-right form-control"
              (click)="addToDesktop(selectedPerm)"> {{ 'USER_PERMISSIONS.ADD' | translate }}
      </button>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <h5>{{ 'USER_PERMISSIONS.CURRENT_DESKTOP_PERMISSIONS' | translate }}</h5>
      <tag-input [(ngModel)]='desktopPerms' [identifyBy]="'id'" [displayBy]="'name'" theme='dark'
                 [editable]='false' [allowDupes]='false'></tag-input>
    </div>
  </div>
  <div class="row mt-3">
    <div class="col-md-12 text-right">
      <button class="btn btn-secondary mr-2" (click)="closeModal()">{{ 'USER_PERMISSIONS.CANCEL' | translate }}</button>
      <button class="btn btn-theme" (click)="savePermissions()">{{ 'USER_PERMISSIONS.SAVE' | translate }}</button>
    </div>
  </div>
</div>

