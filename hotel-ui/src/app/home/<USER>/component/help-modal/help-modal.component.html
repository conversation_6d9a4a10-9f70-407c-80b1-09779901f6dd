<div class="modal-header">
  <h4 class="modal-title">
    <i class="fas fa-question-circle mr-2"></i>
    {{ 'Help & Support' | translate }}
  </h4>
  <button type="button" class="close" (click)="closeModal()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <!-- Navigation Tabs -->
  <ul class="nav nav-tabs mb-4">
    <li class="nav-item">
      <a class="nav-link" 
         [class.active]="activeTab === 'getting-started'"
         (click)="setActiveTab('getting-started')">
        <i class="fas fa-play-circle mr-1"></i>
        {{ 'Getting Started' | translate }}
      </a>
    </li>
    <li class="nav-item">
      <a class="nav-link" 
         [class.active]="activeTab === 'faq'"
         (click)="setActiveTab('faq')">
        <i class="fas fa-question mr-1"></i>
        {{ 'FAQ' | translate }}
      </a>
    </li>
    <li class="nav-item">
      <a class="nav-link" 
         [class.active]="activeTab === 'tutorials'"
         (click)="setActiveTab('tutorials')">
        <i class="fas fa-video mr-1"></i>
        {{ 'Video Tutorials' | translate }}
      </a>
    </li>
    <li class="nav-item">
      <a class="nav-link" 
         [class.active]="activeTab === 'contact'"
         (click)="setActiveTab('contact')">
        <i class="fas fa-envelope mr-1"></i>
        {{ 'Contact Us' | translate }}
      </a>
    </li>
  </ul>

  <!-- Getting Started Tab -->
  <div *ngIf="activeTab === 'getting-started'" class="tab-content">
    <div class="getting-started-content">
      <div class="welcome-section mb-4">
        <h5><i class="fas fa-rocket mr-2 text-primary"></i>{{ 'Welcome to the System' | translate }}</h5>
        <p class="text-muted">
          <span *ngIf="getCurrentLang() === 'en'">
            This comprehensive business management system helps you manage inventory, sales, purchases, and generate detailed reports.
          </span>
          <span *ngIf="getCurrentLang() === 'sn'">
            මෙම සම්පූර්ණ ව්‍යාපාර කළමනාකරණ පද්ධතිය ඔබට ඉන්වෙන්ටරි, විකුණුම්, මිලදී ගැනීම් කළමනාකරණය කිරීමට සහ විස්තරාත්මක වාර්තා ජනනය කිරීමට උපකාරී වේ.
          </span>
        </p>
      </div>

      <div class="quick-start-guide">
        <h6><i class="fas fa-list-ol mr-2"></i>{{ 'Quick Start Guide' | translate }}</h6>
        <div class="step-list">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <h6>{{ 'Set up your business information' | translate }}</h6>
              <p class="text-muted">
                <span *ngIf="getCurrentLang() === 'en'">Go to Admin > Business Info to configure your company details.</span>
                <span *ngIf="getCurrentLang() === 'sn'">ඔබේ සමාගම් විස්තර සකසන්න සඳහා පරිපාලන > ව්‍යාපාරික තොරතුරු වෙත යන්න.</span>
              </p>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <h6>{{ 'Add your inventory items' | translate }}</h6>
              <p class="text-muted">
                <span *ngIf="getCurrentLang() === 'en'">Start by adding your products in Inventory > Create Item.</span>
                <span *ngIf="getCurrentLang() === 'sn'">ඉන්වෙන්ටරි > භාණ්ඩ නිර්මාණය හි ඔබේ නිෂ්පාදන එකතු කිරීමෙන් ආරම්භ කරන්න.</span>
              </p>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <h6>{{ 'Create your first sale' | translate }}</h6>
              <p class="text-muted">
                <span *ngIf="getCurrentLang() === 'en'">Process your first transaction in Trade > Create Sales Invoice.</span>
                <span *ngIf="getCurrentLang() === 'sn'">වෙළඳාම > විකුණුම් ඉන්වොයිස නිර්මාණය හි ඔබේ පළමු ගනුදෙනුව සකසන්න.</span>
              </p>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">4</div>
            <div class="step-content">
              <h6>{{ 'Generate reports' | translate }}</h6>
              <p class="text-muted">
                <span *ngIf="getCurrentLang() === 'en'">Monitor your business performance with detailed reports.</span>
                <span *ngIf="getCurrentLang() === 'sn'">විස්තරාත්මක වාර්තා සමඟ ඔබේ ව්‍යාපාරික කාර්ය සාධනය නිරීක්ෂණය කරන්න.</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="feature-highlights mt-4">
        <h6><i class="fas fa-star mr-2"></i>{{ 'Key Features' | translate }}</h6>
        <div class="row">
          <div class="col-md-6">
            <div class="feature-item">
              <i class="fas fa-boxes text-primary"></i>
              <span>{{ 'Inventory Management' | translate }}</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-chart-line text-success"></i>
              <span>{{ 'Sales Tracking' | translate }}</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-file-invoice text-info"></i>
              <span>{{ 'Invoice Generation' | translate }}</span>
            </div>
          </div>
          <div class="col-md-6">
            <div class="feature-item">
              <i class="fas fa-chart-bar text-warning"></i>
              <span>{{ 'Detailed Reports' | translate }}</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-users text-secondary"></i>
              <span>{{ 'User Management' | translate }}</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-mobile-alt text-primary"></i>
              <span>{{ 'Mobile Responsive' | translate }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- FAQ Tab -->
  <div *ngIf="activeTab === 'faq'" class="tab-content">
    <div class="faq-content">
      <!-- Search Box -->
      <div class="search-box mb-4">
        <div class="input-group">
          <input type="text" 
                 class="form-control" 
                 [(ngModel)]="searchQuery"
                 (input)="searchFaqs()"
                 [placeholder]="'Search FAQs...' | translate">
          <div class="input-group-append">
            <span class="input-group-text">
              <i class="fas fa-search"></i>
            </span>
          </div>
        </div>
      </div>

      <!-- FAQ List -->
      <div class="faq-list">
        <div *ngFor="let faq of filteredFaqs" class="faq-item">
          <div class="faq-question" 
               data-toggle="collapse" 
               [attr.data-target]="'#faq-' + faq.id"
               [attr.aria-expanded]="false">
            <i class="fas fa-chevron-right mr-2"></i>
            <span *ngIf="getCurrentLang() === 'en'">{{ faq.question }}</span>
            <span *ngIf="getCurrentLang() === 'sn'">{{ faq.questionSn }}</span>
          </div>
          <div class="collapse faq-answer" [id]="'faq-' + faq.id">
            <div class="card card-body">
              <span *ngIf="getCurrentLang() === 'en'">{{ faq.answer }}</span>
              <span *ngIf="getCurrentLang() === 'sn'">{{ faq.answerSn }}</span>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="filteredFaqs.length === 0" class="no-results">
        <div class="text-center text-muted">
          <i class="fas fa-search fa-3x mb-3"></i>
          <h6>{{ 'No FAQs found' | translate }}</h6>
          <p>{{ 'Try different search terms' | translate }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Video Tutorials Tab -->
  <div *ngIf="activeTab === 'tutorials'" class="tab-content">
    <div class="tutorials-content">
      <div class="row">
        <div *ngFor="let tutorial of videoTutorials" class="col-md-6 mb-4">
          <div class="tutorial-card" (click)="openVideoTutorial(tutorial.videoUrl)">
            <div class="tutorial-thumbnail">
              <img [src]="tutorial.thumbnail" [alt]="tutorial.title" class="img-fluid">
              <div class="play-overlay">
                <i class="fas fa-play-circle"></i>
              </div>
              <div class="duration-badge">{{ tutorial.duration }}</div>
            </div>
            <div class="tutorial-info">
              <h6 class="tutorial-title">
                <span *ngIf="getCurrentLang() === 'en'">{{ tutorial.title }}</span>
                <span *ngIf="getCurrentLang() === 'sn'">{{ tutorial.titleSn }}</span>
              </h6>
              <p class="tutorial-description text-muted">
                <span *ngIf="getCurrentLang() === 'en'">{{ tutorial.description }}</span>
                <span *ngIf="getCurrentLang() === 'sn'">{{ tutorial.descriptionSn }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Contact Tab -->
  <div *ngIf="activeTab === 'contact'" class="tab-content">
    <div class="contact-content">
      <div class="row">
        <div class="col-md-6">
          <div class="contact-info">
            <h6><i class="fas fa-headset mr-2"></i>{{ 'Technical Support' | translate }}</h6>
            <div class="contact-item">
              <i class="fas fa-envelope text-primary"></i>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <i class="fas fa-phone text-success"></i>
              <span>+94 11 234 5678</span>
            </div>
            <div class="contact-item">
              <i class="fas fa-clock text-info"></i>
              <span>
                <span *ngIf="getCurrentLang() === 'en'">Mon-Fri: 9:00 AM - 6:00 PM</span>
                <span *ngIf="getCurrentLang() === 'sn'">සඳුදා-සිකුරාදා: පෙ.ව. 9:00 - ප.ව. 6:00</span>
              </span>
            </div>
          </div>

          <div class="contact-info mt-4">
            <h6><i class="fas fa-building mr-2"></i>{{ 'Office Address' | translate }}</h6>
            <div class="contact-item">
              <i class="fas fa-map-marker-alt text-danger"></i>
              <span>
                <span *ngIf="getCurrentLang() === 'en'">123 Business Street, Colombo 03, Sri Lanka</span>
                <span *ngIf="getCurrentLang() === 'sn'">123 ව්‍යාපාරික වීදිය, කොළඹ 03, ශ්‍රී ලංකාව</span>
              </span>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="contact-form">
            <h6><i class="fas fa-paper-plane mr-2"></i>{{ 'Send us a message' | translate }}</h6>
            <form>
              <div class="form-group">
                <label>{{ 'Name' | translate }}</label>
                <input type="text" class="form-control" [placeholder]="'Your name' | translate">
              </div>
              <div class="form-group">
                <label>{{ 'Email' | translate }}</label>
                <input type="email" class="form-control" [placeholder]="'Your email' | translate">
              </div>
              <div class="form-group">
                <label>{{ 'Subject' | translate }}</label>
                <input type="text" class="form-control" [placeholder]="'Message subject' | translate">
              </div>
              <div class="form-group">
                <label>{{ 'Message' | translate }}</label>
                <textarea class="form-control" rows="4" [placeholder]="'Your message' | translate"></textarea>
              </div>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane mr-1"></i>
                {{ 'Send Message' | translate }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="closeModal()">
    {{ 'Close' | translate }}
  </button>
</div>
