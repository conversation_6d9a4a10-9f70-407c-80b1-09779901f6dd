/* Help Modal Styles */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
}

.modal-header .modal-title {
  font-weight: 600;
}

.modal-header .close {
  color: white;
  opacity: 0.8;
}

.modal-header .close:hover {
  opacity: 1;
}

.modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Navigation Tabs */
.nav-tabs {
  border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
  border-color: transparent;
  color: #667eea;
}

.nav-tabs .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  border-radius: 0.25rem 0.25rem 0 0;
}

/* Getting Started Styles */
.welcome-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border-left: 4px solid #667eea;
}

.step-list {
  margin-top: 1rem;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-content h6 {
  color: #495057;
  margin-bottom: 0.5rem;
}

.feature-highlights {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.feature-item i {
  width: 20px;
  margin-right: 0.75rem;
}

/* FAQ Styles */
.search-box .input-group-text {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.faq-item {
  margin-bottom: 0.5rem;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  overflow: hidden;
}

.faq-question {
  padding: 1rem;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.faq-question:hover {
  background: #e9ecef;
}

.faq-question i {
  transition: transform 0.2s ease;
}

.faq-question[aria-expanded="true"] i {
  transform: rotate(90deg);
}

.faq-answer .card {
  border: none;
  background: #fff;
}

.no-results {
  padding: 3rem;
}

/* Video Tutorials Styles */
.tutorial-card {
  background: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.tutorial-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.tutorial-thumbnail {
  position: relative;
  overflow: hidden;
}

.tutorial-thumbnail img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.tutorial-card:hover .tutorial-thumbnail img {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.tutorial-card:hover .play-overlay {
  background: rgba(102, 126, 234, 0.9);
  transform: translate(-50%, -50%) scale(1.1);
}

.duration-badge {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.tutorial-info {
  padding: 1rem;
}

.tutorial-title {
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.tutorial-description {
  font-size: 0.85rem;
  margin-bottom: 0;
  line-height: 1.4;
}

/* Contact Styles */
.contact-info {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.contact-item i {
  width: 20px;
  margin-right: 0.75rem;
}

.contact-form {
  background: #fff;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
}

.contact-form .form-group label {
  font-weight: 500;
  color: #495057;
}

.contact-form .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.contact-form .btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border-color: #5a6fd8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-body {
    max-height: 60vh;
  }
  
  .step-item {
    flex-direction: column;
    text-align: center;
  }
  
  .step-number {
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .tutorial-card {
    margin-bottom: 1rem;
  }
  
  .contact-content .row {
    flex-direction: column;
  }
  
  .contact-info {
    margin-bottom: 1.5rem;
  }
}
