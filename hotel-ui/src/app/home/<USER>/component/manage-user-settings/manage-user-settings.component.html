<div class="container-fluid">
  <div *ngIf="!isModal" class="mb-4">
    <h2 class="component-title">Manage User Settings for {{ username }}</h2>
  </div>

  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">Personal Preferences</h5>
    </div>
    <div class="card-body">
      <!-- Invoice Creation Mode -->
      <div class="form-group mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <label for="invoiceCreationMode">Invoice Creation Mode</label>
          <div class="custom-control custom-switch">
            <input type="checkbox" class="custom-control-input" id="invoiceCreationModeSwitch"
                   [checked]="isSettingEnabled('invoiceCreationMode')"
                   (change)="setSettingEnabled('invoiceCreationMode', $event.target.checked)">
            <label class="custom-control-label" for="invoiceCreationModeSwitch">
              {{ isSettingEnabled('invoiceCreationMode') ? 'Enabled' : 'Disabled' }}
            </label>
          </div>
        </div>
        <select
          id="invoiceCreationMode"
          class="form-control"
          [disabled]="!isSettingEnabled('invoiceCreationMode')"
          [value]="getSettingValue('invoiceCreationMode')"
          (change)="updateSetting('invoiceCreationMode', $event.target.value)">
          <option *ngFor="let option of invoiceCreationModeOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        <small class="form-text text-muted">
          Choose the invoice creation mode for this user. Standard is the default desktop view, Sales Rep is optimized for mobile devices, and Distributor Invoice is for distributor-specific operations.
        </small>
      </div>

      <!-- Minimum Markup Percentage for CASHIER role -->
      <div class="form-group mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <label for="minimumMarkupPercentage">Minimum Markup Percentage</label>
          <div class="custom-control custom-switch">
            <input type="checkbox" class="custom-control-input" id="minimumMarkupPercentageSwitch"
              [checked]="isSettingEnabled('minimumMarkupPercentage')"
              (change)="setSettingEnabled('minimumMarkupPercentage', $event.target.checked)">
            <label class="custom-control-label" for="minimumMarkupPercentageSwitch">
              {{ isSettingEnabled('minimumMarkupPercentage') ? 'Enabled' : 'Disabled' }}
            </label>
          </div>
        </div>
        <div class="input-group">
          <input
            type="number"
            id="minimumMarkupPercentage"
            name="minimumMarkupPercentage"
            class="form-control"
            [value]="getSettingValue('minimumMarkupPercentage')"
            (change)="updateSetting('minimumMarkupPercentage', $event.target.value)"
            [disabled]="!isSettingEnabled('minimumMarkupPercentage')"
            min="0"
            max="100"
            step="0.1">
          <div class="input-group-append">
            <span class="input-group-text">%</span>
          </div>
        </div>
        <small class="form-text text-muted">
          Minimum percentage markup above cost for users with CASHIER role. Default is 4%.
        </small>
      </div>
    </div>
  </div>



  <!-- Loading indicator -->
  <div *ngIf="loading" class="text-center mt-3">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <!-- Modal footer with close button -->
  <div *ngIf="isModal" class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modalRef.hide()">Close</button>
  </div>
</div>
