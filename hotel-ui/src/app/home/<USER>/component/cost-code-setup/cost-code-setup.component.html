<div class="container-fluid px-0">
  <h2 class="component-title">Cost Code Letter Mapping</h2>
  <div class="alert alert-info">
    <i class="fas fa-info-circle"></i> Configure how numbers are displayed as letters in cost codes when barcode printing is enabled.
  </div>

  <div class="row">
    <div class="col-md-8">
      <!-- Letter Mapping Configuration -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-font mr-2"></i>
            Number to Letter Mapping
          </h5>
        </div>
        <div class="card-body">
          <form #costCodeForm="ngForm">
            <!-- Letter Mapping -->
            <div class="form-group mb-3">
              <label>Map Numbers to Letters</label>
              <small class="form-text text-muted mb-3">
                Configure which letter represents each number (0-9) in cost codes
              </small>
              <div class="row">
                <div class="col-md-6" *ngFor="let number of getNumbersForMapping()">
                  <div class="input-group mb-2">
                    <div class="input-group-prepend">
                      <span class="input-group-text">{{ number }} =</span>
                    </div>
                    <input type="text"
                           class="form-control"
                           [(ngModel)]="costCodeSettings.letterMapping[number]"
                           maxlength="1"
                           pattern="[A-Za-z]"
                           style="text-transform: uppercase;"
                           placeholder="Letter">
                  </div>
                </div>
              </div>
            </div>

            <!-- Preview -->
            <div class="form-group mb-3">
              <label>Preview</label>
              <div class="alert alert-light">
                <div><strong>{{ getPreviewExample() }}</strong></div>
                <small class="text-muted">This shows how numbers will be converted to letters in cost codes</small>
              </div>
            </div>
          </form>
        </div>
      </div>

    </div>

    <!-- Actions Panel -->
    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-tools mr-2"></i>
            Actions
          </h5>
        </div>
        <div class="card-body">
          <button type="button"
                  class="btn btn-primary btn-block mb-2"
                  (click)="saveCostCodeSettings()"
                  [disabled]="loading">
            <i class="fas fa-save"></i> Save Letter Mapping
          </button>

          <button type="button"
                  class="btn btn-outline-secondary btn-block mb-2"
                  (click)="resetToDefaults()"
                  [disabled]="loading">
            <i class="fas fa-undo"></i> Reset to Defaults
          </button>

          <hr>

          <h6>How it works</h6>
          <div class="small">
            <p>When cost codes are enabled in barcode settings, numbers in cost codes will be displayed as the letters you configure here.</p>
            <p><strong>Example:</strong> If 2=B and 1=A, then cost code "21" will display as "BA"</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div *ngIf="loading" class="loading-overlay">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
