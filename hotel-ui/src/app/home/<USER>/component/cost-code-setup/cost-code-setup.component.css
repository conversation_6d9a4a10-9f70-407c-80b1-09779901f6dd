/* Component Title */
.component-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
}

/* Cards */
.card {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

/* Form Elements */
.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

/* Preview Alert */
.alert-light {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #495057;
}

/* Table */
.table-responsive {
  border-radius: 0.375rem;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.075);
}

/* Buttons */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

/* Pagination */
.pagination {
  margin-bottom: 0;
}

.page-link {
  color: #007bff;
  border-color: #dee2e6;
}

.page-link:hover {
  color: #0056b3;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
}

/* Statistics */
.small {
  font-size: 0.875rem;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Help Section */
.card-body ul {
  margin-bottom: 0;
  padding-left: 1.25rem;
}

.card-body ul li {
  margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .component-title {
    font-size: 1.5rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .btn-block {
    margin-bottom: 0.5rem;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .pagination {
    justify-content: center;
  }
  
  .page-link {
    padding: 0.375rem 0.75rem;
  }
}

@media (max-width: 576px) {
  .component-title {
    font-size: 1.25rem;
  }
  
  .card {
    margin-bottom: 1rem;
  }
  
  .table-responsive {
    font-size: 0.8rem;
  }
  
  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* Animation */
.card {
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn {
  transition: all 0.15s ease-in-out;
}

.table tbody tr {
  transition: background-color 0.15s ease-in-out;
}

/* Icons */
.fas {
  width: 1rem;
  text-align: center;
}

/* Alert */
.alert {
  border-radius: 0.375rem;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Form Groups */
.form-group {
  margin-bottom: 1rem;
}

.form-text {
  color: #6c757d;
  font-size: 0.875rem;
}

/* Custom Spacing */
.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}
