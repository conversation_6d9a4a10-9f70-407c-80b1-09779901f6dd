<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="component-title mb-0">
      <span *ngIf="!isModal">{{ 'CREATE_USER.TITLE' | translate }}</span>
      <span *ngIf="isModal">Update User</span>
    </h2>
    <div class="d-flex">
      <button type="button" class="btn btn-outline-primary" (click)="openHelpModal(helpModalTemplate)">
        <i class="fas fa-question-circle mr-1"></i> {{ 'CREATE_USER.HELP_BUTTON' | translate }}
      </button>
      <button *ngIf="isModal" type="button" class="close ml-3" aria-label="Close" (click)="closeModal()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
  </div>

  <form (ngSubmit)="saveUser();userForm.reset()" #userForm="ngForm" class="needs-validation">
    <div class="mb-4">
      <h5 class="section-title mb-3"><i
        class="fas fa-user-circle mr-2"></i>{{ 'CREATE_USER.USER_INFO_SECTION' | translate }}</h5>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="firstName" class="form-label">{{ 'CREATE_USER.FIRST_NAME' | translate }} <span
              class="text-danger">*</span></label>
            <input type="text" required #firstName="ngModel"
                   [class.is-invalid]="firstName.invalid && firstName.touched"
                   class="form-control" id="firstName"
                   placeholder="{{ 'CREATE_USER.FIRST_NAME_PLACEHOLDER' | translate }}" name="firstName"
                   [(ngModel)]="user.firstName">
            <div class="invalid-feedback" [class.d-block]="firstName.invalid && firstName.touched">
              {{ 'CREATE_USER.FIRST_NAME_ERROR' | translate }}
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label for="lastName" class="form-label">{{ 'CREATE_USER.LAST_NAME' | translate }} <span
              class="text-danger">*</span></label>
            <input type="text" required #lastName="ngModel"
                   [class.is-invalid]="lastName.invalid && lastName.touched"
                   class="form-control" id="lastName"
                   placeholder="{{ 'CREATE_USER.LAST_NAME_PLACEHOLDER' | translate }}" name="lastName"
                   [(ngModel)]="user.lastName">
            <div class="invalid-feedback" [class.d-block]="lastName.invalid && lastName.touched">
              {{ 'CREATE_USER.LAST_NAME_ERROR' | translate }}
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label for="userName" class="form-label">{{ 'CREATE_USER.USERNAME' | translate }} <span class="text-danger">*</span></label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
              </div>
              <input type="text" required #userName="ngModel"
                     [class.is-invalid]="userName.invalid && userName.touched || userAvailability"
                     class="form-control" id="userName"
                     placeholder="{{ 'CREATE_USER.USERNAME_PLACEHOLDER' | translate }}" name="userName"
                     [(ngModel)]="user.username" (keyup)="checkUserName()">
            </div>
            <div class="invalid-feedback" [class.d-block]="userName.invalid && userName.touched">
              {{ 'CREATE_USER.USERNAME_ERROR' | translate }}
            </div>
            <div class="invalid-feedback" [class.d-block]="userAvailability">
              {{ 'CREATE_USER.USERNAME_TAKEN' | translate }}
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label for="email" class="form-label">{{ 'CREATE_USER.EMAIL' | translate }} <span
              class="text-danger">*</span></label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
              </div>
              <input type="email" required #email="ngModel" [class.is-invalid]="email.invalid && email.touched"
                     class="form-control" id="email" placeholder="{{ 'CREATE_USER.EMAIL_PLACEHOLDER' | translate }}"
                     name="email"
                     [(ngModel)]="user.email" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$">
            </div>
            <div class="invalid-feedback" [class.d-block]="email.invalid && email.touched">
              {{ 'CREATE_USER.EMAIL_ERROR' | translate }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-4">
      <h5 class="section-title mb-3"><i class="fas fa-shield-alt mr-2"></i>{{ 'CREATE_USER.ROLE_SECTION' | translate }}
      </h5>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label for="userRole" class="form-label">{{ 'CREATE_USER.USER_ROLE' | translate }} <span
              class="text-danger">*</span></label>
            <select class="form-control custom-select" id="userRole" name="userRole" [(ngModel)]="userRole"
                    (change)="selectRole(userRole)">
              <option value="" disabled selected>{{ 'CREATE_USER.SELECT_ROLE' | translate }}</option>
              <option *ngFor="let ur of userRoles" [ngValue]="ur">{{ ur.name }}</option>
            </select>
            <small class="form-text text-muted">{{ 'CREATE_USER.ROLE_HELP' | translate }}</small>
          </div>
          <div class="form-group">
            <label class="d-block">{{ 'CREATE_USER.ASSIGNED_ROLES' | translate }}</label>
            <tag-input [(ngModel)]="user.userRoles"
                       [identifyBy]="'id'" [displayBy]="'name'"
                       name="userRoles" [hideForm]="true"
                       [placeholder]="'CREATE_USER.SELECT_ROLE' | translate" (onRemove)="removeFromUserRoles($event)"
                       [secondaryPlaceholder]="'CREATE_USER.NO_ROLES' | translate"
                       [theme]="'bootstrap'">
            </tag-input>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-4">
      <h5 class="section-title mb-3"><i class="fas fa-lock mr-2"></i>{{ 'CREATE_USER.SECURITY_SECTION' | translate }}
      </h5>

      <!-- Change Password Checkbox (only shown when editing) -->
      <div class="custom-control custom-switch mb-3" *ngIf="user.id">
        <input type="checkbox" class="custom-control-input" id="changePassword"
               name="changePassword" [(ngModel)]="changePassword">
        <label class="custom-control-label" for="changePassword">Change Password</label>
      </div>

      <div class="row" *ngIf="!user.id || changePassword">
        <div class="col-md-6">
          <div class="form-group">
            <label for="password" class="form-label">{{ 'CREATE_USER.PASSWORD' | translate }} <span class="text-danger">*</span></label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-key"></i></span>
              </div>
              <input type="password" #password="ngModel" [class.is-invalid]="password.invalid && password.touched"
                     class="form-control" id="password"
                     placeholder="{{ 'CREATE_USER.PASSWORD_PLACEHOLDER' | translate }}" name="password"
                     [(ngModel)]="user.password" required>
            </div>
            <div class="invalid-feedback" [class.d-block]="password.invalid && password.touched">
              {{ 'CREATE_USER.PASSWORD_ERROR' | translate }}
            </div>
            <small class="form-text text-muted">{{ 'CREATE_USER.PASSWORD_HELP' | translate }}</small>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label for="confirmPassword" class="form-label">{{ 'CREATE_USER.CONFIRM_PASSWORD' | translate }} <span
              class="text-danger">*</span></label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-lock"></i></span>
              </div>
              <input type="password" class="form-control"
                     [class.is-invalid]="user.password !== confirmPassword && confirmPassword"
                     id="confirmPassword" placeholder="{{ 'CREATE_USER.CONFIRM_PASSWORD_PLACEHOLDER' | translate }}"
                     name="confirmPassword"
                     [(ngModel)]="confirmPassword" required (ngModelChange)="checkPassword()">
            </div>
            <div class="invalid-feedback" [class.d-block]="user.password !== confirmPassword && confirmPassword">
              {{ 'CREATE_USER.PASSWORD_MISMATCH' | translate }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-4">
      <h5 class="section-title mb-3"><i class="fas fa-cogs mr-2"></i>{{ 'CREATE_USER.MODULE_SECTION' | translate }}</h5>
      <div class="row">
        <div class="col-md-5">
          <div class="form-group">
            <label for="moduleList" class="form-label">{{ 'CREATE_USER.MODULES' | translate }}</label>
            <select class="form-control custom-select" id="moduleList" name="moduleList" [(ngModel)]="selectedModule"
                    (change)="findPermsForModule()">
              <option value="" disabled selected>{{ 'CREATE_USER.SELECT_MODULE' | translate }}</option>
              <option *ngFor="let mod of modules" [ngValue]="mod">{{ mod.name }}</option>
            </select>
          </div>
        </div>
        <div class="col-md-5">
          <div class="form-group">
            <label for="permissionList" class="form-label">{{ 'CREATE_USER.PERMISSIONS' | translate }}</label>
            <select class="form-control custom-select" id="permissionList" name="permissionList"
                    [(ngModel)]="selectedPermission">
              <option value="" disabled selected>{{ 'CREATE_USER.SELECT_PERMISSION' | translate }}</option>
              <option *ngFor="let perm of selectedPermissions" [ngValue]="perm">{{ perm.name }}</option>
            </select>
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <label class="form-label">&nbsp;</label> <!-- Empty label to align with other inputs -->
            <button type="button" class="btn btn-primary btn-block"
                    [disabled]="!selectedPermission || !selectedPermission.id"
                    (click)="addPermissions(selectedPermission)">
              <i class="fas fa-plus-circle mr-1"></i> {{ 'CREATE_USER.ADD_BUTTON' | translate }}
            </button>
          </div>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-12">
          <label class="form-label">{{ 'CREATE_USER.ASSIGNED_PERMISSIONS' | translate }}</label>
          <tag-input [(ngModel)]="permissions"
                     [identifyBy]="'id'" [displayBy]="'name'"
                     name="modules" [hideForm]="true"
                     [placeholder]="'CREATE_USER.NO_PERMISSIONS' | translate"
                     [secondaryPlaceholder]="'CREATE_USER.NO_PERMISSIONS' | translate"
                     [theme]="'bootstrap'">
          </tag-input>
          <small class="form-text text-muted mt-2">{{ 'CREATE_USER.PERMISSIONS_HELP' | translate }}</small>
        </div>
      </div>
    </div>

    <div class="mb-4">
      <h5 class="section-title mb-3"><i class="fas fa-user-cog mr-2"></i>{{ 'CREATE_USER.ACCOUNT_SECTION' | translate }}
      </h5>
      <div class="custom-control custom-switch">
        <input type="checkbox" class="custom-control-input" id="active"
               name="active" [(ngModel)]="user.active">
        <label class="custom-control-label" for="active">{{ 'CREATE_USER.ACCOUNT_ACTIVE' | translate }}</label>
      </div>
      <small class="form-text text-muted">{{ 'CREATE_USER.ACCOUNT_HELP' | translate }}</small>
    </div>
    <div class="d-flex justify-content-between mt-4 mb-2">
      <button type="button" class="btn btn-outline-secondary" (click)="clearForm()">
        <i class="fas fa-undo mr-1"></i> {{ 'CREATE_USER.RESET_BUTTON' | translate }}
      </button>
      <button type="submit" class="btn btn-primary btn-lg px-5"
              [disabled]="!userForm.form.valid || !isPasswordMatch">
        <i class="fas fa-save mr-1"></i>
        <span *ngIf="!isModal">{{ 'CREATE_USER.CREATE_BUTTON' | translate }}</span>
        <span *ngIf="isModal">Update</span>
      </button>
    </div>
  </form>

  <!-- Help Modal Template -->
  <ng-template #helpModalTemplate>
    <div class="modal-header">
      <h5 class="modal-title">{{ 'CREATE_USER.HELP_MODAL_TITLE' | translate }}</h5>
      <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <h6>{{ 'CREATE_USER.HELP_MODAL_SUBTITLE1' | translate }}</h6>
      <p>{{ 'CREATE_USER.HELP_MODAL_TEXT1' | translate }}</p>

      <h6>{{ 'CREATE_USER.HELP_MODAL_SUBTITLE2' | translate }}</h6>
      <p>{{ 'CREATE_USER.HELP_MODAL_TEXT2' | translate }}</p>

      <h6>{{ 'CREATE_USER.HELP_MODAL_SUBTITLE3' | translate }}</h6>
      <p>{{ 'CREATE_USER.HELP_MODAL_TEXT3' | translate }}</p>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary"
              (click)="modalRef.hide()">{{ 'CREATE_USER.HELP_MODAL_CLOSE' | translate }}
      </button>
    </div>
  </ng-template>
</div>
