import {Component, OnInit, TemplateRef} from '@angular/core';
import {User} from '../../model/user';
import {Role} from '../../model/role';
import {BsModalRef, ModalOptions} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Permission} from '../../model/permission';
import {Module} from '../../model/module';
import {UserService} from '../../service/user.service';
import {NotificationService} from '../../../core/service/notification.service';
import {RoleService} from '../../service/role.service';
import {PermissionService} from '../../service/permission.service';
import {TagModelClass} from "ngx-chips/core/accessor";

@Component({
  selector: 'app-create-user',
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.css']
})
export class CreateUserComponent implements OnInit {

  search: any;

  user = new User();
  userRole = new Role();
  userRoles: Array<Role> = [];
  confirmPassword: string;
  selectedRow: number;
  setClickedRow: Function;
  modalRef: BsModalRef;
  modules: Array<Module> = [];
  selectedModule: Module;
  selectedPermission: Permission;
  selectedPermissions: Array<Permission> = [];
  userAvailability: boolean;
  permissions: Array<Permission> = [];
  isFromModal: boolean;
  isPasswordMatch: boolean;
  isPermAdded: boolean;
  changePassword: boolean = false;
  isModal: boolean = false;

  ngOnInit() {
    this.permissions = [];
    this.userAvailability = false;
    this.user = new User();
    this.user.userRoles = [];
    this.selectedPermission = new Permission();
    this.selectedPermissions = [];
    this.selectedModule = new Module();
    this.user.desktopPermissions = [];
    this.user.active = true;
    this.isFromModal = false;
    this.isPasswordMatch = false;
    this.isPermAdded = false;
    this.findAllRole();
    this.getAllModulesForUser();

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  constructor(private userService: UserService, private notificationService: NotificationService,
              private roleService: RoleService, private permService: PermissionService,
              private modalService: BsModalService) {
  }

  /**
   * Opens the help modal
   * @param template The modal template reference
   */
  openHelpModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {
      class: 'modal-dialog-centered'
    });
  }

  saveUser() {
    this.user.permissions = this.permissions;

    // Handle password based on changePassword flag
    if (this.user.id && !this.changePassword) {
      // If editing an existing user and not changing password, set to NOCHNG
      this.user.password = 'NOCHNG';
    }

    if (this.user.permissions.length === 0) {
      this.notificationService.showError('Add Permissions first');
    } else if (this.user.permissions.length !== 0) {
      this.userService.save(this.user).subscribe(result => {
        if (result === true) {
          const successMessage = this.user.id ? "User Updated Successfully" : "User Created Successfully";
          this.notificationService.showSuccess(successMessage);
          this.user.permissions.length = 0;
          this.clearForm();
          if (this.modalRef) {
            this.modalRef.hide();
          }
          this.ngOnInit();
        } else {
          const errorMessage = this.user.id ? "Updating User Failed" : "Creating User Failed";
          this.notificationService.showWarning(errorMessage);
        }
      });
    }
  }

  selectRole(item: Role) {
    let available = false;
    for (const i of this.user.userRoles) {
      if (i.id === item.id) {
        available = true;
      }
    }
    if (!available) {
      this.user.userRoles.push(item);
    }
  }

  findAllRole() {
    this.roleService.findAll().subscribe((data: Array<Role>) => {
      this.userRoles = data;
    });
  }

  clearForm() {
    this.user.userRoles = [];
    this.user.firstName = "";
    this.user.lastName = '';
    this.user.password = '';
    this.confirmPassword = '';
    this.ngOnInit();
  }

  getAllModulesForUser() {
    this.permService.getEnabledModules(this.user.username).subscribe((result: Array<Module>) => {
      this.modules = result;
    })
  }

  addPermissions(selectedPermission) {
    let available = false;
    for (const perm of this.permissions) {
      if (perm.id === selectedPermission.id) {
        available = true;
        this.notificationService.showWarning("This permission already added");
      }
    }
    if (!available) {
      this.permissions.push(this.selectedPermission);
      this.isPermAdded = true;
    }


  }

  selectPerm(perm: Permission) {
    let available = false;
    for (const h of this.user.desktopPermissions) {
      if (h.id === perm.id) {
        available = true;
      }
    }
    if (!available) {
      this.user.desktopPermissions.push(perm);
    }
  }

  findPermsForModule() {
    this.selectedPermissions = [];
    this.permService.findPermsByModule(this.selectedModule.id).subscribe((result: Array<Permission>) => {
      this.selectedPermissions = result;
    })
  }

  removeFromUserRoles(event: Role | TagModelClass) {
    for (let i = 0; i < this.user.userRoles.length; i++) {
      if (this.user.userRoles[i].name === event.name) {
        this.user.userRoles.splice(i);
      }
    }
  }

  removeFromPermission(event: Permission | TagModelClass) {
    for (let i = 0; i < this.user.desktopPermissions.length; i++) {
      if (this.user.desktopPermissions[i].name === event.name) {
        this.user.desktopPermissions.splice(i);
      }
    }
  }

  checkUserName() {
    this.userService.checkBike(this.user.username).subscribe((res: boolean) => {
      this.userAvailability = res;
    });
  }

  checkPassword() {
    if (this.user.password === this.confirmPassword) {
      this.isPasswordMatch = true;
    } else {
      this.isPasswordMatch = false;
    }
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }
}

