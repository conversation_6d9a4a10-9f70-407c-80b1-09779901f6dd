import { Component, OnInit } from '@angular/core';
import { PermissionService } from '../../service/permission.service';
import { PermissionsSidebarService } from '../../service/permissions-sidebar.service';
import { TranslateService } from '../../../../translate.service';
import { ToastrService } from 'ngx-toastr';
import { Company } from '../../model/company';

@Component({
  selector: 'app-starter',
  templateUrl: './starter.component.html',
  styleUrls: ['./starter.component.css']
})
export class StarterComponent implements OnInit {

  today: number = Date.now();
  user: any;
  company: Company;
  permissions: Array<any> = [];
  modulePermissions: Array<any> = [];
  selectedModule: string | null = null;
  selectedModuleData: any = null;
  currentLang: string = 'en';

  constructor(
    private permissionService: PermissionService,
    private permissionsSidebarService: PermissionsSidebarService,
    private translateService: TranslateService,
    private toastr: ToastrService
  ) {
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.company = new Company();
    this.company.name = 'S-out Catalyst';
    this.currentLang = localStorage.getItem('lang') || 'en';
    this.findUserPermissions();
  }

  ngOnInit() {
  }

  findUserPermissions() {
    // Get all available permissions instead of just desktop permissions
    this.permissionService.findAvailablePermissions(this.user.username).subscribe((result: Array<any>) => {
      this.permissions = result;
      this.processPermissionsByModule();
    });
  }

  /**
   * Process permissions and group by module
   */
  processPermissionsByModule() {
    this.modulePermissions = [];

    this.permissions.forEach(permission => {
      let existingModule = this.modulePermissions.find(module =>
        module.name === permission.module.name
      );

      if (existingModule) {
        existingModule.perms.push({
          name: permission.name,
          route: permission.route,
          iconCss: permission.iconCss
        });
      } else {
        this.modulePermissions.push({
          name: permission.module.name,
          perms: [{
            name: permission.name,
            route: permission.route,
            iconCss: permission.iconCss
          }]
        });
      }
    });

    // Sort modules by name for consistent display
    this.modulePermissions.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Get icon for module based on module name
   */
  getModuleIcon(moduleName: string): string {
    const moduleIcons = {
      'Inventory': 'fas fa-boxes',
      'Trade': 'fas fa-handshake',
      'Report': 'fas fa-chart-bar',
      'Admin': 'fas fa-user-cog',
      'HR': 'fas fa-users',
      'Dashboard': 'fas fa-tachometer-alt',
      'Core': 'fas fa-cog',
      'Restaurant': 'fas fa-utensils'
    };

    return moduleIcons[moduleName] || 'fas fa-folder';
  }

  /**
   * Get description for module
   */
  getModuleDescription(moduleName: string): string {
    const moduleDescriptions = {
      'Inventory': 'Manage items, categories, brands, and stock',
      'Trade': 'Handle sales, purchases, customers, and suppliers',
      'Report': 'Generate reports and analytics',
      'Admin': 'System administration and user management',
      'HR': 'Human resources and employee management',
      'Dashboard': 'Overview and key metrics',
      'Core': 'Core system functions',
      'Restaurant': 'Manage tables, orders, and POS'
    };

    return moduleDescriptions[moduleName] || 'Module functions and features';
  }

  /**
   * Toggle permissions sidebar
   */
  toggleSidebar() {
    this.permissionsSidebarService.setPermissions(this.permissions);
    this.permissionsSidebarService.showSidebar();
  }

  /**
   * Open help documentation links
   */
  openHelpLink(type: string) {
    const helpUrls = {
      'user-guide': 'https://viganana.com/general/help/user-guide',
      'faq': 'https://viganana.com/general/help/faq',
      'support': 'https://viganana.com/general/support'
    };

    const url = helpUrls[type];
    if (url) {
      window.open(url, '_blank');
    }
  }

  /**
   * Open YouTube video tutorials
   */
  openVideoTutorial(type: string) {
    const videoUrls = {
      'getting-started': 'https://www.youtube.com/watch?v=YOUR_VIDEO_ID_1',
      'inventory-management': 'https://www.youtube.com/watch?v=YOUR_VIDEO_ID_2',
      'sales-process': 'https://www.youtube.com/watch?v=YOUR_VIDEO_ID_3',
      'reports-analytics': 'https://www.youtube.com/watch?v=YOUR_VIDEO_ID_4'
    };

    const url = videoUrls[type];
    if (url) {
      window.open(url, '_blank');
    }
  }

  /**
   * Select a module and display its permissions
   */
  selectModule(module: any) {
    this.selectedModule = module.name;
    this.selectedModuleData = module;
  }

  /**
   * Switch between languages
   */
  switchLanguage(lang: string) {
    this.currentLang = lang;
    this.toastr.info(`Changing language to ${lang === 'en' ? 'English' : 'Sinhala'}`, 'Language');

    this.translateService.use(lang).then(() => {
      window.location.href = window.location.href.split('#')[0];
    }).catch(error => {
      console.error('Error switching language:', error);
      this.toastr.error(`Error switching language to ${lang}. Please try again.`, 'Error');
    });
  }
}
