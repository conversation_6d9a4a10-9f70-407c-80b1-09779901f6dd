import {Room} from "./room";
import {MetaData} from "../../../core/model/metaData";
import {Customer} from "../../trade/model/customer";
import {SalesInvoiceRecord} from "../../trade/model/sales-invoice-record";
import {BoardType} from "./board-type";

export class Reservation {
  public id: string;
  public invoiceNo: string;
  public reservationCode: string;
  public room: Room;
  public customer: Customer;
  public salesInvoiceRecords: Array<SalesInvoiceRecord>;
  public from: Date;
  public to: Date;
  public boardType: BoardType;
  public active: boolean;
  public roomBook: boolean;
  public todayBook: boolean;
  public reservationStatus: MetaData;
  public invoiceStatus: MetaData;
  public subTotal: number;
  public totalAmount: number;
  public price: number;
  public dueDate: Date;
  public roomCharge: number;
  public totalDiscount: number;
  public payment: number;
  public cashBalance: number;
  public serviceCharge: number;
  public counterNo: string;
  public reservationDate: string;
  public invoiceDate: string;
  public createdDate: string;
  public createdBy: string;
  public lastModifiedDate: string;
  public lastModifiedBy: string;
}
