import {ReservationSiRecord} from "./reservation-si-record";
import {Customer} from "../../trade/model/customer";
import {MetaData} from "../../../core/model/metaData";
import {Room} from "./room";
import {Reservation} from "./reservation";

export class ReservationSi {

  id: string;
  invoiceNo: string;
  date:string;
  subTotal: number;
  totalAmount: number;
  balance: number;
  totalDiscount: number;
  payment: number;
  dueDate: Date;
  cashBalance: number;
  paymentMethod: MetaData;
  reservationSIRecord: Array<ReservationSiRecord>;
  customerName: string;
  customer: Customer;
  room: Room;
  directMode: boolean;
  reservation: Reservation;
  status: MetaData;

}
