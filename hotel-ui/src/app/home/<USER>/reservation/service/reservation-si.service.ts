import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {ReservationApiConstants} from "../reservation-constants";
import {TradeConstants} from "../../trade/trade-constants";

@Injectable({
  providedIn: 'root'
})
export class ReservationSiService {

  constructor(private http: HttpClient) {
  }

  public save(reservationSi) {
    return this.http.post<any>(ReservationApiConstants.SAVE_RESERVATION_SALES_INVOICE, reservationSi);
  }

  public findAll(page, pageSize) {
    return this.http.get(ReservationApiConstants.GET_RESERVATION_SALES_INVOICE, {params: {page: page, pageSize: pageSize}});
  }

  public findAllByInvoiceNo(invoiceNo: string) {
    return this.http.get(ReservationApiConstants.FIND_ALL_BY_RESERVATION_SALES_INVOICE_NO, {params: {invoiceNo: invoiceNo}});
  }

  public findByCustomerNicBr(nicBr) {
    return this.http.get(ReservationApiConstants.FIND_SI_BY_CUST_NIC_BR, {params: {nicBr: nicBr}});
  }

  public findByInvoiceNoLike(keyInvoiceNo) {
    return this.http.get(ReservationApiConstants.FIND_ALL_BY_INVOICE_NO, {params: {keyInvoiceNo: keyInvoiceNo}});
  }

  public   findByDate(date) {
    return this.http.get(ReservationApiConstants.FIND_BY_DATE, {params: {date: date}})
  }
}
