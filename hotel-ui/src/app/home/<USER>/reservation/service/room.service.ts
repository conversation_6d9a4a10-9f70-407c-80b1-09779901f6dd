import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {ReservationApiConstants} from "../reservation-constants";

@Injectable({
  providedIn: 'root'
})

export class RoomService {

  constructor (private http: HttpClient) {
  }

  save (room) {
    return this.http.post<any>(ReservationApiConstants.SAVE_ROOM, room);
  }

  public findAll (page, pageSize) {
    return this.http.get(ReservationApiConstants.GET_ROOMS, {params: {page: page, pageSize: pageSize}});
  }

  public findByName (name) {
    return this.http.get(ReservationApiConstants.SEARCH_ROOM, {params: {any: name}});
  }


  findByNameLike(name: string) {
    return this.http.get(ReservationApiConstants.FIND_BY_ROOM_NAME, {params: {name: name}});
  }

  findByRoomNo(roomNo) {
    return this.http.get(ReservationApiConstants.FIND_BY_ROOM_NO, {params: {roomNo: roomNo}});
  }
}
