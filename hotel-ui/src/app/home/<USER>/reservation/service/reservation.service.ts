import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {ReservationApiConstants} from "../reservation-constants";

@Injectable({
  providedIn: 'root'
})

export class ReservationService {

  constructor(private http: HttpClient) {
  }

  saveReservation(reservation) {
    return this.http.post<any>(ReservationApiConstants.SAVE_RESERVATION, reservation);
  }

  public findAll(page, pageSize) {
    return this.http.get(ReservationApiConstants.GET_RESERVATION, {params: {page: page, pageSize: pageSize}});

  }

  public checkDate(roomId, from) {
    return this.http.get(ReservationApiConstants.FROM_DATE_CHECK, {params: {roomId: roomId, from: from}});
  }

  public findOneById(search) {
    return this.http.get(ReservationApiConstants.FIND_ONE_BY_ID, {params: {id: search}});
  }

  public findById(id) {
    return this.http.get(ReservationApiConstants.FIND_BY_ID, {params: {id : id}});
  }

  public findByStatusLike(booked) {
    return this.http.get(ReservationApiConstants.FIND_BY_STATUS, {params: {booked : booked}});
  }

  public findByRoomNo(id, status) {
    return this.http.get(ReservationApiConstants.FIND_RESERVATION_BY_ROOM_NO, {params: {id: id, status: status}});
  }

  public findReservation(reservationId: string, roomId: string) {
    return this.http.get(ReservationApiConstants.FIND_RESERVATION_BY_RESERVATION_ID, {params: {reservationId : reservationId, roomId : roomId}});
  }

  saveInvoice(reservationInvoice) {
    return this.http.post(ReservationApiConstants.SAVE_RESERVATION_INVOICE, reservationInvoice);
  }

  findAllPendingReservations() {
    return this.http.get(ReservationApiConstants.FIND_ALL_PENDING_RESERVATIONS);
  }

  findReservationInvoiceByInvNo(invoiceNo) {
    return this.http.get(ReservationApiConstants.FIND_RESERVATION_INVOICE_BY_INV_NO, {
      params: {invoiceNo: invoiceNo}
    });
  }
}
