import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {ReservationApiConstants} from "../reservation-constants";

@Injectable({
  providedIn: 'root'
})

export class ReservationItemService {


  constructor (private http: HttpClient) {
  }

  save (reservationItem) {
    return this.http.post<any>(ReservationApiConstants.SAVE_RESERVATION_ITEM, reservationItem);
  }

  public findAll (page, pageSize) {
    return this.http.get(ReservationApiConstants.GET_RESERVATION_ITEM, {params: {page: page, pageSize: pageSize}});
  }

  public findByReservationItem (reservationItemName) {
    return this.http.get(ReservationApiConstants.SEARCH_RESERVATION_ITEM, {params: {reservationItemName: reservationItemName}});
  }


}
