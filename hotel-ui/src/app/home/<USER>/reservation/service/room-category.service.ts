import {Injectable} from "@angular/core";
import {HttpClient} from "@angular/common/http";
import {ItemType} from "../../inventory/model/item-type";
import {ReservationApiConstants} from "../reservation-constants";
import {RoomCategory} from "../model/room-category";

@Injectable({
  providedIn: 'root'
})

export class RoomCategoryService {

  constructor (private http: HttpClient) {
  }

  public save (roomCategory: RoomCategory) {
    return this.http.post<any>(ReservationApiConstants.SAVE_ROOM_CATEGORY, roomCategory);
  }

  public findAll (page, pageSize) {
    return this.http.get(ReservationApiConstants.GET_ROOM_CATEGORY, {params: {page: page, pageSize: pageSize}});
  }

  public findByName (name) {
    return this.http.get(ReservationApiConstants.SEARCH_ROOM_CATEGORY, {params: {any: name}});
  }

}
