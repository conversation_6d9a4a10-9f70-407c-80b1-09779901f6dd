import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {ReservationApiConstants} from "../reservation-constants";

@Injectable({
  providedIn: 'root'
})
export class BoardTypeService {

  constructor(private http: HttpClient) { }

  save(boardType) {
    return this.http.post(ReservationApiConstants.SAVE_BOARD_TYPE, boardType);
  }

  findBoardTypeByNameLike(boardType) {
    return this.http.get(ReservationApiConstants.FIND_BOARD_TYPE_BY_NAME_LIKE, {params: boardType});
  }

  findAllPageable(page, pageSize) {
    return this.http.get(ReservationApiConstants.FIND_ALL_BOARD_TYPES_PAGEABLE, {
      params: {page:page, pageSize: pageSize}
    });
  }

  findById(id) {
    return this.http.get(ReservationApiConstants.FIND_BOARD_TYPE_BY_ID, {params: {id: id}});
  }

  findAll() {
    return this.http.get(ReservationApiConstants.FIND_ALL_BOARD_TYPES);
  }
}
