import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ManageRoomComponent} from "./component/manage-room/manage-room.component";
import {RoomCategoryComponent} from "./component/room-category/room-category.component";
import {ReservationComponent} from "./component/reservation/reservation.component";
import {ManageReservationSiComponent} from "./component/manage-reservation-si/manage-reservation-si.component";
import {ViewReservationInvoiceComponent} from "./component/view-reservation-invoice/view-reservation-invoice.component";
import {ViewAllRoomComponent} from "./component/view-all-room/view-all-room.component";
import {ReservationSiComponent} from "./component/reservation-si/reservation-si.component";
import {ReservationInvoiceComponent} from "./component/reservation-invoice/reservation-invoice.component";
import {BoardTypeComponent} from "./component/board-type/board-type.component";

const routes: Routes = [

  {
    path: 'manage_room',
    component: ManageRoomComponent
  },
  {
    path: 'room_category',
    component: RoomCategoryComponent
  },
  {
    path: 'reservation',
    component: ReservationComponent
  },
  {
    path: 'manage_reservation_si',
    component: ManageReservationSiComponent
  },
  {
    path: 'board_type',
    component: BoardTypeComponent
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class ReservationRoutingModule {
}

export const reservationRouteParams = [ManageRoomComponent, RoomCategoryComponent, ReservationComponent,
  ManageReservationSiComponent,ViewReservationInvoiceComponent, ViewAllRoomComponent,
  ReservationSiComponent,ReservationInvoiceComponent];
