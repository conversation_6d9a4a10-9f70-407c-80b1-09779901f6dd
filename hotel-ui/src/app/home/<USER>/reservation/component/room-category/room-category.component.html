<div class="card">
  <div class="card-header">
    <strong>Manage Room Category</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <div class="input-group mb-2">
            <input [(ngModel)]="keyRoomCategory"
                   [typeahead]="roomCategorySearched"
                   (typeaheadLoading)="loadRoomCategory()"
                   (typeaheadOnSelect)="setSelectedRoomCategory($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   placeholder="Search Room Category"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="item">
          </div>
        </div>

        <table class="table table-hover">
          <thead>
          <tr>
            <th>Room Category Name</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let roomCat of roomCategories"
              (click)="onSelect(roomCat)" [class.active]="roomCat === selectedRoomCategory">
            <td>{{roomCat.name}}</td>
          </tr>
          </tbody>
        </table>

        <div class="row">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>

      <div class="col-md-6">

        <form #ManageRoomCategoryForm="ngForm">
          <label>Room Category Name</label>
          <div class="form-group">
            <input type="text"
                   required
                   #roomCategoryName="ngModel"
                   [class.is-invalid]="roomCategoryName.invalid && roomCategoryName.touched"
                   class="form-control"
                   id="roomCategoryName"
                   [(ngModel)]="roomCategory.name"
                   name="name"
                   placeholder=" Item Type Name">
            <div *ngIf="roomCategoryName.errors && (roomCategoryName.invalid || roomCategoryName.touched)">
              <small class="text-danger" [class.d-none]="roomCategoryName.valid || roomCategoryName.untouched">
                *Room Category Name is required
              </small>
            </div>
          </div>

          <label>Description</label>
          <div class="form-group">
            <textarea required
                      #Description="ngModel"
                      [class.is-invalid]="Description.invalid && Description.touched"
                      class="form-control"
                      id="Description"
                      [(ngModel)]="roomCategory.description" name="description"
                      placeholder="Description ">
              </textarea>
            <div *ngIf="Description.errors && (Description.invalid || Description.touched)">
              <small class="text-danger" [class.d-none]="Description.valid || Description.untouched">*Description is
                required
              </small>
            </div>
          </div>

          <div class="form-check checkbox mr-2">
            <input class="form-check-input" id="active" name="active" type="checkbox"
                   [(ngModel)]="roomCategory.active">
            <label class="form-check-label" for="active">Active</label>
          </div>

          <div class="row pull-right">
            <div class="btn-block">
              <button class="btn btn-success mr-3"
                      [disabled]="selectedRoomCategory != null && !ManageRoomCategoryForm.form.valid" (click)="save(); ManageRoomCategoryForm.reset()">
                Save
              </button>
              <button type="button" class="btn btn-primary mr-3"
                      [disabled]="selectedRoomCategory === null && !ManageRoomCategoryForm.form.valid"
                      (click)="openModal(templateUpdate)">Update
              </button>
              <button type="button" (click)="clear()" class="btn btn-warning mr-3">Clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>

  </div>
</div>

<ng-template #templateUpdate>
  <div class="modal-body text-center ">
    <p>Do you really want to update selected?</p>
    <button type="button" class="btn btn-default" (click)="confirmUpdate()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>


