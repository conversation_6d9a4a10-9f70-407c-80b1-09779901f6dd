import {Component, OnInit, TemplateRef} from '@angular/core';
import {RoomCategory} from "../../model/room-category";
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {RoomCategoryService} from "../../service/room-category.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {ItemType} from "../../../inventory/model/item-type";

@Component({
  selector: 'app-room-category',
  templateUrl: './room-category.component.html',
  styleUrls: ['./room-category.component.css']
})
export class RoomCategoryComponent implements OnInit {

  roomCategory : RoomCategory;
  selectedRoomCategory: RoomCategory;
  roomCategories: Array<RoomCategory> = [];
  collectionSize;
  page;
  pageSize;
  keyRoomCategory: string;
  roomCategorySearched: Array<RoomCategory> = [];
  modalRef: BsModalRef;

  constructor(private roomCategoryService : RoomCategoryService,
              private notification: NotificationService,
              private modalService: BsModalService) { }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 10;
    this.selectedRoomCategory = null;
    this.roomCategory = new RoomCategory();
    this.roomCategory.active = true;
    this.findAll();
  }

  openModal(template: TemplateRef<any>) {
    if (this.selectedRoomCategory) {
      this.modalRef = this.modalService.show(template, {class: 'modal-sm'});
    } else {
      this.notification.showError('please select a row!');
    }
  }

  confirmUpdate() {
    if (this.selectedRoomCategory) {
      this.modalRef.hide();
      this.roomCategoryService.save(this.roomCategory).subscribe((result) => {
        this.notification.showSuccess("Room Category Create Successfully");
      });
      this.ngOnInit();
    } else {
      this.notification.showError('please select a row');
    }
  }

  decline(): void {
    this.modalRef.hide();
    this.ngOnInit();
  }


  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  save() {
    this.roomCategoryService.save(this.roomCategory).subscribe(result => {
      console.log(this.roomCategory);
      if(result.code === 200){
        this.notification.showSuccess(result.message);
        this.ngOnInit();
      }else{
        this.notification.showError(result.message);
      }
      this.clear();
    });
  }

  findAll() {
    this.roomCategoryService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.roomCategories = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  clear() {
    this.keyRoomCategory = null;
    this.ngOnInit();
  }

  onSelect(cat: ItemType) {
    this.selectedRoomCategory = cat;
    this.roomCategory = this.selectedRoomCategory;
  }

  loadRoomCategory() {
    return this.roomCategoryService.findByName(this.keyRoomCategory).subscribe((data: Array<RoomCategory>) => {
      return this.roomCategorySearched = data;
    });
  }

  setSelectedRoomCategory(event) {
    this.roomCategory = event.item;
  }

}
