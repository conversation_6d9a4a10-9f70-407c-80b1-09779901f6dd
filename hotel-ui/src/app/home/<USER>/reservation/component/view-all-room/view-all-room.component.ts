import { Component, OnInit } from '@angular/core';
import {Room} from "../../model/room";
import {RoomService} from "../../service/room.service";
import {Reservation} from "../../model/reservation";
import {ReservationService} from "../../service/reservation.service";
import {formatDate} from "@angular/common";

@Component({
  selector: 'app-view-all-room',
  templateUrl: './view-all-room.component.html',
  styleUrls: ['./view-all-room.component.css']
})
export class ViewAllRoomComponent implements OnInit {

  roomForTable = new Room();
  reservation : Array<Reservation>;
  reservationForView : Array<Reservation>;
  selectedRow: number;
  page;
  pageSize;
  today: Date;
  resBook: string;

  constructor(private roomService: RoomService,
              private reservationService: ReservationService) { }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 20;
    this.resBook = "Booked";
    this.findAllRoom();
    this.findAllBookedReservation();
    this.today = new Date();
    this.reservationForView = [];
  }

  findAllRoom() {
    this.roomService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.roomForTable = data.content;
    });
  }

  findAllBookedReservation() {
    this.reservationService.findByStatusLike(this.resBook).subscribe((result: Array<Reservation>) => {
      this.reservation = result;
      this.roomUpdate(result);
    })
  }
  //
  // roomUpdate(reservation: Array<Reservation>){
  //   for (let res of reservation){
  //     let from = formatDate(res.from, 'M/d/yyyy', 'en_US')
  //     let to = formatDate(res.to, 'M/d/yyyy', 'en_US')
  //     let today = formatDate(this.today, 'M/d/yyyy', 'en_US')
  //     if (from <= today && today <= to){
  //       res.todayBook = true;
  //     }else {
  //       res.todayBook = false;
  //     }
  //     this.reservationForView.push(res);
  //   }
  // }

  roomUpdate(reservation: Array<Reservation>){
    for (let res of reservation){
      if (this.today >= new Date(res.from) && this.today <= new Date(res.to)) {
        res.todayBook = true;
      } else {
        res.todayBook = false;
      }
      this.reservationForView.push(res);
    }
  }

}
