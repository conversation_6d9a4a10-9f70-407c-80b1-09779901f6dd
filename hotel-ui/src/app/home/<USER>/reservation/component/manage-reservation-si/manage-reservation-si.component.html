<div class="card">
  <div class="card-header">
    <strong>Manage Sales Invoices</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyInvoiceNo"
                 [typeahead]="rsis"
                 (typeaheadLoading)="loadInvoiceNo()"
                 (typeaheadOnSelect)="setSelectedInvoiceNo($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="invoiceNo"
                 placeholder="Search By Invoice"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="invoiceNo">
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyCustomerNicBr"
                 [typeahead]="customers"
                 (typeaheadLoading)="loadCustomerByNic()"
                 (typeaheadOnSelect)="setSelectedCustomer($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="nicBr"
                 autocomplete="off"
                 placeholder="Search By NIC"
                 class="form-control" name="nic">
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input required #startDate="ngModel"
                 type="text"
                 name="startDate" id="startDate"
                 [(ngModel)]="sDate"
                 bsDatepicker
                 [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                 class="form-control"
                 placeholder="Enter Job Date"
                 (ngModelChange)="findByDate()">
        </div>
      </div>

    </div>

    <div class="row mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Invoice No</th>
          <th scope="col">Customer Name</th>
          <th scope="col">Date</th>
          <th scope="col">Amount</th>
          <th scope="col">Balance</th>
          <!--          <th scope="col">Status</th>-->
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let rsi of rsis,let i = index"
            (click)="selectSi(rsi,i)"
            [class.active]="i === selectedRow" align="center">

          <td>{{rsi.invoiceNo}}</td>
          <td>{{rsi.customerName ? rsi.customerName : 'Default Customer'}}</td>
          <td>{{rsi.date | date:'short': '+530'}}</td>
          <td>{{rsi.totalAmount | number : '1.2-2'}}</td>
          <td>{{rsi.cashBalance | number: '1.2-2'}}</td>
          <!--          <td>{{rsi.status.value ? rsi.status.value : 'N/A'}}</td>-->
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [maxSize]="maxSize"
                      [boundaryLinks]="true"
                      [(ngModel)]="page"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger pull-right ml-2" (click)="print()">View</button>
        <!--        <button type="button" class="btn btn-danger pull-right ml-2" [disabled]="selectedRsi.cashBalance == 0"-->
        <!--                (click)="payBalance()">Pay Balance-->
        <!--        </button>-->
      </div>
    </div>
  </div>
</div>
