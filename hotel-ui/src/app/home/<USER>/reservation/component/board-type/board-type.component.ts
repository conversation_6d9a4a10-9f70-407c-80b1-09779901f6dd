import { Component, OnInit } from '@angular/core';
import {BoardType} from "../../model/board-type";
import {BoardTypeService} from "../../service/board-type.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {THIS_EXPR} from "@angular/compiler/src/output/output_ast";
import {Response} from "../../../../core/model/response";

@Component({
  selector: 'app-board-type',
  templateUrl: './board-type.component.html',
  styleUrls: ['./board-type.component.css']
})
export class BoardTypeComponent implements OnInit {
  keyBoardType: string;
  boardTypes: Array<BoardType>;
  boardType: BoardType;
  isProcessing: boolean;
  collectionSize: number;
  page;
  pageSize;

  constructor(private boardTypeService: BoardTypeService, private notificationService: NotificationService) { }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 10;
    this.boardType = new BoardType();
    this.findAll();
  }

  loadBoardType() {
    this.boardTypeService.findBoardTypeByNameLike(this.keyBoardType).subscribe((data: Array<BoardType>)=>{
      this.boardTypes = data;
    });
  }

  findAll(){
    this.boardTypeService.findAllPageable(this.page - 1, this.pageSize).subscribe((data: any)=>{
      this.boardTypes = data.content;
      this.collectionSize = data.totalPages * 10;
    })
  }

  setSelectedType(event) {
    this.boardTypeService.findById(event.item.id).subscribe((data: BoardType)=>{
      this.boardTypes = [];
      this.boardTypes.push(data);
      this.keyBoardType = '';
    });
  }

  pageChanged(event){

  }

  save() {
    this.isProcessing = true;
    this.boardTypeService.save(this.boardType).subscribe((response: Response)=>{
      if (200 === response.code){
        this.notificationService.showSuccess(response.message);
        this.ngOnInit();
        this.isProcessing = false;
      }else {
        this.notificationService.showError(response.message);
        this.isProcessing = false;
      }
    });
  }

  clear(){
    this.boardType = new BoardType();
  }
}
