<div class="card">
  <div class="card-header">
    <strong>BOARD TYPE</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6 border-right">
        <div class="input-group">
          <input
            (typeaheadLoading)="loadBoardType()"
            (typeaheadOnSelect)="setSelectedType($event)"
            [(ngModel)]="keyBoardType"
            [typeaheadOptionsLimit]="7"
            [typeahead]="boardTypes"
            autocomplete="off"
            class="form-control"
            name="boardTypeSearch"
            placeholder="Search By Name"
            type="text"
            typeaheadOptionField="name"
            typeaheadWaitingMs="1000">
        </div>


        <table class="table table-striped table-bordered mt-4">
          <thead>
          <th>Name</th>
          <th>Charge</th>
          </thead>
          <tbody>
          <tr *ngFor="let boardType of boardTypes, let i = index">
            <td>{{boardType.name}}</td>
            <td>{{boardType.charge}}</td>
          </tr>
          </tbody>
        </table>

        <div class="row">
          <div class="col-12">
            <pagination class="pagination-sm justify-content-center">
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              (pageChanged)="pageChanged($event)"
            </pagination>
          </div>
        </div>
      </div>
      <div class="col-md-6 pl-3">
          <form #boardTypeForm="ngForm" class="form">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>Name</label>
                  <input #name="ngModel" [(ngModel)]="boardType.name" [class.is-invalid]="name.invalid && name.touched" class="form-control"
                         name="name" placeholder="Enter Name"
                         required type="text" autocomplete="off">
                  <div *ngIf="name.errors && (name.invalid || name.touched)">
                    <small [class.d-none]="name.valid || name.untouched" class="text-danger">
                      *Name is required
                    </small>
                  </div>
                </div>
              </div>
              <div class="col-md-12">
                <div class="form-group">
                  <label>Charge</label>
                  <input #charge="ngModel" [(ngModel)]="boardType.charge" [class.is-invalid]="charge.invalid && charge.touched"
                         class="form-control" name="charge" autocomplete="off"
                         placeholder="Enter Charge" required type="text">
                  <div *ngIf="charge.errors && (charge.invalid || charge.touched)">
                    <small [class.d-none]="charge.valid || charge.untouched" class="text-danger">
                      *Charge is required
                    </small>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <button class="btn btn-warning pull-right mx-2" (click)="clear()">clear</button>
                <button class="btn btn-success pull-right mx-2" [disabled]="isProcessing" (click)="save()">save</button>
              </div>
            </div>
          </form>
      </div>
    </div>
  </div>
</div>
