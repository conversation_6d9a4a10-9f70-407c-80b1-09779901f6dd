<div class="card">
  <div class="card-header">
    <strong>Reservation Invoice</strong>
  </div>
  <div id="container" class="row p-0 m-0 g-0 overflow-hidden">

    <div class="col-md-12 px-3 medium-light my-2">
      <div class="card border-rounded shadow-lg my-0">
        <div class="card-body px-4 py-3">
          <div class="row theme-color-bg text-white py-3">
            <div class="col-md-6 p-0 m-0">
              <div class="row p-0 m-0">
                <div class="col-md-7">
                  <strong>Reservation code : {{reservation.reservationCode}}</strong>
                </div>
                <div class="col-md-5">
                  <strong>Room No : {{reservation.room.roomNo}}</strong>
                </div>
              </div>
            </div>
            <div class="col-md-6 p-0 m-0">
              <div class="row p-0 m-0">
                <div class="col-md-7">
                  <strong>Customer Name : {{reservation.customer.name}}</strong>
                </div>
                <div class="col-md-5">
                  <strong>To : {{reservation.to}}</strong>
                </div>
              </div>
            </div>
          </div>

          <hr>

          <div class="row mt-2">
            <div class="col-md-3">
              <div class="input-group">
                <input [(ngModel)]="keyReservationItem"
                       [typeahead]="items"
                       (typeaheadLoading)="loadItem()"
                       (typeaheadOnSelect)="setSelectedItem($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="itemName"
                       placeholder="Search Item by Name"
                       autocomplete="off"
                       size="16"
                       required
                       class="form-control"
                       name="reservationItem">
                <span class="input-group-append">
                  <button class="btn theme-color-bg text-white fa fa-search-plus" type="button"></button>
                  </span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="input-group">
                <input [(ngModel)]="keyBarcode"
                       [typeahead]="itemList"
                       (typeaheadLoading)="loadItemByBarcode()"
                       (typeaheadOnSelect)="setSelectedItem($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="barcode"
                       placeholder="Search Item by Barcode"
                       autocomplete="off"
                       size="16"
                       required
                       class="form-control"
                       name="reservationItem">
                <span class="input-group-append">
                  <button class="btn theme-color-bg text-white fa fa-search-plus" type="button"></button>
                  </span>
              </div>
            </div>
            <div class="col-md-3">
              <input type="text"
                     class="form-control"
                     autocomplete="off"
                     id="price"
                     [(ngModel)]="sPrice"
                     name="price"
                     placeholder="Price">
            </div>
            <div class="col-md-2">
              <input type="text"
                     class="form-control"
                     autocomplete="off"
                     id="qty"
                     #quantity
                     [(ngModel)]="qty"
                     name="qty"
                     placeholder="Quantity">
            </div>
            <div class="col-md-1">
              <button type="button" class="btn btn-customize" (click)="addToInvoice()">Add</button>
            </div>
          </div>

          <div class="row mt-2">
            <div class="col-md-12">
                <table class="table h-50">
                  <thead class="thead-dark font-weight-bold">
                  <td>Item</td>
                  <td>Qty</td>
                  <td>Charge</td>
                  </thead>
                  <tbody>
                  <tr *ngFor="let siRec of reservationInvoice.salesInvoiceRecords, let i = index"
                      (click)="selectRow(selectedSiRecord,i)" [class.active]="i === selectedSiRecordIndex">
                    <td>{{siRec.itemName}}</td>
                    <td>{{siRec.displayQuantity ? siRec.displayQuantity : 'N/A'}}</td>
                    <td>{{siRec.price}}</td>
                  </tr>
                  </tbody>
                </table>
            </div>
          </div>
          <hr>

          <div class="row">
            <div class="col-md-1 py-2">
              <label>Due Date</label>
            </div>
            <div class="col-md-2 py-2">
              <input type="text" name="dueDate" id="dueDate"
                     [(ngModel)]="reservationInvoice.dueDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                     class="form-control" placeholder="Due Date">
            </div>
            <div class="col-md-1 py-2">
              <label>Sub Total</label>
            </div>
            <div class="col-md-2 py-2">
              <input class="form-control" [ngModel]="reservationInvoice.subTotal | number" readonly>
            </div>
            <div class="col-md-1 py-2">
              <label>Room Charge</label>
            </div>
            <div class="col-md-2 py-2">
              <input class="form-control" [ngModel]="reservation.roomCharge | number" readonly>
            </div>
            <div class="col-md-1 py-2">
              <label>Discount</label>
            </div>
            <div class="col-md-2 py-2">
              <input class="form-control" [(ngModel)]="totalInvDiscount" (ngModelChange)="calculateTotal()">
            </div>
          </div>
          <div class="row">
            <div class="col-md-1 py-2">
              <label>Total</label>
            </div>
            <div class="col-md-2 py-2">
              <input class="form-control" [ngModel]="reservationInvoice.totalAmount | number" readonly>
            </div>
            <div class="col-md-1 py-2">
              <label>Balance</label>
            </div>
            <div class="col-md-2 py-2">
              <input class="form-control" [(ngModel)]="reservationInvoice.cashBalance" placeholder="balance"
                     (ngModelChange)="calculateBalance()"disabled>
            </div>
            <div class="col-md-1 py-2">
              <label>Payment</label>
            </div>
            <div class="col-md-2 py-2">
              <input class="form-control" [(ngModel)]="reservationInvoice.payment" placeholder="amount paying"
                     (ngModelChange)="calculateBalance()">
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <button type="button" class="btn btn-customize pull-right ml-2" (click)="save(false)"
                      [disabled]="isProcessing">
                Checkout
              </button>
              <button type="button" class="btn btn-outline-customize pull-right ml-2" (click)="save(true)"
                      [disabled]="isProcessing">
                Print
              </button>
              <button type="button" class="btn btn-danger pull-left ml-2" (click)="removeRow()">
                Remove row
              </button>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>



