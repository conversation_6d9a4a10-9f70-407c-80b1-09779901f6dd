import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Host<PERSON><PERSON>ener, NgModule, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {MetaData} from "../../../../core/model/metaData";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Customer} from '../../../trade/model/customer';
import {Room} from "../../model/room";
import {ReservationService} from "../../service/reservation.service";
import {RoomService} from "../../service/room.service";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {CustomerService} from "../../../trade/service/customer.service";
import {Reservation} from "../../model/reservation";
import {ReservationItemService} from "../../service/reservation-item.service";
import {ReservationSi} from "../../model/reservation-si";
import {ReservationSiRecord} from "../../model/reservation-si-record";
import {ReservationSiService} from "../../service/reservation-si.service";
import {ReservationInvoiceComponent} from "../reservation-invoice/reservation-invoice.component";
import {Item} from "../../../inventory/model/item";
import {ItemService} from "../../../inventory/service/item.service";
import {SalesInvoice} from "../../../trade/model/sales-invoice";
import {SalesInvoiceRecord} from "../../../trade/model/sales-invoice-record";
import {User} from "../../../../admin/model/user";
import {StockService} from "../../../inventory/service/stock.service";
import {Warehouse} from "../../../inventory/model/warehouse";
import {WarehouseService} from "../../../inventory/service/warehouse.service";
import {Stock} from "../../../inventory/model/stock";
// import {ChoosePriceComponent} from "../../../trade/component/prices/choose-price.component";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-reservation-si',
  templateUrl: './reservation-si.component.html',
  styleUrls: ['./reservation-si.component.css']
})
export class ReservationSiComponent implements OnInit {

  reservation: Reservation;
  reservationInvoice: Reservation;
  isView: false;
  modalRef: BsModalRef;
  selectedRoom: Room;
  room: Room;
  roomCharge: number;
  user: User;
  reservationForTable: Reservation;
  boardTypes: MetaData[];
  selectedRow: number;
  page;
  pageSize;
  isRoomBook: boolean;
  res: string;
  visibleBtn: boolean;
  isCollapsed: boolean;
  keyReservationItem: string;
  keyBarcode: string;
  items : Array<Item> = [];
  itemList : Array<Item> = [];
  item = new Item();
  selectedItem = new Item();
  sPrice: number;
  duplicateIndex: string;

  salesInvoice: SalesInvoice;
  salesInvoiceRecord: SalesInvoiceRecord;
  rsi: ReservationSi;
  reservationSiRec: ReservationSiRecord;
  qty: number;
  selectedSiRecordIndex: number;
  calculatePrice: number;
  removeItemRow: boolean;
  modalRefReservationInvoice: BsModalRef;
  isProcessing: boolean;
  selectedStock: Stock;
  itemStockList: Array<Stock>;
  //modalData
  customerName: string;
  customer : Customer;
  // reservation: Reservation;
  roomNo: string;
  roomName: string;
  checkOut: Date;
  checkIn: Date;
  subTotal: number;
  totalAmount: number;
  paymentMethodName: string;
  rsiRec: ReservationSiRecord;
  selectedSiRecord: SalesInvoiceRecord;
  ableToSaveInvoice: boolean;

  subscriptions: Subscription[] = [];
  resInvRecord: MetaData;
  resInvoice: MetaData;
  warehouse: Warehouse;

  invRecordItem = new Item();
  invRecordUnitPrice : number;
  invRecordActualQty : number;
  invRecordQuantity : number;
  invRecordPrice : number;
  invRecordItemCost : number;
  invRecordDisplayQuantity : number;
  invRecordDiscount : number;
  invRecordWareHouse : Warehouse;
  totalInvDiscount: number;
  priceName: string;

  @ViewChild('payment') payment: ElementRef;
  @ViewChild('quantity') quantity: ElementRef;
  @ViewChild('sellingPrice') sellingPrice: ElementRef;

  constructor(private reservationService: ReservationService,
              private modalService: BsModalService,
              private roomService: RoomService,
              private metaDataService: MetaDataService,
              public notificationService: NotificationService,
              private customerService: CustomerService,
              private reservationItemService: ReservationItemService,
              private reservationSalesInvoiceService: ReservationSiService,
              private itemService: ItemService,
              private stockService: StockService,
              private warehouseService: WarehouseService) {
  }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 20;
    this.isView = false;
    this.reservation = new Reservation();
    this.reservation.customer = new Customer();
    this.reservation.room = new Room();
    this.reservation.salesInvoiceRecords = [];
    this.reservationInvoice = new Reservation();
    this.reservationInvoice.salesInvoiceRecords = [];
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.visibleBtn = false;
    this.isCollapsed = true;
    this.loadBoardTypes();
    this.loadWarehouse();
    this.loadRecordType();
    this.loadInvoiceType();
    this.findAllReservation();
    // this.calculateSubTotal()
    this.paymentMethodName = "Cash";
    this.item = new Item();
    this.rsi = new ReservationSi();
    this.rsi.reservationSIRecord = [];
    this.rsi.dueDate = new Date();
    this.rsi.cashBalance = 0.00;
    this.totalInvDiscount = 0;
    this.rsi.payment = 0;
    this.itemStockList = [];
    this.reservationInvoice.dueDate = new Date();
  }

  loadReservation(id){
    this.reservationService.findById(id).subscribe((res: Reservation)=>{
      this.reservation = res;
      // this.calculateRoomChargeBal(this.reservation);
    })
  }

  // @HostListener('window:keydown', ['$event'])
  // keyEvent(event: KeyboardEvent) {
  //   if (event.key == 'Enter') {
  //     this.payment.nativeElement.focus();
  //   }
  // }

/*  hideModal() {
    this.modalRef.hide();
  }

  gotoTop() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }*/

  clear() {
    this.reservation = new Reservation();
    this.ngOnInit();
  }

  loadWarehouse(){
    // this.warehouse = this.user.warehouse;
  }

  loadBoardTypes() {
    this.metaDataService.findByCategory("BoarderType").subscribe((data: any) => {
      this.boardTypes = data;
    })
  }

  loadRecordType(){
    this.metaDataService.findByValueAndCategory("Reservation Invoice Record","Invoice Record Type").subscribe((data: MetaData)=>{
      this.resInvRecord = data;
    })
  }

  loadInvoiceType(){
    this.metaDataService.findByValueAndCategory("Reservation Invoice","Invoice Type").subscribe((data: MetaData)=>{
      this.resInvoice = data;
    })
  }

  findAllReservation() {
    this.isRoomBook = true;
    this.reservationService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.reservationForTable = result.content;
    });
  }

  loadItem() {
    this.itemService.findAllByNameLike(this.keyReservationItem).subscribe((data: Array<Item>) => {
      return this.items = data;
    });
  }

  loadItemByBarcode(){
    this.itemService.findAllByBarcodeLike(this.keyBarcode).subscribe((data: Array<Item>)=>{
      this.itemList = data;
    })
  }

  setSelectedItem(event) {
    this.selectedItem = new Item();
    this.selectedItem = event.item;
    this.keyReservationItem = this.selectedItem.itemName;
    this.keyBarcode = this.selectedItem.barcode;
    if (true === this.selectedItem.manageStock) {
      this.loadSelectedItemStock()
    } else {
      this.invRecordItem = new Item();
      this.invRecordItem = this.selectedItem;
      this.invRecordUnitPrice = this.selectedItem.sellingPrice;
      this.sPrice = this.invRecordUnitPrice;
      this.quantity.nativeElement.value = '';
      this.quantity.nativeElement.focus();
    }
  }

  loadSelectedItemStock(){
    this.stockService.findByItemAndWarehouse(this.selectedItem.itemCode, this.warehouse.id).subscribe((stock: Stock)=>{
      this.selectedStock = stock;
      this.itemStockList.push(stock)
      this.checkAvailability();
    })
  }

  checkAvailability(){
    if (null !== this.selectedStock){
      if (this.selectedStock.quantity === 0 || this.selectedStock.quantity < 0){
        this.notificationService.showError("Not Stock Available");
      }else {
        this.setSearchedStock();
      }
    }else {
      this.notificationService.showError("Not Stock Available")
    }
  }

  setSearchedStock(){
    this.priceName = '';
      if (this.selectedItem.prices != null && this.selectedItem.prices.length > 0) {
        // this.modalRef = this.modalService.show(ChoosePriceComponent, <ModalOptions>{class: "modal-sm"});
        this.modalRef.content.modalRef = this.modalRef;
        this.modalRef.content.prices = this.selectedItem.prices;
        this.subscriptions.push(this.modalService.onHide.subscribe(result => {
          this.priceName = this.modalRef.content.price.name;
          this.invRecordItem = this.selectedItem;
          this.invRecordUnitPrice = this.modalRef.content.price.price;
          this.invRecordActualQty = this.modalRef.content.price.quantity;
          this.sPrice = this.invRecordUnitPrice;
          this.focusQty();
          this.unsubscribe();
        }));
      } else {
        this.invRecordItem = this.selectedItem;
        this.invRecordQuantity = 1;
        this.invRecordUnitPrice = this.selectedItem.sellingPrice;
        this.invRecordPrice = this.invRecordQuantity * this.invRecordUnitPrice;
        this.invRecordItemCost = this.selectedItem.itemCost;
        this.sPrice = this.invRecordUnitPrice;
        this.focusQty();
      }
  }

  unsubscribe(){
    this.subscriptions.forEach((subscription : Subscription)=>{
      subscription.unsubscribe();
    })
    this.subscriptions = [];
  }

  focusQty(){
    this.quantity.nativeElement.focus();
  }

  selectRow(selectedSiRecord, index) {
    this.selectedSiRecordIndex = index;
    this.selectedSiRecord = selectedSiRecord;
  }

  addToInvoice() {
    if (true === this.selectedItem.manageStock){
      for (let stock of this.itemStockList){
        if (stock.itemCode === this.selectedItem.itemCode){
          if(this.selectedItem.prices != null && this.selectedItem.prices.length > 0){
            if (stock.quantity < this.qty * this.invRecordActualQty){
              this.notificationService.showError("Not Enough Stock");
            }else {
              this.invRecordItem = this.selectedItem;
              this.invRecordQuantity = this.qty;
              this.invRecordPrice = this.invRecordUnitPrice * this.invRecordQuantity;
              this.addToInvoiceRecord();
            }
          }else {
            if (stock.quantity < this.qty){
              this.notificationService.showError("Not Enough Stock");
            }else {
              this.invRecordItem = this.selectedItem;
              this.invRecordQuantity = this.qty;
              this.invRecordPrice = this.invRecordUnitPrice * this.invRecordQuantity;
              this.addToInvoiceRecord();
            }
          }
        }
        break;
      }
    }else {
      this.invRecordItem = this.selectedItem;
      this.invRecordQuantity = this.qty;
      this.invRecordPrice = this.invRecordUnitPrice * this.invRecordQuantity;
      this.addToInvoiceRecord();
    }
  }
  //
  //
  // addToInvoice() {
  //   this.reservationSiRec = new ReservationSiRecord();
  //   if (this.qty > 1) {
  //     this.calculatePrice = this.qty * this.item.sellingPrice;
  //     this.reservationSiRec.quantity = this.qty;
  //     this.reservationSiRec.resItemName = this.keyReservationItem;
  //     this.reservationSiRec.price = this.calculatePrice;
  //     this.reservationSiRec.date = new Date();
  //     this.reservationSiRec.item = this.item;
  //     this.rsi.reservationSIRecord.push(this.reservationSiRec);
  //   } else {
  //     this.reservationSiRec.quantity = this.qty;
  //     this.reservationSiRec.resItemName = this.keyReservationItem;
  //     this.reservationSiRec.price = this.item.sellingPrice;
  //     this.rsi.reservationSIRecord.push(this.reservationSiRec);
  //     console.log(this.reservationSiRec)
  //   }
  // }

  addToInvoiceRecord(){
    if (null === this.reservationInvoice.salesInvoiceRecords){
      this.reservationInvoice.salesInvoiceRecords = [];
    }
    this.salesInvoiceRecord = new SalesInvoiceRecord();
    this.salesInvoiceRecord.item = new Item();
    this.salesInvoiceRecord.item = this.invRecordItem;
    let dup = this.checkForDuplicate();
    if (dup) {
      this.addDuplicate();
    } else {
      this.salesInvoiceRecord.item = this.invRecordItem;
      this.salesInvoiceRecord.itemCode = this.invRecordItem.itemCode;
      if (!this.priceName){
        this.salesInvoiceRecord.itemName = this.invRecordItem.itemName;
      }else {
        this.salesInvoiceRecord.itemName = this.invRecordItem.itemName + ' ' + this.priceName;
      }
      this.salesInvoiceRecord.price = this.invRecordPrice;
      // this.salesInvoiceRecord.displayQuantity = this.invRecordQuantity;
      this.salesInvoiceRecord.quantity = this.invRecordActualQty * this.invRecordQuantity;
      //this.salesInvoiceRecord.actualUnitQuantity = this.invRecordActualQty;
      // this.salesInvoiceRecord.displayQuantity = this.invRecordDisplayQuantity;
      this.salesInvoiceRecord.itemCost = this.invRecordItemCost;
      this.salesInvoiceRecord.discount = this.invRecordDiscount;
      // this.salesInvoiceRecord.isManageStock = this.invRecordItem.manageStock;
      this.salesInvoiceRecord.unitPrice = this.invRecordUnitPrice;
      // this.salesInvoiceRecord.warehouse = this.invRecordWareHouse;
      // this.salesInvoiceRecord.date = new Date();
      this.reservationInvoice.salesInvoiceRecords.push(this.salesInvoiceRecord);
    }
    this.calculateSubTotal();
    this.clearAddToInvoice();
  }

  //check items to duplicate
  checkForDuplicate() {
    for (const index in this.reservationInvoice.salesInvoiceRecords) {
      if (this.invRecordItem.prices != null && this.invRecordItem.prices.length > 0){
        if (this.reservationInvoice.salesInvoiceRecords[index].itemCode === this.invRecordItem.itemCode &&
          this.reservationInvoice.salesInvoiceRecords[index].unitPrice === this.invRecordUnitPrice) {
          this.duplicateIndex = index;
          return true;
        }
      }else {
        if (this.reservationInvoice.salesInvoiceRecords[index].itemCode === this.invRecordItem.itemCode &&
          this.reservationInvoice.salesInvoiceRecords[index].unitPrice ===  this.invRecordUnitPrice) {
          this.duplicateIndex = index;
          return true;
        }
      }
    }
    return false;
  }

  //add duplicated item to salesInvoiceRecord
  addDuplicate() {
    if (this.invRecordItem.manageStock){
      for (let stock of this.itemStockList){
        if (this.invRecordItem.prices != null && this.invRecordItem.prices.length > 0){
          if (stock.quantity < (this.reservation.salesInvoiceRecords[this.duplicateIndex].actualQuantity + this.invRecordActualQty)){
            this.notificationService.showError("Not enough Stock");
          }else {
            this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].actualQuantity = this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].actualQuantity +
              this.invRecordActualQty;
            this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity = this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity +
              this.invRecordQuantity;
          }
        }else {
          if (stock.quantity < (this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity + this.invRecordQuantity)){
            this.notificationService.showError("Not enough Stock");
          }else {
            this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity = this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity +
              this.invRecordQuantity;
          }
        }
        break;
      }
    }else {
      this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity = this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity
        + this.invRecordQuantity;
    }
    this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].price = this.reservationInvoice.salesInvoiceRecords[this.duplicateIndex].quantity *
      this.invRecordUnitPrice;
    this.calculateTotal();
  }

  clearAddToInvoice() {
    this.keyReservationItem = '';
    this.keyBarcode = '';
    this.sPrice = 0;
    this.qty = 0;
  }

  calculateSubTotal() {
    this.reservationInvoice.subTotal = 0;
    this.reservationInvoice.totalAmount = 0;
    for (let index = 0; this.reservationInvoice.salesInvoiceRecords.length > index; index++) {
      this.reservationInvoice.subTotal = this.reservationInvoice.subTotal + this.reservationInvoice.salesInvoiceRecords[index].price;
    }
    this.calculateTotal();
  }

  calculateTotal() {
    if (!this.reservationInvoice.totalDiscount) {
      this.reservationInvoice.totalDiscount = 0
    }
    this.reservationInvoice.totalDiscount = (this.reservationInvoice.subTotal * this.totalInvDiscount)/100;
    this.reservationInvoice.totalAmount = this.reservationInvoice.subTotal - this.reservationInvoice.totalDiscount;
      // (undefined === this.reservationInvoice.serviceCharge ? 0 : this.reservationInvoice.serviceCharge);
      // + (undefined === this.reservationInvoice.deliveryCharge ? 0 : this.reservationInvoice.deliveryCharge);
    this.reservationInvoice.totalAmount = this.reservationInvoice.totalAmount + this.reservation.roomCharge;
    this.calculateBalance();
  }

  calculateRoomChargeBal(reservation){
    this.reservationInvoice.totalAmount = 0;
      this.reservationInvoice.totalAmount = reservation.roomCharge;
    console.log(this.reservationInvoice.totalAmount)
    console.log(reservation.roomCharge)
    this.calculateBalance();
  }

  calculateBalance() {
    if (this.reservationInvoice.payment) {
      this.reservationInvoice.cashBalance = this.reservationInvoice.payment - this.reservationInvoice.totalAmount;
      if (this.reservationInvoice.payment > 0) {
        this.ableToSaveInvoice = true;
      } else {
        this.ableToSaveInvoice = false;
      }
    }
  }

  setPaymentMethod() {
    if (this.paymentMethodName) {
      this.metaDataService.findByValueAndCategory(this.paymentMethodName, "PaymentMethod").subscribe((data: any) => {
        this.rsi.paymentMethod = new MetaData();
        this.rsi.paymentMethod = data;
      });
    } else {
      return false;
    }
  }

  ddtSubTotal() {
    this.reservationInvoice.subTotal = this.reservationInvoice.subTotal - this.reservationInvoice.price;
    this.reservationInvoice.totalAmount = this.reservationInvoice.totalAmount - this.reservationInvoice.price;
  }

  removeRow() {
    this.reservationInvoice.salesInvoiceRecords.splice(this.selectedSiRecordIndex, 1);
    this.removeItemRow = true;
    this.ddtSubTotal();
  }

  //store and parse current reservation data to backend
  setReservationData(){
    this.reservationInvoice.id = this.reservation.id;
    this.reservationInvoice.reservationCode = this.reservation.reservationCode;
  }

  save(print: boolean) {
    this.isProcessing = true;
    this.setReservationData();
    this.reservationService.saveInvoice(this.reservationInvoice).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        if (print) {
          this.modalRefReservationInvoice = this.modalService.show(ReservationInvoiceComponent, <ModalOptions>{class: 'modal-sm'});
          this.modalRefReservationInvoice.content.invoiceNo = result.data;
          this.modalRefReservationInvoice.content.findReservationSalesInvoice();
          this.modalRefReservationInvoice.content.findInvoice(result.data);
        }
        if (!this.rsi.directMode)
          this.modalRef.hide();
        this.ngOnInit();
        this.isProcessing = false;
      } else {
        this.notificationService.showError(result.message);
        this.isProcessing = false;
      }
      this.ngOnInit();
    })
    this.isProcessing = false;
  }
}
