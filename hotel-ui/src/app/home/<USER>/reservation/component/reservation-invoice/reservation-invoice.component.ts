import { Component, OnInit } from '@angular/core';
import {Company} from "../../../../core/model/company";
import {CompanyService} from "../../../../core/service/company.service";
import {DomSanitizer} from "@angular/platform-browser";
import {ReservationSi} from "../../model/reservation-si";
import {ReservationSiService} from "../../service/reservation-si.service";
import {Customer} from "../../../trade/model/customer";
import {Reservation} from "../../model/reservation";
import {ReservationService} from "../../service/reservation.service";
import {Room} from "../../model/room";

@Component({
  selector: 'app-reservation-invoice',
  templateUrl: './reservation-invoice.component.html',
  styleUrls: ['./reservation-invoice.component.css']
})
export class ReservationInvoiceComponent implements OnInit {

  company: Company;
  imageFile: any;
  invoiceNo: string;
  invoice: Reservation;
  date: Date;
  paymentStatus: string;
  user: string;
  roomName: string;
  roomNo: string;
  roomCharge: number;
  checkOut: Date;
  checkIn: Date;
  selectedReservation: Reservation;
  reservationId: string;
  roomId: string;

  constructor( private companyService: CompanyService,
               private sanitizer: DomSanitizer,
               private reservationSiService: ReservationSiService,
               private reservationService: ReservationService) { }

  ngOnInit(): void {
    this.invoice = new Reservation();
    this.invoice.room = new Room();
    this.invoice.customer = new Customer();
    this.company = new Company();
    this.date = new Date();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user.firstName;
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
      this.imageFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + this.company.logo);
    });
  }

  findSelectedReservation() {
    this.reservationService.findReservation(this.reservationId, this.roomId).subscribe((res: Reservation) => {
      this.selectedReservation = res;
      this.roomName = this.selectedReservation.room.roomName;
      this.roomNo = this.selectedReservation.room.roomNo;
      this.roomCharge = this.selectedReservation.room.roomCharge;
      this.checkIn = this.selectedReservation.from;
      this.checkOut = this.selectedReservation.to;
    })
  }

  findReservationSalesInvoice() {
    this.findCompany();
    this.findSelectedReservation();
    this.reservationSiService.findAllByInvoiceNo(this.invoiceNo).subscribe((result: Reservation) => {
      console.log(result);
      this.invoice = result;
      this.date = new Date(this.invoice.dueDate);
      if (this.invoice.invoiceStatus.value == 'Paid') {
        this.paymentStatus = 'Cash';
      } else {
        this.paymentStatus = 'Credit';
      }
    })
  }

  findInvoice(invoiceNo){
    this.reservationService.findReservationInvoiceByInvNo(invoiceNo).subscribe((invoice: Reservation)=>{
      this.invoice = invoice;
      console.log(invoice)
    });
  }

}
