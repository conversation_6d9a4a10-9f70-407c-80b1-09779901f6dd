<div id="print-section" style="padding: 20px; font-family: <PERSON>erdana; width: 78mm; height: auto">

  <address style="text-align: center; padding-top: 0px">
    <p style="font-weight: bold; font-size: 0.9rem; padding: 0px;
    margin-bottom: 10px; font-family: <PERSON>erdana;  font-style: normal;">{{company.name}}</p>
    <p
      style="font-size: 0.8rem; margin-bottom: 10px; font-family: Verdana; font-style: normal;">{{company.fullAddress}}</p>
    <p
      style="font-size: 0.8rem; margin-bottom: 10px; font-family: Verdana; font-style: normal;">{{company.telephone1 + ' / ' + company.telephone2}}</p>
    <!--    <p style="font-size: 0.8rem">{{company.email}}</p>-->
  </address>

  <div style="width: 100%">
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 10px; margin-bottom: 10px; ">
    <table style="width: 100%">
      <td style="font-size: 0.7rem;text-align: left">{{date | date:'dd/mm/yyyy, hh:mm aa'}}</td>
      <td style="font-size: 0.7rem;text-align: right">{{invoice.invoiceNo}}</td>
    </table>
    <table style="width: 100%">
      <tr>
        <td style="font-size: 0.7rem; text-align: left">Usr : {{user}}</td>
        <td style="font-size: 0.7rem; text-align: right">Cust :{{invoice.customer.name}}</td>
      </tr>
    </table>    <table style="width: 100%">
      <tr>
        <td style="font-size: 0.7rem; text-align: left">Room : {{invoice.room.roomName}}</td>
        <td style="font-size: 0.7rem; text-align: right">Room Charge :{{invoice.roomCharge}}</td>
      </tr>
    <tr>
      <td style="font-size: 0.7rem; text-align: left">From : {{invoice.from}}</td>
      <td style="font-size: 0.7rem; text-align: right">To :{{invoice.to}}</td>
    </tr>
    </table>
  </div>

  <div>
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 10px; margin-top: 0px;">
    <table style="width: 100%; margin: 0.3rem; font-size: 0.6rem; font-family: Verdana  ">
      <thead>
      <tr>
        <th style="padding: 0.4rem">#</th>
        <th style="padding: 0.4rem">Item Name</th>
        <th style="padding: 0.4rem">Qty</th>
        <th style="padding: 0.4rem">Price</th>
        <th style="padding: 0.4rem">Amount</th>
      </tr>
      </thead>
      <tbody style="font-size: 0.6rem">
      <tr *ngFor="let rec of invoice.salesInvoiceRecords; index as i">
        <td style="text-align: left">{{i + 1}}</td>
        <td style="padding: 0.4rem; text-align: left">{{rec.itemName}}</td>
        <td style="padding: 0.4rem;text-align: left">{{rec.displayQuantity}}</td>
        <td style="padding: 0.4rem;text-align: left">{{rec.unitPrice | number : '1.2-2'}}</td>
        <td style="padding: 0.4rem; text-align: left">{{rec.price | number : '1.2-2'}}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <hr style="background-color: #fff; border-top: 2px dashed #000000;">

  <div style="margin-top: 0px; width: 100%">
    <div style="display: grid; grid-template-columns: 6fr 4fr; grid-template-rows: 1fr">
      <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
        <li style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">Sub Total</li>
        <li style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">Discount</li>
        <li style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">Total</li>
        <li style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">Cash</li>
        <li style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">Balance</li>
      </ul>
      <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
        <li
          style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">{{invoice.subTotal | number : '1.2-2'}}</li>
        <li
          style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">{{invoice.totalDiscount | number : '1.2-2'}}</li>
        <li
          style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">{{invoice.totalAmount | number : '1.2-2'}}</li>
        <li
          style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">{{invoice.payment | number : '1.2-2'}}</li>
        <li
          style="font-size: 0.7rem; margin-bottom: 5px; font-family: Verdana; font-weight: bold;">{{invoice.cashBalance | number : '1.2-2'}}</li>
      </ul>
    </div>
  </div>

  <p style="text-align: center; font-style: normal; font-size: 0.8rem;
            font-family: Verdana; font-weight: bold; margin-bottom: 5px;">Thank You. Come Again.</p>
  <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 0px; margin-top: 0px;">
  <p style="text-align: center; font-style: normal; font-size: 0.6rem;  margin-top: 1px;
            font-family: Verdana; font-weight: bold; margin-bottom: 2px;">Software by S-OUT solutions</p>
  <p style="text-align: center; font-style: normal; font-size: 0.6rem;  margin-top: 2px;
            font-family: Verdana; font-weight: bold;">071 97 98 99 9</p>

</div>

<div>
  <button class="btn btn-primary pull-right m-4" printSectionId="print-section" #printBtn
          ngxPrint="useExistingCss">
    Print
  </button>
</div>
