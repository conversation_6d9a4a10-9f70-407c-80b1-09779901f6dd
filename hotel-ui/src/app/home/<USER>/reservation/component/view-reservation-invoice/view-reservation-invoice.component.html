<div class="card">
  <div class="card-header bg-info">
    <div class="row">
      <div class="col-md-7">
        <h3 class="py-5">Invoice</h3>
      </div>
      <div style="text-align:right" class="col-md-5 pull-right py-2">
        <strong class="mb-1">{{company.name}}</strong><br>
        <label>{{company.fullAddress}}</label>
        <label>{{company.telephone1 + '/' + company.telephone2}}</label>
        <label>{{company.email}}</label>
      </div>
    </div>
  </div>

  <div class="card-body px-4 py-3">
    <div class="row">
      <div class="col-md-5 align-items-center ml-3 mt-3">
        <strong>Invoice #{{invoice.invoiceNo}}</strong><br>
        <label>{{date | date:'medium': '+530'}}</label><br>
        <label><span>Cashier : </span> {{user}}</label><br>
        <label><span>Payment Type: </span> {{paymentStatus}}</label>
      </div>
      <div class="col-md-3 py-3">
        <label><span>Guest Name : </span>{{invoice.customerName}}</label><br>
        <label><span>Address : </span>{{invoice.customer.address}}</label><br>
        <label><span>Contact : </span>{{invoice.customer.telephone1}}</label><br>
      </div>
      <div class="col-md-3 py-3 ">
        <label><span>Room No : </span>{{roomNo}}</label><br>
        <label><span>Check In : </span>{{checkIn}}</label><br>
        <label><span>Check Out : </span>{{checkOut}}</label><br>
      </div>
    </div>

    <div class="row mt-2">
      <div class="col-md-12">
        <table class="table">
          <thead class="thead-dark font-weight-bold">
          <tr>
            <th>ID</th>
            <th>Item</th>
            <th>Quantity</th>
            <th>Total</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>{{0}}</td>
            <td>{{roomName}}</td>
            <td>{{" - "}}</td>
            <td>{{roomCharge}}</td>
          </tr>
          <tr *ngFor="let rec of invoice.reservationSIRecord; index as i">
            <td>{{i + 1}}</td>
            <td>{{rec.resItemName}}</td>
            <td>{{rec.quantity ? rec.quantity : " - "}}</td>
            <td>{{rec.price | number : '1.2-2'}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="row">
              <div class="col-md-2 bg-light py-3">
                <strong>Sub Total</strong><br>
                <label>{{invoice.subTotal | number : '1.2-2'}}</label>
              </div>
              <div class="col-md-2 bg-light py-3">
                <strong>Discount</strong><br>
                <label>{{invoice.totalDiscount | number : '1.2-2'}}</label>
              </div>
              <div class="col-md-2 bg-light py-3">
                <strong>Grand Total</strong><br>
                <label>{{invoice.totalAmount | number : '1.2-2'}}</label>
              </div>
              <div class="col-md-2 bg-light py-3">
                <strong>Paid Amount</strong><br>
                <label>{{invoice.payment | number : '1.2-2'}}</label>
              </div>

      <div class="col-md-4 bg-light py-3" style="text-align:right">
              <strong>Balance</strong><br>
              <label>{{invoice.cashBalance | number : '1.2-2'}}</label>
      </div>
    </div>

  </div>

</div>
