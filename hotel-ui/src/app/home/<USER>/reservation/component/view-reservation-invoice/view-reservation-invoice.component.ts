import { Component, OnInit } from '@angular/core';
import {Company} from "../../../../core/model/company";
import {ReservationSi} from "../../model/reservation-si";
import {Reservation} from "../../model/reservation";
import {CompanyService} from "../../../../core/service/company.service";
import {DomSanitizer} from "@angular/platform-browser";
import {ReservationSiService} from "../../service/reservation-si.service";
import {ReservationService} from "../../service/reservation.service";
import {Customer} from "../../../trade/model/customer";
import {Room} from "../../model/room";

@Component({
  selector: 'app-view-reservation-invoice',
  templateUrl: './view-reservation-invoice.component.html',
  styleUrls: ['./view-reservation-invoice.component.css']
})
export class ViewReservationInvoiceComponent implements OnInit {

  company: Company;
  imageFile: any;
  invoiceNo: string;
  invoice: ReservationSi;
  date: Date;
  paymentStatus: string;
  user: string;
  roomName: string;
  roomNo: string;
  roomCharge: number;
  checkOut: Date;
  checkIn: Date;
  selectedReservation: Reservation;
  reservationId: string;
  roomId: string;

  constructor( private companyService: CompanyService,
               private sanitizer: DomSanitizer,
               private reservationSiService: ReservationSiService,
               private reservationService: ReservationService) { }

  ngOnInit(): void {
    this.invoice = new ReservationSi();
    this.invoice.customer = new Customer();
    this.company = new Company();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user.firstName;
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
      this.imageFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + this.company.logo);
    });
  }

  findSelectedReservation() {
    this.reservationService.findReservation(this.reservationId, this.roomId).subscribe((res: Reservation) => {
      this.selectedReservation = res;
      this.roomName = this.selectedReservation.room.roomName;
      this.roomNo = this.selectedReservation.room.roomNo;
      this.roomCharge = this.selectedReservation.room.roomCharge;
      this.checkIn = this.selectedReservation.from;
      this.checkOut = this.selectedReservation.to;
    })
  }

  findReservationSalesInvoice() {
    this.findCompany();
    this.findSelectedReservation();
    console.log(this.roomId)
    console.log(this.reservationId)
    this.reservationSiService.findAllByInvoiceNo(this.invoiceNo).subscribe((result: ReservationSi) => {
      console.log(result);
      this.invoice = result;
      this.date = new Date(this.invoice.date);
      if (this.invoice.status.value == 'Paid') {
        this.paymentStatus = 'Cash';
      } else {
        this.paymentStatus = 'Credit';
      }
    })
  }

}
