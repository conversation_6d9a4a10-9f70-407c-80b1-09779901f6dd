<div class="container-fluid">
  <div class="row">
    <div class="col-md-12">
      <h2 class="component-title">Manage Rooms</h2>
      <div class="card">
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <input [(ngModel)]="keyRoom"
                       [typeahead]="rooms"
                       (typeaheadLoading)="loadRoom()"
                       (typeaheadOnSelect)="setSelectedRoom($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="roomNo"
                       placeholder="Search Room"
                       autocomplete="off"
                       size="16"
                       required
                       class="form-control"
                       name="roomName">
              </div>

              <table class="table table-striped">
                <thead>
                <tr>
                  <th>Room No</th>
                  <th>Room Name</th>
                  <th>Room Charge</th>
                  <th>Room Category</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let room of rooms;let i=index"
                    (click)="roomDetails(room,i)"
                    [class.active]="i === selectedRow">
                  <td>{{room.roomNo}}</td>
                  <td>{{room.roomName}}</td>
                  <td>{{room.roomCharge}}</td>
                  <td>{{room.roomCategory.name}}</td>
                </tr>
                </tbody>
              </table>
              <div class="row">
                <div class="col-xs-12 col-12">
                  <pagination class="pagination-sm justify-content-center"
                              [totalItems]="collectionSize"
                              [(ngModel)]="page"
                              [boundaryLinks]="true"
                              [maxSize]="10"
                              (pageChanged)="pageChanged($event)">
                  </pagination>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <form #manageRoomForm="ngForm" (ngSubmit)="saveRoom(); manageRoomForm.reset() ">
                <div class="form-group">
                  <label>Room No </label>
                  <input type="text"
                         required
                         #rmNo="ngModel"
                         [class.is-invalid]="rmNo.invalid && rmNo.touched"
                         class="form-control"
                         id="roomNo"
                         [(ngModel)]="room.roomNo"
                         name="roomNo"
                         placeholder="Room No"
                         autocomplete="off">
                  <div *ngIf="rmNo.errors && (rmNo.invalid || rmNo.touched)">
                    <small class="text-danger" [class.d-none]="rmNo.valid || rmNo.untouched">
                      *Room No is required
                    </small>
                  </div>
                </div>
                <div class="form-group">
                  <label>Room Name </label>
                  <input type="text"
                         required
                         #roomName="ngModel"
                         [class.is-invalid]="roomName.invalid && roomName.touched"
                         class="form-control"
                         [(ngModel)]="room.roomName"
                         name="roomName" id="roomName"
                         placeholder="Room Name">
                  <div *ngIf="roomName.errors && (roomName.invalid || roomName.touched)">
                    <small class="text-danger" [class.d-none]="roomName.valid || roomName.untouched">
                      *Room Name is required
                    </small>
                  </div>
                </div>
                <div class="form-group">
                  <label>Room Charge </label>
                  <input type="text"
                         required
                         #roomCharge="ngModel"
                         [class.is-invalid]="roomCharge.invalid && roomCharge.touched"
                         class="form-control"
                         [(ngModel)]="room.roomCharge"
                         name="roomCharge"
                         placeholder="Room Charge">
                  <div *ngIf="roomCharge.errors && (roomCharge.invalid || roomCharge.touched)">
                    <small class="text-danger" [class.d-none]="roomCharge.valid || roomCharge.untouched">
                      *Room Charge is required
                    </small>
                  </div>
                </div>
                <div class="form-group">
                  <label>Room Category</label>
                  <div class="input-group">
                    <input [(ngModel)]="keyRoomCategory"
                           [typeahead]="roomCategories"
                           (typeaheadLoading)="loadRoomCategories()"
                           (typeaheadOnSelect)="setSelectedRoomCategory($event)"
                           [typeaheadOptionsLimit]="7"
                           [class.is-invalid]="category.invalid && category.touched"
                           typeaheadWaitMs="1000"
                           typeaheadOptionField="name"
                           placeholder="Search Item Categories"
                           autocomplete="off"
                           size="16"
                           required
                           #category="ngModel"
                           class="form-control " name="category">
                    <span class="input-group-append">
                  <button class="btn btn-primary fa fa-plus" (click)="openModalLarge(templateRoomCategory)"
                          type="button">
                  </button>
                  </span>
                  </div>

                  <div *ngIf="category.errors && (category.invalid || category.touched)">
                    <small class="text-danger" [class.d-none]="category.valid || category.untouched">
                      *Room Category is required
                    </small>
                  </div>
                </div>
                <div class="form-check checkbox mr-2">
                  <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                         [(ngModel)]="room.active">
                  <label class="form-check-label" for="check3">Active</label>
                </div>

                <div class="text-right">
                  <button type="submit" class="btn btn-primary mr-2" [disabled]="!manageRoomForm.form.valid">Save</button>
                  <button type="button" class="btn btn-secondary" (click)="clear()">Clear</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<ng-template #templateRoomCategory>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Add Room Category</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-room-category></app-room-category>
  </div>
</ng-template>
