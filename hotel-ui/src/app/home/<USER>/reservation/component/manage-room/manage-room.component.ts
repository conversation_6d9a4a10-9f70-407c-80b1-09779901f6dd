import {Component, OnInit, TemplateRef} from '@angular/core';
import {Room} from "../../model/room";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {RoomCategory} from "../../model/room-category";
import {NotificationService} from "../../../../core/service/notification.service";
import {RoomService} from "../../service/room.service";
import {RoomCategoryService} from "../../service/room-category.service";

@Component({
  selector: 'app-manage-room',
  templateUrl: './manage-room.component.html',
  styleUrls: ['./manage-room.component.css']
})
export class ManageRoomComponent implements OnInit {

  room = new Room();
  rooms: Array<Room> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyRoom: string;
  collectionSize;
  page;
  pageSize;
  modalRef: BsModalRef;
  selectedRoomCategory: RoomCategory;
  keyRoomCategory: string;
  roomCategories: Array<RoomCategory> = [];

  constructor(private roomService : RoomService,
              private notificationService: NotificationService,
              private modalService: BsModalService,
              private roomCategoryService: RoomCategoryService) { }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 8;
    this.room = new Room();
    this.room.active = true;
    this.findAll();
  }

  openModalLarge(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {class: 'modal-lg'} as ModalOptions);
  }

  saveRoom() {
    this.roomService.save(this.room).subscribe(result => {
      this.ngOnInit();
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
      } else {
        this.notificationService.showError(result.message);
      }
    });
  }

  loadRoom() {
    this.roomService.findByRoomNo(this.keyRoom).subscribe((data: Array<Room>) => {
      return this.rooms = data;
    });
  }

  findAll() {
    this.roomService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.rooms = data.content;
      console.log(this.rooms)
      this.collectionSize = data.totalPages * 8;
    });
  }

  loadRoomCategories() {
    this.roomCategoryService.findByName(this.keyRoomCategory).subscribe((data: Array<RoomCategory>) => {
      return this.roomCategories = data;
    });
  }

  setSelectedRoomCategory(event) {
    this.selectedRoomCategory = event.item;
    this.room.roomCategory = new RoomCategory();
    this.room.roomCategory = this.selectedRoomCategory;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  roomDetails(room, index) {
    this.room = room;
    this.selectedRow = index;
    this.keyRoomCategory = this.room.roomCategory.name;
  }

  setSelectedRoom(event) {
    this.room = event.item;
  }


  clear() {
    this.selectedRoomCategory = new RoomCategory();
    this.room = new Room();
  }
  //
  // checkRoomNo(){
  //   this.roomService.checkRoomNo(this.room.roomNo).subscribe((data: boolean) => {
  //     this.roomNoAvailability = data;
  //   })
  // }
}
