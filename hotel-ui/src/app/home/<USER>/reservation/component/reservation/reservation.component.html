<div class="container-fluid">
  <div class="row">
    <div class="col-md-12">
      <h2 class="component-title">Reservations</h2>
      <div class="card">
        <div class="card-body">
          <div class="mb-3">
            <button class="btn btn-primary" (click)="openModalLarge(templateReservation)">New Reservation</button>
          </div>
          <table class="table table-striped">
            <thead>
            <tr>
              <th>Room No</th>
              <th>Room Name</th>
              <th>Customer</th>
              <th>From</th>
              <th>To</th>
              <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let reservation of reservationForTable; let i=index">
              <td>{{reservation.room.roomNo}}</td>
              <td>{{reservation.room.roomName}}</td>
              <td>{{reservation.customer.name}}</td>
              <td>{{reservation.from | date : 'shortDate'}}</td>
              <td>{{reservation.to | date : 'shortDate'}}</td>
              <td>
                <button class="btn btn-sm btn-info mr-2" (click)="openModalMedium(templateReservationDetails, reservation)">View</button>
                <button class="btn btn-sm btn-primary" (click)="addInvoice()">Invoice</button>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #templateReservation>
  <div class="modal-header">
    <h4 class="modal-title pull-left">New Reservation</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form #manageReservationForm="ngForm">
      <div class="row">
        <div class="col-12 mt-2">
          <label>Room</label>
          <div class="input-group">
            <input [(ngModel)]="keyRoom"
                   [typeahead]="rooms"
                   (typeaheadLoading)="loadRooms()"
                   (typeaheadOnSelect)="setSelectedRoom($event)"
                   [typeaheadOptionsLimit]="10"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="roomNo"
                   placeholder="Search room"
                   autocomplete="off"
                   id="appendedInputButtons" size="16"
                   class="form-control" name="room">
            <span class="input-group-append">
                  <button class="btn btn-primary fa fa-plus" (click)="openModalLarge(templateRoom)"
                          type="button"></button>
                  </span>
          </div>
        </div>

        <div class="col-12 mt-2">
          <label>Customer Name</label>
          <div class="input-group">
            <input [(ngModel)]="keyCustomerSearch"
                   [typeahead]="customerSearchList"
                   (typeaheadLoading)="searchCustomers()"
                   (typeaheadOnSelect)="setSelectedCustomer($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   placeholder="Search Customer"
                   autocomplete="off"
                   size="16"
                   class="form-control"
                   name="searchCustomer">
            <span class="input-group-append">
                  <button class="btn btn-primary fa fa-plus" (click)="newCustomer()"
                          type="button"></button>
                  </span>
          </div>
        </div>

        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mt-2">
          <div class="form-group mb-0">
            <label>NIC/SSN</label>
            <input type="text"
                   required
                   #nic="ngModel"
                   [class.is-invalid]="nic.invalid && nic.touched"
                   class="form-control"
                   autocomplete="off"
                   id="nic"
                   [(ngModel)]="customer.nicBr"
                   name="nic"
                   placeholder="NIC">
            <small class="text-danger" [class.d-none]="nic.valid || nic.untouched">
              *NIC is required
            </small>
          </div>
        </div>

        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mt-2">
          <div class="form-group mb-0">
            <label>Contact No</label>
            <input type="text"
                   required
                   #contact="ngModel"
                   autocomplete="off"
                   [class.is-invalid]="contact.invalid && contact.touched"
                   class="form-control"
                   id="contact"
                   [(ngModel)]="customer.telephone1"
                   name="contact"
                   placeholder="Contact">
            <div>
              <small class="text-danger" [class.d-none]="contact.valid || contact.untouched">
                *Contact is required
              </small>
            </div>
          </div>
        </div>

        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mt-2">
          <div class="form-group mb-0">
            <label>From</label>
            <input type="text"
                   required
                   #from="ngModel"
                   [class.is-invalid]="from.invalid && from.touched"
                   class="form-control"
                   autocomplete="off"
                   [datesDisabled]="disabledDates"
                   id="from"
                   [(ngModel)]="reservation.from"
                   bsDatepicker
                   [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                   name="from"
                   placeholder="From">
            <small class="text-danger" [class.d-none]="from.valid || from.untouched">
              *From is required
            </small>
            <small *ngIf="dateAvailability" [class.is-none]="true" class="text-danger">* This room is booked for this day
            </small>
          </div>
        </div>

        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-6 mt-2">
          <div class="form-group mb-0">
            <label>To</label>
            <input type="text"
                   required
                   #to="ngModel"
                   autocomplete="off"
                   [class.is-invalid]="to.invalid && to.touched"
                   class="form-control"
                   id="to"
                   bsDatepicker
                   [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                   [(ngModel)]="reservation.to"
                   name="to"
                   placeholder="To">
            <small class="text-danger" [class.d-none]="to.valid || to.untouched">
              *To is required
            </small>
          </div>
        </div>

        <div class="col-12 mt-2">
          <label>Board Type</label>
          <div class="input-group">
            <select class="form-control"
                    (change)="setSelectedBoardType()"
                    name="ticketTypeSelect"
                    [(ngModel)]="boardTypeId"
                    #boardTypeSelect="ngModel">
              <option [value]="undefined">- Select -</option>
              <option *ngFor="let typ of boardTypes" [value]="typ.id">
                {{typ.name}}
              </option>
            </select>
          </div>
        </div>
      </div>

      <div class="col-md-12 text-right">
        <button class="btn btn-primary mt-3 mx-2" (click)="save(manageReservationForm)"
                [disabled]="!manageReservationForm.valid||isView||epfAvailability||dateAvailability">
          Save
        </button>
        <button type="button" class="btn btn-secondary mt-3 mx-2" (click)="clear()">clear</button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #templateRoom>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Add Room</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-manage-room></app-manage-room>
  </div>
</ng-template>

<ng-template #templateViewAllRoom>
  <div class="modal-header">
    <h4 class="modal-title pull-left">View All Booked Room</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-view-all-room></app-view-all-room>
  </div>
</ng-template>

<ng-template #templateReservationDetails>
  <div class="modal-body">
    <div class="card">
      <div class="card-header">
        <strong>{{selectedReservation.room.roomName}} Details</strong>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="card-body">
        <div class="bg-light mb-2 p-2">
          <strong>Room No : {{selectedReservation.room.roomNo}}</strong> <br><br>
          <strong>Customer Name : {{selectedReservation.customer.name}}</strong> <br><br>
          <strong>Customer NIC : {{selectedReservation.customer.nicBr}}</strong> <br><br>
          <strong>Customer Contact : {{selectedReservation.customer.telephone1}} | {{selectedReservation.customer.telephone2}}</strong> <br><br>
          <strong>From : {{selectedReservation.from}}</strong> <br><br>
          <strong>To : {{selectedReservation.to}}</strong> <br><br>
        </div>
      </div>
    </div>
  </div>
</ng-template>


