import {<PERSON>mpo<PERSON>, <PERSON>ement<PERSON>ef, HostListener, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {Reservation} from "../../model/reservation";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Room} from "../../model/room";
import {MetaData} from "../../../../core/model/metaData";
import {Customer} from "../../../trade/model/customer";
import {ReservationService} from "../../service/reservation.service";
import {RoomService} from "../../service/room.service";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {CustomerService} from "../../../trade/service/customer.service";
import {NgForm} from "@angular/forms";
import {NewCustomerComponent} from "../../../trade/component/customer/new-customer/new-customer.component";
import {ReservationSiComponent} from "../reservation-si/reservation-si.component";
import {BoardType} from "../../model/board-type";
import {BoardTypeService} from "../../service/board-type.service";

@Component({
  selector: 'app-reservation',
  templateUrl: './reservation.component.html',
  styleUrls: ['./reservation.component.css']
})
export class ReservationComponent implements OnInit {

  reservation = new Reservation();
  epfAvailability = false;
  isView: false;
  modalRef: BsModalRef;
  keyRoom: string;
  rooms: Array<Room> = [];
  selectedRoom: Room;
  room: Room;
  reservationForTable: Array<Reservation>;
  boardTypeId: string;
  boardTypes: Array<BoardType>;
  keyCustomerSearch: string;
  customerSearchList: Array<Customer>;
  customer: Customer;
  selectedRow: number;
  page;
  pageSize;
  isRoomBook: boolean;
  res: string;
  visibleBtn: boolean;
  isCollapsed: boolean;
  dateAvailability = false;
  selectedReservation: Reservation;
  selectedReservationForModal: Reservation;
  resBook: string;
  reservationList: Array<Reservation> = [];

  convertFromDate: Date;
  convertToDate: Date;

  disabledDates = [];

  @ViewChild('payment') payment: ElementRef;
  @ViewChild('quantity') qty: ElementRef;
  @ViewChild('sellingPrice') sellingPrice: ElementRef;

  constructor(private reservationService: ReservationService,
              private modalService: BsModalService,
              private roomService: RoomService,
              private metaDataService: MetaDataService,
              public notificationService: NotificationService,
              private customerService: CustomerService, private boardTypeService: BoardTypeService) {
  }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 20;
    this.selectedRoom = new Room();
    this.reservation = new Reservation();
    this.isView = false;
    this.customer = new Customer();
    this.room = new Room();
    this.resBook = "Booked";
    this.visibleBtn = false;
    this.isCollapsed = true;
    this.loadBoardTypes();
    this.findAllBookedReservation();
  }
  //
  // @HostListener('window:keydown', ['$event'])
  // keyEvent(event: KeyboardEvent) {
  //   if (event.key == 'Enter') {
  //     this.payment.nativeElement.focus();
  //   }
  // }

  openModalLarge(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {class: 'modal-lg'} as ModalOptions);
  }

  openModalMedium(template: TemplateRef<Reservation>, reservation: Reservation) {
    this.modalRef = this.modalService.show(template, {class: 'modal-lg'} as ModalOptions);
    this.selectedReservation = reservation;
    this.ngOnInit();
  }

  save(form: NgForm) {
    this.reservation.active = true;
    this.reservation.roomBook = true;
    this.reservationService.saveReservation(this.reservation).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.findAllBookedReservation();
        this.clear();
        this.modalRef.hide();
      } else {
        this.notificationService.showError(result.message);
      }
    });
  }

  clear() {
    this.reservation = new Reservation();
    this.keyRoom = '';
    this.keyCustomerSearch = '';
    this.customer = new Customer();
    this.ngOnInit();
  }

  loadRooms() {
    this.roomService.findByRoomNo(this.keyRoom).subscribe((data: Array<Room>) => {
      this.rooms = data;
    });
  }

  setSelectedRoom(event) {
    this.reservation.room = event.item;
    this.selectedRoom = event.item;
    this.loadReservations();
  }

  loadBoardTypes() {
    this.boardTypeService.findAll().subscribe((data: Array<BoardType>) => {
      this.boardTypes = data;
    })
  }

  setSelectedBoardType() {
    this.reservation.boardType = new BoardType();
    this.reservation.boardType.id = this.boardTypeId;
  }


  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  setSelectedCustomer(event) {
    this.reservation.customer = event.item;
    this.customer = event.item;
  }

  newCustomer() {
    this.modalRef = this.modalService.show(NewCustomerComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.isModal = true;
    this.modalRef.content.modalRef = this.modalRef;
  }

  findAllBookedReservation() {
    this.isRoomBook = true;
    this.reservationService.findAllPendingReservations().subscribe((result: Array<Reservation>) => {
      return this.reservationForTable = result;
    })
  }

  selectRecord(reservation, index) {
    this.reservationService.findOneById(reservation.id).subscribe((res: Reservation) => {
      this.selectedReservationForModal = res;
    })
    this.selectedRow = index;
  }

  addInvoice() {
    if (null != this.selectedReservationForModal.id) {
      this.modalRef = this.modalService.show(ReservationSiComponent, <ModalOptions>{class: 'modal-xl'});
      // this.modalRef.content.customerName = this.selectedReservationForModal.customer.name;
      // this.modalRef.content.roomNo = this.selectedReservationForModal.room.roomNo;
      // this.modalRef.content.roomName = this.selectedReservationForModal.room.roomName;
      // this.modalRef.content.roomCharge = this.selectedReservationForModal.room.roomCharge;
      // this.modalRef.content.rsi.subTotal = this.selectedReservationForModal.room.roomCharge;
      // this.modalRef.content.rsi.totalAmount = this.selectedReservationForModal.room.roomCharge;
      // this.modalRef.content.checkOut = this.selectedReservationForModal.to;
      // this.modalRef.content.checkIn = this.selectedReservationForModal.from;
      // this.modalRef.content.customer = this.selectedReservationForModal.customer;
      // this.modalRef.content.room = this.selectedReservationForModal.room;
      // this.modalRef.content.reservation = this.selectedReservationForModal;
      this.modalRef.content.loadReservation(this.selectedReservationForModal.id);
      this.ngOnInit();
    }
    this.ngOnInit();
  }

  loadReservations() {
    this.reservationService.findByRoomNo(this.selectedRoom.id,"Booked").subscribe((data: Array<Reservation>) => {
      this.reservationList = data;
      this.checkDate(data);
    });
  }

  checkDate(reservation: Array<Reservation>) {
    for (let res of reservation) {
      this.convertFromDate = new Date(res.from);
      this.convertToDate = new Date(res.to);
      this.disabledDates.push(this.convertFromDate);
      this.disabledDates.push(this.convertToDate);
      console.log(this.disabledDates)
    }
  }

  // checkDate(reservation: Array<Reservation>) {
  //   for (let res of reservation) {
  //     for (let d = new Date(res.from); d <= new Date(res.to); d.setDate(d.getDate() + 1)) {
  //       this.disabledDates2.push(new Date(res.from));
  //       this.disabledDates2.push(d);
  //       console.log(this.disabledDates2)
  //     }
  //   }
  // }
}
