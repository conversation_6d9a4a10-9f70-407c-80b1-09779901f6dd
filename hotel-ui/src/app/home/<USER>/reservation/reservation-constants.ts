import {environment} from '../../../../environments/environment';

export class ReservationApiConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_ROOM = ReservationApiConstants.API_URL + 'room/save';
  public static GET_ROOMS = ReservationApiConstants.API_URL + 'room/findAll';
  public static SEARCH_ROOM = ReservationApiConstants.API_URL + 'room/findByName';
  public static FIND_BY_ROOM_NAME = ReservationApiConstants.API_URL + 'room/searchByName';
  public static FIND_BY_ROOM_NO = ReservationApiConstants.API_URL + 'room/searchByRoomNo';

  public static SAVE_ROOM_CATEGORY = ReservationApiConstants.API_URL + 'roomCategory/save';
  public static GET_ROOM_CATEGORY = ReservationApiConstants.API_URL + 'roomCategory/findAll';
  public static SEARCH_ROOM_CATEGORY = ReservationApiConstants.API_URL + 'roomCategory/findByName';

  public static SAVE_RESERVATION = ReservationApiConstants.API_URL + 'reservation/saveReservation';
  public static FIND_ALL_PENDING_RESERVATIONS = ReservationApiConstants.API_URL + 'reservation/findAllPendingReservations';
  public static GET_RESERVATION = ReservationApiConstants.API_URL + 'reservation/findAll';
  public static FROM_DATE_CHECK = ReservationApiConstants.API_URL + 'reservation/checkFromDate';
  public static FIND_ONE_BY_ID = ReservationApiConstants.API_URL + 'reservation/findOneById';
  public static FIND_BY_ID = ReservationApiConstants.API_URL + 'reservation/find_byId';
  public static FIND_RESERVATION_BY_ROOM_NO = ReservationApiConstants.API_URL + 'reservation/findReservationByRoomNo';
  public static FIND_BY_STATUS = ReservationApiConstants.API_URL + 'reservation/findByStatus';
  public static FIND_RESERVATION_BY_RESERVATION_ID = ReservationApiConstants.API_URL + 'reservation/findReservationById';
  public static SAVE_RESERVATION_INVOICE = ReservationApiConstants.API_URL + 'reservation/saveInvoice';
  public static FIND_RESERVATION_INVOICE_BY_INV_NO = ReservationApiConstants.API_URL + 'reservation/findReservationInvoiceByInvNo';

  public static SAVE_RESERVATION_SALES_INVOICE = ReservationApiConstants.API_URL + 'reservationInvoice/save';
  public static FIND_ALL_BY_RESERVATION_SALES_INVOICE_NO = ReservationApiConstants.API_URL + 'reservationInvoice/searchByInvoiceNo';
  public static GET_RESERVATION_SALES_INVOICE = ReservationApiConstants.API_URL + 'reservationInvoice/findAllPages';
  public static FIND_SI_BY_CUST_NIC_BR = ReservationApiConstants.API_URL + 'reservationInvoice/findAllByCustomer';
  public static FIND_ALL_BY_INVOICE_NO = ReservationApiConstants.API_URL + 'reservationInvoice/findByInvoiceNo';

  public static FIND_BY_DATE = ReservationApiConstants.API_URL + 'reservationInvoice/findByDate';
  public static SAVE_RESERVATION_ITEM = ReservationApiConstants.API_URL + 'reservationItem/save';
  public static GET_RESERVATION_ITEM = ReservationApiConstants.API_URL + 'reservationItem/findAll';

  public static SEARCH_RESERVATION_ITEM = ReservationApiConstants.API_URL + 'reservationItem/findByReservationItemName';
  public static FIND_BOARD_TYPE_BY_NAME_LIKE = ReservationApiConstants.API_URL + 'boardType/findByNameLike';
  public static SAVE_BOARD_TYPE = ReservationApiConstants.API_URL + 'boardType/save';
  public static FIND_ALL_BOARD_TYPES_PAGEABLE = ReservationApiConstants.API_URL + 'boardType/findAllPageable';
  public static FIND_ALL_BOARD_TYPES = ReservationApiConstants.API_URL + 'boardType/findAll';
  public static FIND_BOARD_TYPE_BY_ID = ReservationApiConstants.API_URL + 'boardType/findById';

}


