import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { UserSettings } from '../model/user-settings';
import { ApiConstants } from '../admin-constants';

/**
 * Service for managing user settings
 */
@Injectable({
  providedIn: 'root'
})
export class UserSettingsService {
  private currentSettingsSubject: BehaviorSubject<UserSettings>;
  public currentSettings$: Observable<UserSettings>;

  constructor(private http: HttpClient) {
    this.currentSettingsSubject = new BehaviorSubject<UserSettings>(null);
    this.currentSettings$ = this.currentSettingsSubject.asObservable();
    this.loadCurrentUserSettings();
  }

  /**
   * Load settings for the current user
   */
  loadCurrentUserSettings(): Observable<UserSettings> {
    return this.http.get<any>(ApiConstants.GET_CURRENT_USER_SETTINGS)
      .pipe(
        map(settingsData => {
          // Create a proper UserSettings instance
          const settings = new UserSettings();

          // Copy properties from the response
          settings.id = settingsData.id;
          settings.user = settingsData.user;
          settings.settings = settingsData.settings || {};
          settings.createdDate = settingsData.createdDate;
          settings.createdBy = settingsData.createdBy;
          settings.lastModifiedDate = settingsData.lastModifiedDate;
          settings.lastModifiedBy = settingsData.lastModifiedBy;

          return settings;
        }),
        tap(settings => {
          this.currentSettingsSubject.next(settings);
        })
      );
  }

  /**
   * Get settings for a specific user
   * @param username The username
   */
  getUserSettings(username: string): Observable<UserSettings> {
    return this.http.get<any>(ApiConstants.GET_USER_SETTINGS, { params: { username } })
      .pipe(
        map(settingsData => {
          // Create a proper UserSettings instance
          const settings = new UserSettings();

          // Copy properties from the response
          settings.id = settingsData.id;
          settings.user = settingsData.user;
          settings.settings = settingsData.settings || {};
          settings.createdDate = settingsData.createdDate;
          settings.createdBy = settingsData.createdBy;
          settings.lastModifiedDate = settingsData.lastModifiedDate;
          settings.lastModifiedBy = settingsData.lastModifiedBy;

          return settings;
        })
      );
  }

  /**
   * Update a setting for the current user
   * @param key Setting key
   * @param value Setting value
   * @param enabled Whether the setting is enabled
   */
  updateSetting(key: string, value: string, enabled: boolean = true): Observable<boolean> {
    return this.http.post<boolean>(ApiConstants.UPDATE_SETTING, null, {
      params: { key, value, enabled: enabled.toString() }
    }).pipe(
      tap(() => this.loadCurrentUserSettings().subscribe())
    );
  }

  /**
   * Update a setting for a specific user (admin only)
   * @param username The username
   * @param key Setting key
   * @param value Setting value
   * @param enabled Whether the setting is enabled
   */
  updateSettingForUser(username: string, key: string, value: string, enabled: boolean = true): Observable<boolean> {
    return this.http.post<boolean>(ApiConstants.UPDATE_SETTING_FOR_USER, null, {
      params: { username, key, value, enabled: enabled.toString() }
    });
  }

  /**
   * Update multiple settings for the current user
   * @param settings Map of setting keys to values
   * @param enabled Whether the settings should be enabled
   */
  updateSettings(settings: { [key: string]: string }, enabled: boolean = true): Observable<boolean> {
    return this.http.post<boolean>(ApiConstants.UPDATE_SETTINGS, { settings, enabled })
      .pipe(
        tap(() => this.loadCurrentUserSettings().subscribe())
      );
  }

  /**
   * Update multiple settings for a specific user (admin only)
   * @param username The username
   * @param settings Map of setting keys to values
   * @param enabled Whether the settings should be enabled
   */
  updateSettingsForUser(username: string, settings: { [key: string]: string }, enabled: boolean = true): Observable<boolean> {
    return this.http.post<boolean>(ApiConstants.UPDATE_SETTINGS_FOR_USER, { username, settings, enabled });
  }

  /**
   * Enable or disable a setting for the current user
   * @param key Setting key
   * @param enabled Whether the setting should be enabled
   */
  setSettingEnabled(key: string, enabled: boolean): Observable<boolean> {
    return this.http.post<boolean>(ApiConstants.SET_SETTING_ENABLED, null, {
      params: { key, enabled: enabled.toString() }
    }).pipe(
      tap(() => this.loadCurrentUserSettings().subscribe())
    );
  }

  /**
   * Enable or disable a setting for a specific user (admin only)
   * @param username The username
   * @param key Setting key
   * @param enabled Whether the setting should be enabled
   */
  setSettingEnabledForUser(username: string, key: string, enabled: boolean): Observable<boolean> {
    return this.http.post<boolean>(ApiConstants.SET_SETTING_ENABLED_FOR_USER, null, {
      params: { username, key, enabled: enabled.toString() }
    });
  }

  /**
   * Get a setting value for the current user
   * @param key Setting key
   */
  getSettingValue(key: string): Observable<string> {
    return this.http.get<string>(ApiConstants.GET_SETTING_VALUE, { params: { key } });
  }

  /**
   * Get a setting value for a specific user (admin only)
   * @param username The username
   * @param key Setting key
   */
  getSettingValueForUser(username: string, key: string): Observable<string> {
    return this.http.get<string>(ApiConstants.GET_SETTING_VALUE_FOR_USER, { params: { username, key } });
  }

  /**
   * Check if a setting is enabled for the current user
   * @param key Setting key
   */
  isSettingEnabled(key: string): Observable<boolean> {
    return this.http.get<boolean>(ApiConstants.IS_SETTING_ENABLED, { params: { key } });
  }

  /**
   * Check if a setting is enabled for a specific user (admin only)
   * @param username The username
   * @param key Setting key
   */
  isSettingEnabledForUser(username: string, key: string): Observable<boolean> {
    return this.http.get<boolean>(ApiConstants.IS_SETTING_ENABLED_FOR_USER, { params: { username, key } });
  }

  /**
   * Get all settings for the current user
   */
  getAllSettings(): Observable<{ [key: string]: string }> {
    return this.http.get<{ [key: string]: string }>(ApiConstants.GET_ALL_SETTINGS);
  }

  /**
   * Get all settings for a specific user (admin only)
   * @param username The username
   */
  getAllSettingsForUser(username: string): Observable<{ [key: string]: string }> {
    return this.http.get<{ [key: string]: string }>(ApiConstants.GET_ALL_SETTINGS_FOR_USER, { params: { username } });
  }

  /**
   * Get current settings value
   */
  getCurrentSettings(): UserSettings | null {
    return this.currentSettingsSubject.value;
  }
}
