import { Injectable } from '@angular/core';
import { UserSettingsService } from './user-settings.service';
import { environment } from '../../../../environments/environment';
import { Observable, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { GeneralSettingsService } from './general-settings.service';

/**
 * Service to provide easy access to settings throughout the application
 * This service will check user settings first, then fall back to general settings,
 * and finally fall back to environment settings
 */
@Injectable({
  providedIn: 'root'
})
export class SettingsAccessService {
  private cachedUserSettings: { [key: string]: string } = null;
  private cachedGeneralSettings: { [key: string]: any } = {};

  constructor(
    private userSettingsService: UserSettingsService,
    private generalSettingsService: GeneralSettingsService
  ) {
    this.loadSettings();
  }

  /**
   * Load all settings from the server
   */
  private loadSettings(): void {
    // Load user settings
    this.userSettingsService.getAllSettings().subscribe(
      settings => {
        this.cachedUserSettings = settings;
      },
      error => {
        console.error('Error loading user settings:', error);
        this.cachedUserSettings = {};
      }
    );

    // Load general settings
    this.generalSettingsService.getAllSettings().subscribe(
      settings => {
        // Convert array of settings to a key-value map
        this.cachedGeneralSettings = {};
        settings.forEach(setting => {
          this.cachedGeneralSettings[setting.key] = setting.value;
        });
      },
      error => {
        console.error('Error loading general settings:', error);
        this.cachedGeneralSettings = {};
      }
    );
  }

  /**
   * Get a setting value
   * @param key Setting key
   * @param defaultValue Default value if setting not found
   * @returns Observable of the setting value
   */
  getSetting(key: string, defaultValue?: any): Observable<any> {
    // If we have cached user settings, check them first
    if (this.cachedUserSettings !== null) {
      // Check user settings first
      if (this.cachedUserSettings[key] !== undefined) {
        return of(this.cachedUserSettings[key]);
      }

      // Then check general settings
      if (this.cachedGeneralSettings[key] !== undefined) {
        return of(this.cachedGeneralSettings[key]);
      }

      // Finally fall back to environment or default
      if (environment[key] !== undefined) {
        return of(environment[key]);
      }

      return of(defaultValue);
    }

    // If we don't have cached settings, get from server
    return this.userSettingsService.getSettingValue(key).pipe(
      // If user setting exists and is enabled, use it
      switchMap(value => {
        if (value !== null) {
          return of(value);
        }

        // Otherwise try to get from general settings
        return this.generalSettingsService.getSettingByKey(key).pipe(
          map(setting => setting ? setting.value : null),
          catchError(() => of(null))
        );
      }),
      // If neither user nor general setting exists, use environment or default
      map(value => value !== null ? value : (environment[key] !== undefined ? environment[key] : defaultValue)),
      catchError(() => of(environment[key] !== undefined ? environment[key] : defaultValue))
    );
  }

  /**
   * Get the default discount mode
   * @returns Observable of the default discount mode
   */
  getDefaultDiscountMode(): Observable<string> {
    return this.getSetting('defaultDiscountMode', 'percentage');
  }



  /**
   * Get the silent printing setting
   * @returns Observable of the silent printing setting
   */
  getUseSilentPrint(): Observable<boolean> {
    return this.getSetting('useSilentPrint', false);
  }

  /**
   * Get the API URL (always from environment, not overridable)
   * @returns The API URL
   */
  getApiUrl(): string {
    return environment.apiUrl;
  }

  /**
   * Refresh settings from the server
   */
  refreshSettings(): void {
    this.loadSettings();
  }

  /**
   * Get a setting value synchronously (from cache only)
   * @param key Setting key
   * @param defaultValue Default value if setting not found
   * @returns The setting value or default
   */
  getSettingSync(key: string, defaultValue?: any): any {
    // Check user settings first
    if (this.cachedUserSettings && this.cachedUserSettings[key] !== undefined) {
      return this.cachedUserSettings[key];
    }

    // Then check general settings
    if (this.cachedGeneralSettings && this.cachedGeneralSettings[key] !== undefined) {
      return this.cachedGeneralSettings[key];
    }

    // Finally fall back to environment or default
    return environment[key] !== undefined ? environment[key] : defaultValue;
  }
}
