import {Injectable} from '@angular/core';

import {ToastrService} from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor(private toastrService: ToastrService) {
  }

  /**
   * Show error notification
   * @param message - String message or response object
   */
  public showError(message: any): void {
    const errorMessage = this.extractMessage(message);
    this.toastrService.error(errorMessage, 'Error');
  }

  /**
   * Show success notification
   * @param message - String message or response object
   */
  public showSuccess(message: any): void {
    const successMessage = this.extractMessage(message);
    this.toastrService.success(successMessage, 'Success');
  }

  /**
   * Show warning notification
   * @param message - String message or response object
   */
  public showWarning(message: any): void {
    const warningMessage = this.extractMessage(message);
    this.toastrService.warning(warningMessage, 'Warning');
  }

  /**
   * Show info notification
   * @param message - String message or response object
   */
  public showInfo(message: any): void {
    const infoMessage = this.extractMessage(message);
    this.toastrService.info(infoMessage, 'Info');
  }

  /**
   * Handle response object and show appropriate notification
   * @param response - Backend response object
   * @param defaultSuccessMessage - Default message for successful operations
   * @param defaultErrorMessage - Default message for failed operations
   */
  public handleResponse(response: any, defaultSuccessMessage: string = 'Operation completed successfully', defaultErrorMessage: string = 'Operation failed'): void {
    if (!response) {
      this.showError(defaultErrorMessage);
      return;
    }

    // Check if response indicates success
    if (this.isSuccessResponse(response)) {
      const message = this.extractMessage(response) || defaultSuccessMessage;
      this.showSuccess(message);
    } else {
      const message = this.extractMessage(response) || defaultErrorMessage;
      this.showError(message);
    }
  }

  /**
   * Extract message from various response formats
   * @param input - String, response object, or any other input
   * @returns Extracted message string
   */
  private extractMessage(input: any): string {
    if (!input) {
      return 'No message provided';
    }

    // If it's already a string, return it
    if (typeof input === 'string') {
      return input;
    }

    // If it's a boolean, convert to meaningful message
    if (typeof input === 'boolean') {
      return input ? 'Operation completed successfully' : 'Operation failed';
    }

    // If it's an object, try to extract message
    if (typeof input === 'object') {
      // Try different common message properties
      if (input.message) {
        return input.message;
      }
      if (input.msg) {
        return input.msg;
      }
      if (input.data && typeof input.data === 'string') {
        return input.data;
      }
      if (input.error) {
        return input.error;
      }
      if (input.description) {
        return input.description;
      }

      // If object has status/code information, create meaningful message
      if (input.code || input.status) {
        const code = input.code || input.status;
        if (code === 200 || code === 201) {
          return 'Operation completed successfully';
        } else if (code >= 400 && code < 500) {
          return 'Invalid request or data';
        } else if (code >= 500) {
          return 'Server error occurred';
        }
      }

      // Last resort: return JSON string (but avoid [object Object])
      try {
        return JSON.stringify(input);
      } catch (e) {
        return 'Unable to display message';
      }
    }

    // For any other type, convert to string
    return String(input);
  }

  /**
   * Determine if response indicates success
   * @param response - Response object
   * @returns True if response indicates success
   */
  private isSuccessResponse(response: any): boolean {
    if (!response) {
      return false;
    }

    // Check success property
    if (response.hasOwnProperty('success')) {
      return response.success === true;
    }

    // Check status/code properties
    if (response.code) {
      return response.code === 200 || response.code === 201;
    }
    if (response.status) {
      return response.status === 200 || response.status === 201;
    }

    // Check if it's a boolean true
    if (typeof response === 'boolean') {
      return response;
    }

    // If response exists and no explicit failure indicators, assume success
    return true;
  }

}
