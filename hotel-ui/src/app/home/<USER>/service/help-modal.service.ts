import { Injectable } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { HelpModalComponent } from '../component/help-modal/help-modal.component';

@Injectable({
  providedIn: 'root'
})
export class HelpModalService {

  constructor(private modalService: BsModalService) { }

  /**
   * Open help modal
   */
  openHelpModal(): BsModalRef {
    return this.modalService.show(HelpModalComponent, {
      class: 'modal-lg',
      backdrop: 'static',
      keyboard: true
    });
  }

  /**
   * Open help modal with specific tab
   */
  openHelpModalWithTab(tab: string): BsModalRef {
    const modalRef = this.modalService.show(HelpModalComponent, {
      class: 'modal-lg',
      backdrop: 'static',
      keyboard: true
    });

    if (modalRef.content) {
      modalRef.content.activeTab = tab;
    }

    return modalRef;
  }

  /**
   * Open FAQ section directly
   */
  openFAQ(): BsModalRef {
    return this.openHelpModalWithTab('faq');
  }

  /**
   * Open video tutorials section
   */
  openTutorials(): BsModalRef {
    return this.openHelpModalWithTab('tutorials');
  }

  /**
   * Open contact support section
   */
  openContactSupport(): BsModalRef {
    return this.openHelpModalWithTab('contact');
  }
}