<div class="app-body min-vh-100 d-flex align-items-center bg-light">
  <main class="main w-100">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
          <div class="card shadow-sm">
            <div class="card-body p-4">
              <!-- Logo -->
              <div class="text-center mb-4">
                <img src="../../../../assets/img/brand/system-logo.png" class="img-fluid" width="80" height="80" alt="System Logo">
              </div>

              <!-- Title -->
              <h2 class="text-center mb-3">{{ 'LOGIN.TITLE' | translate }}</h2>
              <p class="text-center text-muted mb-4">{{ 'LOGIN.SUBTITLE' | translate }}</p>

              <!-- Login Form -->
              <form #loginForm="ngForm" (ngSubmit)="login()">
                <!-- Username Input -->
                <div class="form-group mb-3">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="fa fa-user"></i></span>
                    </div>
                    <input type="text" class="form-control" placeholder="{{ 'LOGIN.USERNAME' | translate }}" autocomplete="username"
                           [(ngModel)]="username" name="username" required>
                  </div>
                </div>

                <!-- Password Input -->
                <div class="form-group mb-4">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text"><i class="fa fa-lock"></i></span>
                    </div>
                    <input type="password" class="form-control" placeholder="{{ 'LOGIN.PASSWORD' | translate }}" autocomplete="current-password"
                           [(ngModel)]="password" required name="password">
                  </div>
                </div>

                <!-- Login Button -->
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary btn-block">{{ 'LOGIN.LOGIN_BUTTON' | translate }}</button>
                </div>
              </form>
            </div>

            <!-- Footer -->
            <div class="card-footer text-center bg-transparent py-3">
              <h6 class="mb-0">{{ 'LOGIN.FOOTER' | translate }} <span class="font-weight-bold">{{ 'LOGIN.COMPANY' | translate }}</span></h6>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
