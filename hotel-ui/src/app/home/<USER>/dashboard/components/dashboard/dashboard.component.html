<div class="container-fluid">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="component-title mb-0">Dashboard - {{ getPeriodDisplayName() }}</h2>
    <button class="btn btn-outline-primary btn-sm" (click)="refreshDashboard()" [disabled]="loading">
      <i class="fa fa-refresh" [class.fa-spin]="loading"></i> Refresh
    </button>
  </div>

  <!-- Period Selection -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="form-group">
        <label>Time Period:</label>
        <select class="form-control" [(ngModel)]="selectedPeriod" (change)="onPeriodChange()">
          <option value="today">Today</option>
          <option value="thisWeek">This Week</option>
          <option value="thisMonth">This Month</option>
          <option value="last7Days">Last 7 Days</option>
          <option value="last30Days">Last 30 Days</option>
          <option value="custom">Custom Range</option>
        </select>
      </div>
    </div>

    <!-- Custom Date Range -->
    <div class="col-md-6" *ngIf="customDateRange">
      <div class="row">
        <div class="col-md-5">
          <div class="form-group">
            <label>Start Date:</label>
            <input type="date" class="form-control" [(ngModel)]="startDate">
          </div>
        </div>
        <div class="col-md-5">
          <div class="form-group">
            <label>End Date:</label>
            <input type="date" class="form-control" [(ngModel)]="endDate">
          </div>
        </div>
        <div class="col-md-2">
          <div class="form-group">
            <label>&nbsp;</label>
            <button class="btn btn-primary btn-block" (click)="onCustomDateSearch()">Search</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p class="mt-2">Loading dashboard data...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading">

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
      <!-- Sales KPI -->
      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                  Total Sales
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ formatCurrency(salesSummary.dailySales || salesSummary.totalSales || 0) }}
                </div>
              </div>
              <div class="col-auto">
                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Profit KPI -->
      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                  Total Profit
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ formatCurrency(salesSummary.dailyProfit || salesSummary.totalProfit || 0) }}
                </div>
              </div>
              <div class="col-auto">
                <i class="fas fa-chart-line fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Invoices KPI -->
      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-info shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                  Total Invoices
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ formatNumber(salesSummary.dailyInvoiceCount || salesSummary.totalInvoices || 0) }}
                </div>
              </div>
              <div class="col-auto">
                <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stock Value KPI -->
      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
          <div class="card-body">
            <div class="row no-gutters align-items-center">
              <div class="col mr-2">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                  Stock Value
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                  {{ formatCurrency(stockSummary.totalStockValue || 0) }}
                </div>
              </div>
              <div class="col-auto">
                <i class="fas fa-boxes fa-2x text-gray-300"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="row">

      <!-- Sales Summary -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Sales Summary</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-sm-6 mb-2">
                <strong>Total Sales:</strong><br>
                <span class="text-success">{{ formatCurrency(salesSummary.dailySales || salesSummary.totalSales || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Total Profit:</strong><br>
                <span class="text-success">{{ formatCurrency(salesSummary.dailyProfit || salesSummary.totalProfit || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Invoice Count:</strong><br>
                <span class="text-info">{{ formatNumber(salesSummary.dailyInvoiceCount || salesSummary.totalInvoices || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Average Invoice:</strong><br>
                <span class="text-info">{{ formatCurrency(salesSummary.averageInvoiceValue || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Profit Margin:</strong><br>
                <span class="text-warning">{{ getProfitMargin().toFixed(2) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stock Summary -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Stock Summary</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-sm-6 mb-2">
                <strong>Stock Value:</strong><br>
                <span class="text-success">{{ formatCurrency(stockSummary.totalStockValue || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Stock Cost:</strong><br>
                <span class="text-danger">{{ formatCurrency(stockSummary.totalStockCost || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Total Items:</strong><br>
                <span class="text-info">{{ formatNumber(stockSummary.totalStockItems || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Total Quantity:</strong><br>
                <span class="text-info">{{ formatNumber(stockSummary.totalStockQuantity || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Estimated Profit:</strong><br>
                <span class="text-success">{{ formatCurrency(stockSummary.estimatedProfit || 0) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pending Bills Summary -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Pending Bills</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-sm-6 mb-2">
                <strong>Total Amount:</strong><br>
                <span class="text-warning">{{ formatCurrency(pendingBillsSummary.totalPendingBills || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Bill Count:</strong><br>
                <span class="text-warning">{{ formatNumber(pendingBillsSummary.pendingBillCount || 0) }}</span>
              </div>
              <div class="col-sm-12 mb-2">
                <strong>Oldest Bill Age:</strong><br>
                <span class="text-danger">{{ formatNumber(pendingBillsSummary.oldestPendingBillAge || 0) }} days</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cheques Summary -->
      <div class="col-lg-6 mb-4">
        <div class="card shadow">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Cheques Summary</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-sm-6 mb-2">
                <strong>Pending Cheques:</strong><br>
                <span class="text-warning">{{ formatCurrency(chequeSummary.totalPendingCheques || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Pending Count:</strong><br>
                <span class="text-warning">{{ formatNumber(chequeSummary.pendingChequeCount || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Deposited Cheques:</strong><br>
                <span class="text-success">{{ formatCurrency(chequeSummary.totalDepositedCheques || 0) }}</span>
              </div>
              <div class="col-sm-6 mb-2">
                <strong>Deposited Count:</strong><br>
                <span class="text-success">{{ formatNumber(chequeSummary.depositedChequeCount || 0) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cash Flow Summary -->
      <div class="col-lg-12 mb-4">
        <div class="card shadow">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Cash Flow Summary</h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-lg-4 mb-2">
                <strong>Total Income:</strong><br>
                <span class="text-success">{{ formatCurrency(cashFlowSummary.totalIncome || 0) }}</span>
              </div>
              <div class="col-lg-4 mb-2">
                <strong>Total Expense:</strong><br>
                <span class="text-danger">{{ formatCurrency(cashFlowSummary.totalExpense || 0) }}</span>
              </div>
              <div class="col-lg-4 mb-2">
                <strong>Net Cash Flow:</strong><br>
                <span [class]="(cashFlowSummary.netCashFlow || 0) >= 0 ? 'text-success' : 'text-danger'">
                  {{ formatCurrency(cashFlowSummary.netCashFlow || 0) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="row" *ngIf="showCharts">
        <div class="col-12 mb-4">
          <div class="card shadow">
            <div class="card-header py-3">
              <h6 class="m-0 font-weight-bold text-primary">Business Trends - {{ getPeriodDisplayName() }}</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Sales Trend Chart -->
                <div class="col-lg-6 mb-4">
                  <div class="chart-container">
                    <canvas #salesChart width="400" height="300"></canvas>
                  </div>
                </div>

                <!-- Profit Trend Chart -->
                <div class="col-lg-6 mb-4">
                  <div class="chart-container">
                    <canvas #profitChart width="400" height="300"></canvas>
                  </div>
                </div>

                <!-- Stock Value Trend Chart -->
                <div class="col-lg-12 mb-4">
                  <div class="chart-container">
                    <canvas #stockValueChart width="800" height="300"></canvas>
                  </div>
                </div>

                <!-- Cash Flow Trend Chart -->
                <div class="col-lg-12 mb-4">
                  <div class="chart-container">
                    <canvas #cashFlowChart width="800" height="300"></canvas>
                  </div>
                </div>
              </div>

              <!-- Chart Info -->
              <div class="row">
                <div class="col-12">
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Chart Information:</strong> These charts show daily trends for the selected period.
                    Charts are available for periods with multiple days (Last 7 Days, Last 30 Days, This Month, Custom Range).
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
