import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DashboardSummary } from '../model/dashboard-summary';
import { environment } from '../../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  private baseUrl = environment.apiUrl + 'dashboard';

  constructor(private http: HttpClient) { }

  /**
   * Get today's dashboard summary
   * @returns Observable of dashboard summary
   */
  getTodayDashboard(): Observable<DashboardSummary> {
    return this.http.get<DashboardSummary>(`${this.baseUrl}/today`);
  }

  /**
   * Get dashboard summary for a specific date
   * @param date The date in YYYY-MM-DD format
   * @returns Observable of dashboard summary
   */
  getDashboardByDate(date: string): Observable<DashboardSummary> {
    return this.http.get<DashboardSummary>(`${this.baseUrl}/date`, { params: { date } });
  }

  /**
   * Get dashboard summary for a date range
   * @param startDate Start date in YYYY-MM-DD format
   * @param endDate End date in YYYY-MM-DD format
   * @returns Observable of dashboard summary
   */
  getDashboardByDateRange(startDate: string, endDate: string): Observable<DashboardSummary> {
    return this.http.get<DashboardSummary>(`${this.baseUrl}/dateRange`, {
      params: { startDate, endDate }
    });
  }

  /**
   * Get dashboard summary for this week
   * @returns Observable of dashboard summary
   */
  getThisWeekDashboard(): Observable<DashboardSummary> {
    return this.http.get<DashboardSummary>(`${this.baseUrl}/thisWeek`);
  }

  /**
   * Get dashboard summary for this month
   * @returns Observable of dashboard summary
   */
  getThisMonthDashboard(): Observable<DashboardSummary> {
    return this.http.get<DashboardSummary>(`${this.baseUrl}/thisMonth`);
  }

  /**
   * Get dashboard summary for the last 7 days
   * @returns Observable of dashboard summary
   */
  getLast7DaysDashboard(): Observable<DashboardSummary> {
    return this.http.get<DashboardSummary>(`${this.baseUrl}/last7Days`);
  }

  /**
   * Get dashboard summary for the last 30 days
   * @returns Observable of dashboard summary
   */
  getLast30DaysDashboard(): Observable<DashboardSummary> {
    return this.http.get<DashboardSummary>(`${this.baseUrl}/last30Days`);
  }

  /**
   * Get chart data for a date range
   * @param startDate Start date in YYYY-MM-DD format
   * @param endDate End date in YYYY-MM-DD format
   * @returns Observable of daily statistics array
   */
  getChartData(startDate: string, endDate: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/chartData`, {
      params: { startDate, endDate }
    });
  }
}
