import {environment} from '../../../environments/environment';

export class ApiConstants {

  public static API_URL = environment.apiUrl;


  public static SAVE_USER = ApiConstants.API_URL + 'user/save';
  public static GET_USERS = ApiConstants.API_URL + 'user/findAll';
  public static GET_USER = ApiConstants.API_URL + 'user/findUser';
  public static DISABLE_USER = ApiConstants.API_URL + 'user/delete';
  public static SEARCH_USER = ApiConstants.API_URL + 'user/search';
  public static USER_CHECK = ApiConstants.API_URL + 'user/checkForUserName';
  public static UPDATE_USER = ApiConstants.API_URL + 'user/updateDesktopPerm';
  public static SEARCH_BY_NAME = ApiConstants.API_URL + 'user/searchByName';


  public static GET_ROLES = ApiConstants.API_URL + 'role/findAll';
  public static SAVE_ROLE = ApiConstants.API_URL + 'role/save';
  public static DELETE_ROLE = ApiConstants.API_URL + 'role/delete';
  public static SEARCH_ROLE = ApiConstants.API_URL + 'role/search';

  public static FIND_ENABLED_PERMISSIONS = ApiConstants.API_URL + 'user/findEnabledPermission';
  public static FIND_DESKTOP_PERMISSIONS = ApiConstants.API_URL + 'user/findDesktopPermissions';
  public static FIND_PERMISSION_BY_MODULE = ApiConstants.API_URL + 'user/findPermissionByModule';
  public static SAVE_DESKTOP_PERMS = ApiConstants.API_URL + 'user/saveDesktopPerms';
  public static GET_ENABLED_MODULES = ApiConstants.API_URL + 'user/getEnabledModules';
  public static GET_PERMISSION = ApiConstants.API_URL + 'user/getPermission';
  public static SEARCH_USER_BY_USERNAME = ApiConstants.API_URL + ' user/findByUsername';
  public static FIND_USERS_WITH_CASHIER_ROLE = ApiConstants.API_URL + 'user/findUsersWithCashierRole';
  public static IS_ADMIN = ApiConstants.API_URL + 'user/isAdmin';

  // User Settings API endpoints
  public static GET_CURRENT_USER_SETTINGS = ApiConstants.API_URL + 'userSettings/getCurrentUserSettings';
  public static GET_USER_SETTINGS = ApiConstants.API_URL + 'userSettings/getUserSettings';
  public static UPDATE_SETTING = ApiConstants.API_URL + 'userSettings/updateSetting';
  public static UPDATE_SETTINGS = ApiConstants.API_URL + 'userSettings/updateSettings';
  public static SET_SETTING_ENABLED = ApiConstants.API_URL + 'userSettings/setSettingEnabled';
  public static GET_SETTING_VALUE = ApiConstants.API_URL + 'userSettings/getSettingValue';
  public static IS_SETTING_ENABLED = ApiConstants.API_URL + 'userSettings/isSettingEnabled';
  public static GET_ALL_SETTINGS = ApiConstants.API_URL + 'userSettings/getAllSettings';

  // Admin User Settings API endpoints
  public static UPDATE_SETTING_FOR_USER = ApiConstants.API_URL + 'userSettings/updateSettingForUser';
  public static UPDATE_SETTINGS_FOR_USER = ApiConstants.API_URL + 'userSettings/updateSettingsForUser';
  public static SET_SETTING_ENABLED_FOR_USER = ApiConstants.API_URL + 'userSettings/setSettingEnabledForUser';
  public static GET_SETTING_VALUE_FOR_USER = ApiConstants.API_URL + 'userSettings/getSettingValueForUser';
  public static IS_SETTING_ENABLED_FOR_USER = ApiConstants.API_URL + 'userSettings/isSettingEnabledForUser';
  public static GET_ALL_SETTINGS_FOR_USER = ApiConstants.API_URL + 'userSettings/getAllSettingsForUser';

  // General Settings API endpoints
  public static SAVE_SETTING = ApiConstants.API_URL + 'settings/save';
  public static GET_ALL_GENERAL_SETTINGS = ApiConstants.API_URL + 'settings/findAll';
  public static GET_SETTINGS_BY_CATEGORY = ApiConstants.API_URL + 'settings/findByCategory';
  public static GET_SETTING_BY_KEY = ApiConstants.API_URL + 'settings/findByKey';
  public static DELETE_SETTING = ApiConstants.API_URL + 'settings/delete';

  // Route API endpoints
  public static SAVE_ROUTE = ApiConstants.API_URL + 'route/save';
  public static UPDATE_ROUTE = ApiConstants.API_URL + 'route/update';
  public static FIND_ROUTE_BY_ID = ApiConstants.API_URL + 'route/findById';
  public static FIND_ALL_ROUTES = ApiConstants.API_URL + 'route/findAll';
  public static SEARCH_ROUTES_BY_NAME = ApiConstants.API_URL + 'route/searchByName';
  public static SEARCH_ROUTES_BY_NAME_NO_PAGINATION = ApiConstants.API_URL + 'route/findByName';
  public static DELETE_ROUTE = ApiConstants.API_URL + 'route/delete';
  public static SET_ROUTE_ACTIVE = ApiConstants.API_URL + 'route/setActive';

  public static SAVE_SHOW_TABLE = ApiConstants.API_URL + 'table/save';
  public static FIND_ALL_TABLE_PAGEABLE = ApiConstants.API_URL + 'table/findAllPageable';
  public static FIND_ALL_TABLES = ApiConstants.API_URL + 'table/findAll';
  public static FIND_ALL_TABLES_BY_TABLE_NO_LIKE = ApiConstants.API_URL + 'table/searchByTableNoLike';

}
