<nav class="navbar navbar-expand-lg theme-color-bg sticky-top ml-auto">
  <div class="navbar-header">
    <a class="navbar-brand text-white" routerLink="../home/<USER>">{{ 'SITE_NAME' | translate }}</a>
    <span class="font-italic text-white small">(Version {{ appVersion }})</span>
  </div>

  <div class="navbar-right ml-auto d-flex align-items-center">
    <!-- Home Icon -->
    <div class="mr-3" title="Home" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-home cursor-pointer text-white" routerLink="/home/<USER>"></i>
    </div>

    <!-- Dashboard Icon (Admin Only) -->
    <div *ngIf="isAdmin" class="mr-3" title="{{ 'HEADER.DASHBOARD' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-tachometer-alt cursor-pointer text-white" (click)="navigateToDashboard()"></i>
    </div>

    <!-- POS (Sales) Icon (Updated to a cash register icon) -->
    <div class="mr-3" title="{{ 'HEADER.POS' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-cash-register cursor-pointer text-white" routerLink="/invoice_router" (click)="openFullscreen()"></i>
    </div>

    <!-- Logout Icon (Updated to a door exit icon) -->
    <div class="mr-3" title="{{ 'HEADER.LOGOUT' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-power-off cursor-pointer text-white" routerLink="../login"></i>
    </div>

    <!-- Permissions Menu Button -->
    <div title="All Permissions" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-chevron-down cursor-pointer text-white"
         (click)="togglePermissionsSidebar()"></i>
    </div>
  </div>
</nav>
