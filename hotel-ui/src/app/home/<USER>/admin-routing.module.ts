import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {CreateUserComponent} from './component/create-user/create-user.component';
import {UserManagementComponent} from './component/user-management/user-management.component';
import {ManageUserPermissionsComponent} from './component/user-permissions/manage-user-permissions.component';
import {BusinessInfoComponent} from "./component/business-info/business.component";
import {ExpenseTypeComponent} from "./component/expense-type/expense-type.component";
import {GeneralSettingsComponent} from "./component/general-settings/general-settings.component";
import {CostCodeSetupComponent} from "./component/cost-code-setup/cost-code-setup.component";
import {UserSettingsComponent} from "./component/user-settings/user-settings.component";
import {ManageUserSettingsComponent} from "./component/manage-user-settings/manage-user-settings.component";

const routes: Routes = [
  {path: 'new_user', component: CreateUserComponent},
  {path: 'manage_users', component: UserManagementComponent},
  {path: 'user_permissions', component: ManageUserPermissionsComponent},
  {path: 'expense_type', component: ExpenseTypeComponent},
  {path: 'company_detail', component: BusinessInfoComponent},
  {path: 'settings', component: GeneralSettingsComponent},
  {path: 'cost_code_setup', component: CostCodeSetupComponent},
  {path: 'user_settings', component: UserSettingsComponent},
  {path: 'manage_user_settings', component: ManageUserSettingsComponent},
  ];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule {
}

export const adminRouteParams = [CreateUserComponent, UserManagementComponent, ManageUserPermissionsComponent,
  BusinessInfoComponent, ExpenseTypeComponent, GeneralSettingsComponent, CostCodeSetupComponent, UserSettingsComponent,
  ManageUserSettingsComponent];
