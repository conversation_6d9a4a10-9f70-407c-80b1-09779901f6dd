import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {OrderComponent} from './component/order/order.component';
import {TablesComponent} from "./component/tables/tables.component";

import {RecipesComponent} from "./component/recipes/recipes.component";
import {PosComponent} from "./component/pos/pos.component";

const routes: Routes = [
  {
    path: 'order',
    component: OrderComponent
  },
  {
    path: 'tables',
    component: TablesComponent
  },
  {
    path: 'recipes',
    component: RecipesComponent
  },
  {
    path: 'pos',
    component: PosComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RestaurantRoutingModule {
}

export const restaurantRouteParams = [OrderComponent, TablesComponent, RecipesComponent, PosComponent];
