import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ApiConstants} from '../../inventory/inventory-constants';
import {TransferStock} from "../../inventory/model/transfer-stock";

@Injectable({
  providedIn: 'root'
})
export class StockService {

  constructor(private http: HttpClient) {
  }

  public findAll(page, pageSize) {
    return this.http.get(ApiConstants.GET_MAIN_STOCK, {params: {page: page, pageSize: pageSize}});
  }

  public findAllByItemCode(itemCode) {
    return this.http.get(ApiConstants.FIND_MAIN_STOCK_BY_ITEM_CODE, {params: {itemCode}});
  }

  addStockManual(manualStock) {
    return this.http.post<any>(ApiConstants.ADD_MAIN_STOCK_MANUAL, manualStock);
  }

  public findAllByWarehouse(warehouseId, page, pageSize) {
    return this.http.get(ApiConstants.FIND_BY_WAREHOUSE, {
      params: {
        warehouseId: warehouseId,
        page: page, pageSize: pageSize
      }
    });
  }

  adjustStock(actualQuantity, stockId, remark) {
    return this.http.get(ApiConstants.ADJUST_MAIN_STOCK, {params: {actualQuantity, stockId, remark}});
  }

  public findAllByCodeLike(search) {
    return this.http.get(ApiConstants.FIND_MAIN_STOCK_BY_ITEM_CODE, {params: {barcode: search}});
  }

  public findAllByBarcodeLike(search) {
    return this.http.get(ApiConstants.FIND_ALL_BY_BARCODE_LIKE, {params: {barcode: search}});
  }

  findAllByNameLike(search) {
    return this.http.get(ApiConstants.GET_ALL_BY_NAME_LIKE, {params: {name: search}});
  }

  save(transferStock: TransferStock) {
    return this.http.post<any>(ApiConstants.SAVE_STOCK, transferStock);
  }

  findByBarcodeAndWarehouse(item, warehouse) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BARCODE_AND_WAREHOUSE, {
      params: {
        barcode: item,
        warehouseId: warehouse
      }
    });
  }

  findAllStockByItemCategory(id) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_CATEGORY, {params: {id: id}})
  }

  findStockByItemCodeAndWarehouse(itemCode, warehouseId) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_CODE_AND_WAREHOUSE, {
      params: {
        itemCode: itemCode,
        warehouseId: warehouseId
      }
    })
  }

  findByItemAndWarehouse(itemCode, warehouseId) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_CODE_AND_WAREHOUSE, {
      params: {
        itemCode: itemCode,
        warehouseId: warehouseId
      }
    })
  }
}
