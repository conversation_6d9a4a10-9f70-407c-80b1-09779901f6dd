import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {ApiConstants} from "../restaurant-constants";

@Injectable({
  providedIn: 'root'
})
export class TableService {

  constructor(private http: HttpClient) { }

  save(table) {
    return this.http.post<any>(ApiConstants.SAVE_SHOW_TABLE, table);
  }

  findAllPagination(page, pageSize) {
    return this.http.get(ApiConstants.FIND_ALL_TABLE_PAGEABLE,
      {params: {page: page, pageSize: pageSize}});
  }

  findAll() {
    return this.http.get(ApiConstants.FIND_ALL_TABLES);
  }

  findByTableNoLike(tableNo){
    return this.http.get
    (ApiConstants.FIND_ALL_TABLES_BY_TABLE_NO_LIKE, {params: {tableNo: tableNo}})
  }
}