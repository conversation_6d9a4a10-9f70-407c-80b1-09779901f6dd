<div class="card bg-customize">
  <div class="card-body medium-light">
    <div class="row">
      <div  class="col-md-2 m-2 p-1" *ngFor="let table of tables">
        <div *ngIf="table.tableNo !== 'Not for Table' && table.available === true" class="card rounded shadow-lg" style="height: 100px" (click)="selectTable(table)">
          <div class="card-body">
            <p class="text-dark text-center">{{table.tableNo}}</p>
          </div>
        </div>
        <div *ngIf="table.tableNo === 'Not for Table'" class="card rounded shadow-lg bg-danger" style="height: 100px" (click)="selectTable(table)">
          <div class="card-body">
            <p class="text-center text-dark">{{table.tableNo}}</p>
          </div>
        </div>
      </div>
    </div>
    <!--    <div class="row">-->
    <!--      <div class="card rounded shadow-lg col-md-12">-->
    <!--        <div class="card-body">-->
    <!--          <p class="text-info text-center">All Tables Are Full!</p>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</div>
