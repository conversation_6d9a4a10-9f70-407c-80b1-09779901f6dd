import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {Table} from "../../model/table";
import {TableService} from "../../service/table.service";
import {BsModalRef} from "ngx-bootstrap/modal";
import {Order} from "../../model/order";

@Component({
  selector: 'app-select-table-modal',
  templateUrl: './select-table-modal.component.html',
  styleUrls: ['./select-table-modal.component.css']
})
export class SelectTableModalComponent implements OnInit {

  tables: Table[] = [];
  modalRef: BsModalRef;
  orders: Order[];
  disableTableSelect: boolean;

  @Output() passEntry: EventEmitter<any> = new EventEmitter();

  constructor(
    private tableService: TableService
  ) {
  }

  ngOnInit(): void {
  }

  findTables() {
    this.tableService.findAll().subscribe((data: any) => {
/*      if (this.orders[0]) {
        let orderTables = Array.from(this.orders, order => order.table);
        data.forEach(table=>{
          orderTables.forEach(orderTable =>{
            if(!this.shallowEqual(table, orderTable)){
              this.tables.push(table);
            }else{
              this.tables = [];
            }
          })
        });
        if(this.tables[0]){
          this.disableTableSelect = false;
        }else{
          this.disableTableSelect = true;
        }
        console.log(this.tables);
      // } else {*/
        this.tables = data;
      // }
    });
  }

  shallowEqual(object1, object2) {
    const keys1 = Object.keys(object1);
    const keys2 = Object.keys(object2);
    if (keys1.length !== keys2.length) {
      return false;
    }
    for (let key of keys1) {
      if (object1[key] !== object2[key]) {
        return false;
      }
    }
    return true;
  }

  selectTable(table) {
    this.passEntry.emit(table);
    this.modalRef.hide();
  }
}
