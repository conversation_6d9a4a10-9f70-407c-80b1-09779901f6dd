<div class="container-fluid px-0 p-1">
  <div class="theme-color">
    <div class="d-flex justify-content-between align-items-center header-section">
      <div>
        <h2 class="mb-0">{{ 'RESTAURANT_POS' | translate }}</h2>
      </div>
      <div class="d-none d-md-block">
        <i class="fa fa-utensils fa-2x ml-3 select-item cursor-pointer"
           title="Restaurant POS"></i>
        <i class="fa fa-list fa-2x ml-3 select-item cursor-pointer"
           (click)="showCategories()" title="Categories"></i>
        <i class="fa fa-history fa-2x ml-3 select-item cursor-pointer"
           routerLink="../home/<USER>/manage_sales_invoices" title="Sales History"></i>
        <i class="fa fa-home fa-2x ml-3 select-item cursor-pointer"
           routerLink="../home/<USER>" title="Home"></i>
      </div>
      <!-- Mobile navigation buttons -->
      <div class="d-md-none d-flex">
        <button class="btn btn-sm btn-secondary px-2 mx-1" (click)="showCategories()">
          <i class="fa fa-list"></i>
        </button>
        <button class="btn btn-sm btn-secondary px-2 mx-1" routerLink="../home/<USER>/manage_sales_invoices">
          <i class="fa fa-history"></i>
        </button>
        <button class="btn btn-sm btn-secondary px-2 mx-1" routerLink="../home/<USER>">
          <i class="fa fa-home"></i>
        </button>
      </div>
    </div>
  </div>
  <div class="content-section overflow-auto">
    <div class="row flex-grow-1">
      <div class="col-md-2 col-sm-12 d-flex flex-column p-2 border-end">
        <h4 class="text-primary">Orders</h4>
        <div class="flex-grow-1 overflow-auto">
          <ul class="list-group list-group-flush">
            <li class="list-group-item list-group-item-action"
                *ngFor="let ordr of waitingOrders; let i = index"
                (click)="selectRecord(ordr, i)" [class.active]="i === selectedRow">
              {{ordr.orderNo}}
            </li>
          </ul>
        </div>
      </div>

    <div class="col-md-7 col-sm-12 p-2">
      <div class="row">
        <div class="col-md-4">
          <input [(ngModel)]="keyBarcodeSearch"
                 [typeahead]="itemSearchList"
                 (typeaheadLoading)="searchItemsByBarcode()"
                 (typeaheadOnSelect)="setSearchedSelectedItem($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="barcode"
                 autocomplete="off"
                 class="form-control form-control-lg"
                 placeholder="Barcode"
                 name="searchItem">
        </div>
        <div class="col-md-4">
          <input [(ngModel)]="keyItemNameSearch"
                 [typeahead]="itemNameSearchList"
                 (typeaheadLoading)="searchItemsByName()"
                 (typeaheadOnSelect)="setSearchedSelectedItem($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadWaitMs="1000"
                 [typeaheadItemTemplate]="customItemTemplate"
                 typeaheadOptionField="itemName"
                 autocomplete="off"
                 class="form-control form-control-lg"
                 placeholder="Item Name"
                 name="searchItem">
        </div>
        <ng-template #customItemTemplate let-model="item">
          <span><strong>{{model.itemName}}</strong> - {{model.sellingPrice}}</span>
        </ng-template>
        <div class="col-md-2">
          <input type="number" class="form-control form-control-lg" placeholder="Price" [(ngModel)]="sPrice">
        </div>
        <div class="col-md-2">
          <input type="number" class="form-control form-control-lg" placeholder="Qty" [(ngModel)]="itemQty" (keydown.enter)="addToInvoice()">
        </div>
      </div>
      <hr>
      <div class="overflow-auto" style="height: calc(100vh - 150px);">
        <div class="row" *ngIf="isCategory">
          <div class="col-md-3" *ngFor="let category of categories">
            <div class="card text-center p-2 m-2" (click)="selectCategory(category.id)">
              {{category.name}}
            </div>
          </div>
        </div>
        <div class="row" *ngIf="!isCategory">
          <div class="col-md-3" *ngFor="let item of items">
            <div class="card text-center p-2 m-2" (click)="setItem(item)">
              <div>{{item.itemName}}</div>
              <small class="text-muted">Rs: {{item.sellingPrice | number: '1.2-2'}}</small>
            </div>
          </div>
          <div class="col-12 text-center mt-3">
            <button class="btn btn-secondary" (click)="showCategories()">Back to Categories</button>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 col-sm-12 d-flex flex-column p-2 bg-light">
      <div class="flex-grow-1 overflow-auto">
        <div *ngFor="let rec of invoice.salesInvoiceRecords; let i = index" class="d-flex align-items-center p-2 border-bottom">
          <div class="flex-grow-1">{{rec.itemName}}</div>
          <div class="px-2">
            <i class="fa fa-minus-circle cursor-pointer" (click)="decreaseQuantity(i, 'displayQuantity', 'quantity', rec)"></i>
            <span class="px-2">{{rec.displayQuantity}}</span>
            <i class="fa fa-plus-circle cursor-pointer" (click)="increaseQuantity(i, 'displayQuantity', 'quantity',rec)"></i>
          </div>
          <div class="px-2">{{rec.price | number:'1.2-2'}}</div>
          <i class="fa fa-trash text-danger cursor-pointer" (click)="remove(i)"></i>
        </div>
      </div>
      <div class="mt-auto p-2">
        <div class="d-flex justify-content-between">
          <span>Sub Total</span>
          <strong>{{invoice.subTotal | number:'1.2-2'}}</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span>Service Charge</span>
          <input type="number" class="form-control form-control-sm w-50" [(ngModel)]="invoice.serviceCharge" (ngModelChange)="calculateTotal()">
        </div>
        <hr>
        <div class="d-flex justify-content-between h4">
          <strong>Total</strong>
          <strong>{{invoice.totalAmount | number:'1.2-2'}}</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <span>Payment</span>
          <input type="number" class="form-control form-control-sm w-50" [(ngModel)]="invoice.payment" (ngModelChange)="calculateBalance()">
        </div>
        <div class="d-flex justify-content-between">
          <span>Balance</span>
          <strong>{{invoice.cashBalance | number:'1.2-2'}}</strong>
        </div>
        <div class="d-flex justify-content-around mt-2">
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="paymentMethod" id="cash" value="Cash" [(ngModel)]="paymentMethodName" [checked]="true">
            <label class="form-check-label" for="cash">Cash</label>
          </div>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="paymentMethod" id="card" value="Card" [(ngModel)]="paymentMethodName">
            <label class="form-check-label" for="card">Card</label>
          </div>
        </div>
        <div class="d-grid gap-2 mt-2">
          <button class="btn btn-info" (click)="openModalSelectTable()" [disabled]="!(invoice.salesInvoiceRecords.length > 0)">Add To Order</button>
          <button class="btn btn-secondary" (click)="printOnly()" [disabled]="!(invoice.salesInvoiceRecords.length > 0)">Print</button>
          <button class="btn btn-primary" (click)="save(true)" [disabled]="!(invoice.salesInvoiceRecords.length > 0) || !ableToSaveInvoice">Checkout</button>
        </div>
      </div>
    </div>
  </div>
</div>
