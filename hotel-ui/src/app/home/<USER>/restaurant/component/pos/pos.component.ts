import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostL<PERSON>ener, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ItemService} from "../../../inventory/service/item.service";
import {Item} from "../../../inventory/model/item";
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {SalesInvoice} from "../../../trade/model/sales-invoice";
import {SalesInvoiceRecord} from "../../../trade/model/sales-invoice-record";
import {NotificationService} from "../../../../core/service/notification.service";
import {StockService} from "../../../inventory/service/stock.service";
import {Order} from "../../model/order";
import {Table} from "../../model/table";
import {TableService} from "../../service/table.service";
import {Invoice80Component} from "../../../trade/component/invoices/invoice-80-en/invoice-80.component";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {SalesInvoiceService} from "../../../trade/service/sales-invoice.service";
import {Customer} from "../../../trade/model/customer";
import {CustomerService} from "../../../trade/service/customer.service";
import {CheckOutDoneComponent} from "../check-out-done/check-out-done.component";
import {SelectTableModalComponent} from "../select-table-modal/select-table-modal.component";
import {User} from "../../../../admin/model/user";
import {Subscription} from "rxjs";
import {OrderService} from "../../service/order.service";
import {TicketComponent} from "../ticket/ticket.component";
import {Stock} from "../../../inventory/model/stock";
import {Warehouse} from "../../../inventory/model/warehouse";
import {ItemCategoryService} from "../../../inventory/service/item-category.service";
import {SubCategoryService} from "../../../inventory/service/sub-category.service";
import {SubCategory} from "../../../inventory/model/sub-category";

@Component({
  selector: 'app-sales-invoice',
  templateUrl: './pos.component.html',
  styleUrls: ['./pos.component.css']
})
export class PosComponent implements OnInit {

  isCategory: boolean;
  isSubCategory: boolean;
  time: Date;

  categories: any[];
  subCategories: SubCategory[];
  selectedCategory: any;
  items: Item[];
  itemQty: number;
  displayQty: number;
  relativeQty: number;

  keyBarcodeSearch: string;
  itemSearchList: Array<Item> = [];

  keyItemNameSearch: string;
  itemNameSearchList: Array<Item> = [];

  item: Item = new Item();
  stock: Stock;
  selectedItemStock: Stock;
  selectedItem: Item;

  selectedRow: number;
  totalAmount: number;

  salesTypes: MetaData[];

  waitingOrders: Array<Order>;

  invoice: SalesInvoice;
  salesInvoiceRecord: SalesInvoiceRecord;
  duplicateIndex: string;
  sPrice: number;
  ableToSaveInvoice: boolean;

  order: Order;
  selectedOrder: Order;
  keyTableNo: string;
  tables: Table[];
  selectedTable: Table;
  isOrderInTable: boolean;
  isProcessing: boolean;
  modalRefInvoice: BsModalRef;
  modalRefTicket: BsModalRef;
  modalRef: BsModalRef;
  keyCustomerSearch: string;
  customerSearchList: Customer[] = [];
  isOrderSelected: boolean;
  paymentMethodName: string;
  isSalesTypeSelected: boolean;
  isTableSelected: boolean;
  isDelivery: boolean;
  numberOfSelectedItem: number;
  user: User;
  currentWarehouse: Warehouse;
  subscriptions: Subscription[] = [];
  priceName: string;
  addOneItem: boolean;

  @ViewChild('payment') payment: ElementRef;
  @ViewChild('quantity') qty: ElementRef;
  @ViewChild('sellingPrice') sellingPrice: ElementRef;
  @ViewChild('barcodeElm') barcodeElm: ElementRef;
  @ViewChild('itemName') itemName: ElementRef;

  constructor(private itemService: ItemService,
              private categoryService: ItemCategoryService,
              private subCategoryService: SubCategoryService,
              private metaDataService: MetaDataService,
              private notificationService: NotificationService,
              private stockService: StockService,
              private tableService: TableService,
              private salesInvoiceService: SalesInvoiceService,
              private orderService: OrderService,
              private modalService: BsModalService,
              private customerService: CustomerService) {
  }

  ngOnInit(): void {
    this.isCategory = true;
    this.isSubCategory = false;
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.selectedTable = new Table();
    this.loadCategories();
    this.loadSalesTypes();
    this.waitingOrders = [];
    this.ableToSaveInvoice = false;
    this.isOrderInTable = false;
    this.isProcessing = false;
    this.isOrderSelected = false;
    this.isSalesTypeSelected = false;
    this.isTableSelected = false;
    this.addOneItem = false;
    this.numberOfSelectedItem = 0;
    this.paymentMethodName = "Cash";
    this.isDelivery = false;
    this.initInvoice();
    this.initOrder();
    this.setPaymentMethod();
    this.setDefaultOrderType();
    this.findWaitingOrders();
    this.loadWareHouse();
    //this.fullScreen();
  }

  @HostListener('window:keydown', ['$event'])
  keyEvent(event: KeyboardEvent) {
    if (event.key == 'Escape') {
      this.gotoBarcode();
      this.clearAddToInvoice();
    }
  }

  fullScreen() {
    var element = document.querySelector("#container");
    element.requestFullscreen()
      .then(function () {
      })
      .catch(function (error) {
        console.log(error.message);
      });
  }

  //load warehouse
  loadWareHouse() {
    this.currentWarehouse = this.user.warehouse;
  }

  //load categories
  loadCategories() {
    this.categoryService.findAllCategories().subscribe((data: any) => {
      this.categories = data;
    });
  }

  //show categories
  showCategories() {
    this.isCategory = true;
    this.isSubCategory = false;
    this.items = [];
    this.subCategories = [];
  }

  //show subcategories
  showSubCategories() {
    this.isCategory = false;
    this.isSubCategory = true;
    this.items = [];
  }

  //load subcategories by category
  selectCategory(category) {
    this.selectedCategory = category;
    this.subCategoryService.findByCategoryId(category.id).subscribe((result: any) => {
      this.subCategories = result;
      this.isCategory = false;
      this.isSubCategory = true;
    });
  }

  //load items by subcategory
  selectSubCategory(subCategory: SubCategory) {
    this.itemService.findAllBySubCategory(subCategory).subscribe((result: any) => {
      this.items = [];
      result.forEach(item => {
        this.items.push(item);
      });
      this.isSubCategory = false;
    });
  }

  //load waiting orders
  findWaitingOrders() {
    this.orderService.findAll().subscribe((orders: Array<Order>) => {
      if (orders.length > 0) {
        this.waitingOrders = orders;
      }
    });
  }

  //search items by barcode
  searchItemsByBarcode() {
    this.itemService.findAllByBarcodeLike(this.keyBarcodeSearch).subscribe((result: Array<Item>) => {
      this.itemSearchList = result;
    });
  }

  //search item by name
  searchItemsByName() {
    this.itemService.findAllByNameLike(this.keyItemNameSearch).subscribe((result: Array<Item>) => {
      this.itemNameSearchList = result;
    });
  }

  //add selected item to sale invoice record
  setItem(item: Item) {
    this.addOneItem = true;
    this.selectedItem = new Item();
    this.selectedItem = item;
    if (true === this.selectedItem.manageStock) {
      this.loadSelectedItemStock()
    } else {
      this.addToInvoiceRecord();
    }
  }

  //Load stock by selected item
  loadSelectedItemStock() {
    this.stockService.findByItemAndWarehouse(this.selectedItem.id, this.currentWarehouse.id).subscribe((stock: Stock) => {
      this.selectedItemStock = stock;
      this.checkAvailability()
    });
  }

  //Check availability from selected item stock
  checkAvailability() {
    if (null !== this.selectedItemStock) {
      if (this.selectedItemStock.quantity === 0 || this.selectedItemStock.quantity < 0) {
        this.notificationService.showError("Not Enough Stock");
        this.keyItemNameSearch = '';
        this.keyBarcodeSearch = '';
      } else {
        this.setSearchedStock();
      }
    } else {
      this.notificationService.showError(this.selectedItem.itemName + " Not Stock Available")
    }
  }

  //set searched item stock from loaded item
  setSearchedStock() {
    this.priceName = '';
      this.sPrice = this.selectedItem.sellingPrice;
      this.relativeQty = 1;
      this.focusQty();
    }

  //unsubscribe selected item from modal
  unsubscribe() {
    this.subscriptions.forEach((subscription: Subscription) => {
      subscription.unsubscribe();
    });
    this.subscriptions = [];
  }

  //add selected item to invoice
  addToInvoice() {
    if (this.selectedItem.id && this.itemQty > 0) {
      this.displayQty = this.itemQty;
      this.itemQty = this.itemQty * this.relativeQty;
      if (true === this.selectedItem.manageStock) {
        this.stockService.findByItemAndWarehouse(this.selectedItem.id, this.currentWarehouse.id).subscribe((stock: Stock) => {
            if (stock.itemCode === this.selectedItem.itemCode) {
              if (this.selectedItem.prices != null && this.selectedItem.prices.length > 0) {
                if (stock.quantity < (this.itemQty * this.relativeQty)) {
                  this.notificationService.showError("Not Enough Stock");
                } else {
                  this.addToInvoiceRecord();
                }
              } else {
                if (stock.quantity < this.itemQty) {
                  this.notificationService.showError("Not Enough Stock");
                } else {
                  this.addToInvoiceRecord();
                }
              }
            }
          }
        );
      } else {
        this.addToInvoiceRecord();
      }
    }
  }

  //add selected item to salesInvoiceRecord
  addToInvoiceRecord() {
    this.salesInvoiceRecord = new SalesInvoiceRecord();
    this.salesInvoiceRecord.item = new Item();
    this.salesInvoiceRecord.item = this.selectedItem;
    let dup = this.checkForDuplicate();
    if (dup) {
      this.addDuplicate();
    } else {
      this.salesInvoiceRecord.itemCode = this.selectedItem.itemCode;
      if (!this.priceName) {
        this.salesInvoiceRecord.itemName = this.selectedItem.itemName;
      } else {
        this.salesInvoiceRecord.itemName = this.selectedItem.itemName + ' - ' + this.priceName;
      }
      this.salesInvoiceRecord.price = this.sPrice * this.displayQty;
      this.salesInvoiceRecord.displayQuantity = this.displayQty;
      console.log('abbbcc', this.itemQty);
      this.salesInvoiceRecord.quantity = this.itemQty;
      this.salesInvoiceRecord.itemCost = this.selectedItem.itemCost;
      this.salesInvoiceRecord.discount = 0;
      this.salesInvoiceRecord.isManageStock = this.selectedItem.manageStock;
      this.salesInvoiceRecord.unitPrice = this.selectedItem.sellingPrice;
      this.salesInvoiceRecord.warehouse = this.currentWarehouse;
      this.salesInvoiceRecord.date = new Date();
      this.invoice.salesInvoiceRecords.push(this.salesInvoiceRecord);
    }
    this.calculateSubTotal();
    this.clearAddToInvoice();
  }

  //calculate sub total
  calculateSubTotal() {
    this.invoice.subTotal = 0;
    this.invoice.totalAmount = 0;
    this.invoice.advancePayment = 0;
    for (let index = 0; this.invoice.salesInvoiceRecords.length > index; index++) {
      this.invoice.subTotal = this.invoice.subTotal + this.invoice.salesInvoiceRecords[index].price;
    }
    this.calculateTotal();
  }

  //check items to duplicate
  checkForDuplicate() {
    for (const index in this.invoice.salesInvoiceRecords) {
      if (this.selectedItem.prices != null && this.selectedItem.prices.length > 0) {
        if (this.invoice.salesInvoiceRecords[index].itemCode === this.selectedItem.itemCode &&
          this.invoice.salesInvoiceRecords[index].unitPrice === this.sPrice) {
          this.duplicateIndex = index;
          return true;
        }
      } else {
        if (this.invoice.salesInvoiceRecords[index].itemCode === this.selectedItem.itemCode &&
          this.invoice.salesInvoiceRecords[index].unitPrice === this.sPrice) {
          this.duplicateIndex = index;
          return true;
        }
      }
    }
    return false;
  }

  //add duplicated item to salesInvoiceRecord
  addDuplicate() {
    if (this.selectedItem.manageStock) {
      this.stockService.findByItemAndWarehouse(this.selectedItem.id, this.currentWarehouse.id).subscribe((stock: Stock) => {
        if (this.selectedItem.prices != null && this.selectedItem.prices.length > 0) {
          if (stock.quantity < (this.invoice.salesInvoiceRecords[this.duplicateIndex].displayQuantity +
            this.itemQty)) {
            this.notificationService.showError("Not enough Stock");
          } else {
            this.invoice.salesInvoiceRecords[this.duplicateIndex].displayQuantity = this.invoice.salesInvoiceRecords[this.duplicateIndex].displayQuantity +
              this.itemQty;
            this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity =
              this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity +
              this.itemQty;
          }
        } else {
          if (stock.quantity < (this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity + this.itemQty)) {
            this.notificationService.showError("Not enough Stock");
          } else {
            this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity =
              this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity +
              this.itemQty;
          }
        }
      });
    } else {
      this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity =
        this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity
        + this.itemQty;
    }
    this.invoice.salesInvoiceRecords[this.duplicateIndex].price =
      this.invoice.salesInvoiceRecords[this.duplicateIndex].quantity *
      this.itemQty;
    this.calculateTotal();
  }

  calculateTotal() {
    if (!this.invoice.totalDiscount) {
      this.invoice.totalDiscount = 0
    }
    this.invoice.totalAmount = this.invoice.subTotal +
      (undefined === this.invoice.serviceCharge ? 0 : this.invoice.serviceCharge);
    this.calculateBalance();
  }

  //calculate balance
  calculateBalance() {
    if (this.invoice.payment) {
      this.invoice.cashBalance = this.invoice.payment - this.invoice.totalAmount;
      if (this.invoice.payment > 0) {
        this.ableToSaveInvoice = true;
      } else {
        this.ableToSaveInvoice = false;
      }
    }
  }

  //select order from order list
  selectRecord(order: any, i: any) {
    this.selectedOrder = new Order();
    this.selectedRow = i;
    this.isOrderSelected = true;
    this.orderService.findAllByOrderId(order.id).subscribe((order: Order) => {
      this.selectedOrder = order;
      this.convertOrderToInvoice();
    });
  }

  //load sales types
  loadSalesTypes() {
    this.metaDataService.findByCategory("SalesType").subscribe((data: any) => {
      this.salesTypes = data;
    });
  }

  //set default order type
  setDefaultOrderType() {
    this.metaDataService.findByValueAndCategory('Table', 'SalesType').subscribe((salesType: MetaData) => {
      this.invoice.salesType = salesType.value;
    });
  }

  //clear added invoice
  clearAddToInvoice() {
    this.keyBarcodeSearch = '';
    this.keyItemNameSearch = '';
    this.sPrice = null;
    this.itemQty = null;
    this.selectedItem = new Item();
    this.relativeQty = 1;
    this.displayQty = 0;
    this.selectedItemStock = new Stock();
    this.gotoBarcode();
  }

  //clear all data
  clearAll() {
    this.initInvoice();
    this.selectedTable = new Table();
    this.keyItemNameSearch = "";
    this.keyTableNo = "";
    this.findWaitingOrders();
    this.setDefaultOrderType();
    this.gotoBarcode();
  }

  //add order list
  addToOrderList() {
    if (this.selectedTable && this.selectedTable.id && this.invoice.salesInvoiceRecords.length > 0) {
      this.order.salesInvoiceRecords = this.invoice.salesInvoiceRecords;
      this.order.salesType = this.invoice.salesType;
      this.order.subTotal = this.invoice.subTotal;
      this.order.payment = this.invoice.payment;
      this.order.serviceCharge = this.invoice.serviceCharge;
      this.order.table = this.selectedTable;
      this.orderService.save(this.order).subscribe((result: any) => {
        if (result.code === 200) {
          this.modalRefTicket = this.modalService.show(TicketComponent, <ModalOptions>{class: 'modal-sm'});
          this.modalRefTicket.content.modalRef = this.modalRefTicket;
          this.modalRefTicket.content.orderId = result.data;
          this.modalRefTicket.content.findOrder();
          this.clearAll();
        } else {
          this.notificationService.showError("Creating Order Failed");
        }
      });
    }
  }

  loadTables() {
    this.tableService.findByTableNoLike(this.keyTableNo).subscribe((data: any) => {
      this.tables = data;
    });
  }

  setSelectedTable(event) {
    this.selectedTable = event.item;
  }

  //save sales invoice
  save(print: boolean) {
    if (!this.isProcessing) {
      this.isProcessing = true;
      this.salesInvoiceService.save(this.invoice).subscribe((result: any) => {
        if (result.code === 200) {
          this.notificationService.showSuccess(result.message);
          if (this.invoice.convertedFromOrders) {
            this.clearAll();
          }
          if (print) {
            this.modalRefInvoice = this.modalService.show(Invoice80Component, <ModalOptions>{class: 'modal-sm'});
            this.modalRefInvoice.content.invoiceNo = result.data;
            this.modalRefInvoice.content.findInvoice();
            this.modalRefInvoice.content.modalRef = this.modalRefInvoice;
          }
          this.modalRef.hide();
          this.isProcessing = false;
          this.ngOnInit();
        } else if (result.code === 999) {
          this.notificationService.showError(result.message);
          this.isProcessing = false;
          //this.router.navigateByUrl('/home/<USER>/cashier')
        } else {
          this.notificationService.showError(result.message);
          this.isProcessing = false;
        }
      });
    }
  }

  printOnly() {
    this.modalRefInvoice = this.modalService.show(Invoice80Component, <ModalOptions>{class: 'modal-sm'});
    this.invoice.payment = 0;
    this.invoice.balance = this.invoice.subTotal;
    this.modalRefInvoice.content.invoice = this.invoice;
    this.modalRefInvoice.content.modalRef = this.modalRefInvoice;
  }

  openModalCheckOutDone() {
    this.modalRef = this.modalService.show(CheckOutDoneComponent, <ModalOptions>{class: 'modal-sm'});
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.payment = this.invoice.payment;
    this.modalRef.content.balance = this.invoice.cashBalance;
    this.modalRef.content.total = this.invoice.totalAmount;
    this.modalService.onHide.subscribe(result => {
      this.ngOnInit();
    });
  }

  //open table modal
  openModalSelectTable() {
    if (this.selectedTable && this.selectedTable.id) {
      this.addToOrderList();
    } else {
      this.modalRef = this.modalService.show(SelectTableModalComponent, <ModalOptions>{class: 'modal-lg'});
      this.modalRef.content.modalRef = this.modalRef;
      this.modalRef.content.findTables();
      this.modalRef.content.passEntry.subscribe((data) => {
        this.selectedTable = data;
      });
      this.modalService.onHide.subscribe(result => {
        this.addToOrderList();
      });
    }
  }

  //search customer
  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  //set selected customer
  setSelectedCustomer(event: any) {
    this.invoice.customerNo = event.item.customerNo;
    this.invoice.customerName = event.item.name;
  }

  //change value of sale invoice qty
  changeValue(i: any, quantity: string, displayQuantity: string, event: any, record: SalesInvoiceRecord) {
    this.selectedItem = new Item();
    this.selectedItem = record.item;
    if (this.selectedItem.manageStock) {
      this.stockService.findByItemAndWarehouse(this.selectedItem.id, this.currentWarehouse.id).subscribe((stock: Stock) => {
        if (stock.itemCode === this.selectedItem.itemCode) {
          if (this.selectedItem.prices != null && this.selectedItem.prices.length > 0) {
            if (stock.quantity <= this.invoice.salesInvoiceRecords[i][quantity] * record.quantity) {
              this.notificationService.showError("Not enough stock");
            } else {
              this.invoice.salesInvoiceRecords[i][quantity] = event.target.textContent;
              this.invoice.salesInvoiceRecords[i][displayQuantity] = event.target.textContent + record.displayQuantity;
              this.invoice.salesInvoiceRecords[i]["price"] =
                this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
              this.calculateSubTotal();
            }
          } else {
            if (stock.quantity <= this.invoice.salesInvoiceRecords[i][quantity]) {
              this.notificationService.showError("Not enough stock");
            } else {
              this.invoice.salesInvoiceRecords[i][quantity] = event.target.textContent;
              this.invoice.salesInvoiceRecords[i]["price"] =
                this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
              this.calculateSubTotal();
            }
          }
        }
      });
    } else {
      this.invoice.salesInvoiceRecords[i][quantity] = event.target.textContent;
      this.invoice.salesInvoiceRecords[i]["price"] =
        event.target.textContent * this.invoice.salesInvoiceRecords[i]["unitPrice"];
    }

    for (const index in this.items) {
      if (this.items[index].itemCode === this.invoice.salesInvoiceRecords[i].itemCode) {
        this.selectedItem = this.items[index];
      }
    }
    if (this.selectedItem.manageStock) {
    } else {
      this.invoice.salesInvoiceRecords[i][quantity] = event.target.textContent;
      this.invoice.salesInvoiceRecords[i]["price"] =
        event.target.textContent * this.invoice.salesInvoiceRecords[i]["unitPrice"];
    }
  }

  //update sales invoice record list
  updateList(i: any, quantity: string, displayQuantity: string, event: any) {
    this.invoice.salesInvoiceRecords[i][quantity] = event.target.textContent;
    this.invoice.salesInvoiceRecords[i]["price"] =
      event.target.textContent * this.invoice.salesInvoiceRecords[i]["unitPrice"];
    this.calculateSubTotal();
  }

  //remove item records from sales invoice
  remove(id: any) {
    this.invoice.salesInvoiceRecords.splice(id, 1);
    this.numberOfSelectedItem--;
    this.calculateSubTotal();
  }

  //increase qty of sales invoice record
  increaseQuantity(i: any, quantity: string, displayQuantity: string, record: SalesInvoiceRecord) {
    this.selectedItem = new Item();
    this.selectedItem = record.item;
    if (true === this.selectedItem.manageStock) {
      // for (let stock of this.itemStockList) {
      this.stockService.findByItemAndWarehouse(this.selectedItem.id, this.currentWarehouse.id).subscribe((stock: Stock) => {
        if (stock.itemCode === this.selectedItem.itemCode) {
          if (this.selectedItem.prices != null && this.selectedItem.prices.length > 0) {
            if (stock.quantity <= this.invoice.salesInvoiceRecords[i][displayQuantity]) {
              this.notificationService.showError("Not enough stock");
            } else {
              this.invoice.salesInvoiceRecords[i][quantity]++;
              this.invoice.salesInvoiceRecords[i][displayQuantity] = this.invoice.salesInvoiceRecords[i][displayQuantity] + record.displayQuantity;
              this.invoice.salesInvoiceRecords[i]["price"] =
                this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
              this.calculateSubTotal();
            }
          } else {
            if (stock.quantity <= this.invoice.salesInvoiceRecords[i][quantity]) {
              this.notificationService.showError("Not enough stock");
            } else {
              this.invoice.salesInvoiceRecords[i][quantity]++;
              this.invoice.salesInvoiceRecords[i]["price"] =
                this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
              this.calculateSubTotal();
            }
          }
        }
      });
    } else {
      this.invoice.salesInvoiceRecords[i][quantity]++;
      this.invoice.salesInvoiceRecords[i]["price"] =
        this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
      this.calculateSubTotal();
    }
  }

  //decrease qty of sales invoice
  decreaseQuantity(i: any, quantity: string, displayQuantity: string, record: SalesInvoiceRecord) {
    this.selectedItem = new Item();
    this.selectedItem = record.item;
    if (true === this.selectedItem.manageStock) {
      this.stockService.findByItemAndWarehouse(this.selectedItem.id, this.currentWarehouse.id).subscribe((stock: Stock) => {
        if (stock.itemCode === this.selectedItem.itemCode) {
          if (this.selectedItem.prices != null && this.selectedItem.prices.length > 0) {
            this.invoice.salesInvoiceRecords[i][quantity]--;
            this.invoice.salesInvoiceRecords[i][displayQuantity] = this.invoice.salesInvoiceRecords[i][displayQuantity] - record.displayQuantity;
            this.invoice.salesInvoiceRecords[i]["price"] =
              this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
            this.calculateSubTotal();
          } else {

            this.invoice.salesInvoiceRecords[i][quantity]--;
            this.invoice.salesInvoiceRecords[i]["price"] =
              this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
            this.calculateSubTotal();
          }
        }
      });
      // }
    } else {
      this.invoice.salesInvoiceRecords[i][quantity]--;
      this.invoice.salesInvoiceRecords[i]["price"] =
        this.invoice.salesInvoiceRecords[i][quantity] * this.invoice.salesInvoiceRecords[i]["unitPrice"];
      this.calculateSubTotal();
    }
  }

  //send order to sales invoice
  convertOrderToInvoice() {
    this.clearAll();
    this.initInvoice();
    this.order = this.selectedOrder;
    this.invoice.salesInvoiceRecords = this.selectedOrder.salesInvoiceRecords;
    this.invoice.salesType = this.selectedOrder.salesType;
    this.isSalesTypeSelected = true;
    this.paymentMethodName = this.selectedOrder.salesType;
    this.invoice.serviceCharge = this.selectedOrder.serviceCharge;
    this.invoice.payment = this.selectedOrder.payment;
    this.invoice.convertedFromOrders = true;
    this.invoice.orderNo = this.selectedOrder.orderNo;
    this.keyTableNo = this.selectedOrder.table.tableNo;
    this.selectedTable = this.selectedOrder.table;
    this.calculateSubTotal();
    this.calculateTotal();
    this.calculateBalance();
    this.isOrderSelected = false;
    this.isProcessing = false;
  }

  //set payment method
  setPaymentMethod() {
    if (this.paymentMethodName) {
      this.metaDataService.findByValueAndCategory(this.paymentMethodName, "PaymentMethod").subscribe((data: any) => {
        this.invoice.paymentMethod = new MetaData();
        this.invoice.paymentMethod = data;
      });
    } else {
      return false;
    }
  }

  showCategories() {
    this.isCategory = true;
  }

  gotoPayment() {
    if (this.keyBarcodeSearch.length == 0)
      this.payment.nativeElement.focus();
  }

  gotoBarcode() {
    this.barcodeElm.nativeElement.focus();
  }

  focusQty() {
    this.qty.nativeElement.focus();
  }

  focusPrice() {
    this.sellingPrice.nativeElement.focus();
  }

  gotoItemName() {
    this.itemName.nativeElement.focus();
  }

  //Set searched item from input field
  setSearchedSelectedItem(event) {
    this.selectedItem = new Item();
    this.itemService.findOneById(event.item.id).subscribe((item: Item) => {
      this.selectedItem = item;
      this.keyItemNameSearch = this.selectedItem.itemName;
      this.keyBarcodeSearch = this.selectedItem.barcode;
      if (true === this.selectedItem.manageStock) {
        this.loadSelectedItemStock()
      } else {
        this.relativeQty = 1;
        this.sPrice = this.selectedItem.sellingPrice;
        this.qty.nativeElement.value = '';
        this.qty.nativeElement.focus();
      }
    })
  }

  //clear data of order
  initOrder() {
    this.order = new Order();
    this.order.salesInvoiceRecords = [];
  }

  //clear data of invoice
  initInvoice() {
    this.invoice = new SalesInvoice();
    this.invoice.salesType = "";
    this.invoice.salesInvoiceRecords = [];
    this.invoice.subTotal = 0;
    this.invoice.payment = null;
    this.invoice.totalAmount = 0;
    this.invoice.serviceCharge = 0;
    this.invoice.totalDiscount = 0;
  }
}
