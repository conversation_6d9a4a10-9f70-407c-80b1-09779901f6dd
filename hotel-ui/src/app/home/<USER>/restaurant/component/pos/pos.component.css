body {
  height: 100vh;
}

.full-height {
  height: 100vh;
}

.btn-outline-customize {
  width: 100%;
  color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-outline-customize:hover {
  color: #fff;
  background-color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-outline-customize:focus, .btn-outline-customize.focus {
  box-shadow: 0 0 0 0.2rem rgba(89, 12, 48, 0.5);
}

.btn-outline-customize.disabled, .btn-outline-customize:disabled {
  color: rgb(89, 12, 48);
  background-color: transparent;
}

.btn-outline-customize:not(:disabled):not(.disabled):active, .btn-outline-customize:not(:disabled):not(.disabled).active,
.show > .btn-outline-customize.dropdown-toggle {
  color: #fff;
  background-color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-outline-customize:not(:disabled):not(.disabled):active:focus, .btn-outline-customize:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-customize.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(89, 12, 48, 0.5);
}

.example-viewport {
  width: 100%;
}

.example-item {
  height: 50px;
}
input[type="radio"]{
  accent-color: rgba(89, 12, 48, 0.5);
}
/*cdk-virtual-scroll-viewport::-webkit-scrollbar-track {*/
/*  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);*/
/*  border-radius: 10px;*/
/*  background-color: #F5F5F5;*/
/*}*/

/*cdk-virtual-scroll-viewport::-webkit-scrollbar {*/
/*  width: 12px;*/
/*  background-color: #F5F5F5;*/
/*}*/

/*cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb {*/
/*  border-radius: 10px;*/
/*  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);*/
/*  background-color: #555;*/
/*}*/

/*.card-carousel {*/
/*  border-radius: 4px;*/
/*  background-color: rgb(235, 240, 255);*/
/*  box-shadow: 0 6px 10px rgba(0, 0, 0, .08), 0 0 6px rgba(0, 0, 0, .05);*/
/*  transition: .3s transform cubic-bezier(.155, 1.105, .295, 1.12), .3s box-shadow, .3s -webkit-transform cubic-bezier(.155, 1.105, .295, 1.12);*/
/*  !*padding: 14px 80px 18px 36px;*!*/
/*  cursor: pointer;*/
/*}*/

/*.card-carousel:hover {*/
/*  transform: scale(1.05);*/
/*  box-shadow: 0 10px 20px rgba(0, 0, 0, .12), 0 4px 8px rgba(0, 0, 0, .06);*/
/*}*/

/*.card-carousel h3 {*/
/*  font-weight: 600;*/
/*}*/

/*.card-carousel img {*/
/*  position: absolute;*/
/*  top: 20px;*/
/*  right: 15px;*/
/*  max-height: 120px;*/
/*}*/

.order {
  background-color: #f2f4f8;
}

.order-card {
  background-color: #d8dde9;
}

.select-item:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, .12), 0 4px 8px rgba(0, 0, 0, .06);
}

/*.lighter {*/
/*  background-color: #e9ecef;*/
/*}*/

.lighter {
  background-color: rgba(89, 12, 48, 0.06);
}

/*.light {*/
/*  background-color: #F8F9FA;*/
/*}*/

.light {
  background-color: rgba(89, 12, 48, 0.06);

}

/*.medium-light {*/
/*  background-color: #DEE2E6;*/
/*}*/

.medium-light {
  background-color: rgba(89, 12, 48, 0.24);

}

.billing {
  background-image: linear-gradient(to right, rgb(255, 255, 255), rgb(89, 12, 48));
}

.billing {
  background-image: linear-gradient(to right, rgb(200, 200, 255), rgb(89, 12, 48));
}

.billing-card {
  background-image: linear-gradient(to left, rgb(255, 255, 255), rgb(89, 12, 48));
}

.item-card {
  background-color: #EFF2F9;
  height: 10rem;
  /*transition: .3s transform cubic-bezier(.155, 1.105, .295, 1.12), .3s box-shadow, .3s -webkit-transform cubic-bezier(.155, 1.105, .295, 1.12);*/
}

.item-card:hover {
  background-color: #e9ecef;
  transform: scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, .12), 0 4px 8px rgba(0, 0, 0, .06);
}

/*.border-rounded {*/
/*  border: 2px solid white;*/
/*  border-radius: 8px;*/
/*}*/

.border-rounded {
  border: 1px solid rgba(89, 12, 48, 0.39);
  border-radius: 6px;
}

.theme-color-outline-button{
  color: rgb(89, 12, 48);
  background-color:  #EFF2F9;
  border: 1px solid rgb(89, 12, 48);
}

.order-button {
  position: absolute;
  left: 20%;
  right: 20%;
  bottom: 3%;
  width: max-content;
}


.has-search .form-control {
  padding-left: 2.375rem;
}

.has-search .form-control-feedback {
  position: absolute;
  z-index: 8;
  display: block;
  width: 2.375rem;
  height: 2.375rem;
  line-height: 2.375rem;
  text-align: center;
  pointer-events: none;
  color: #aaa;
}

.bottom-border {
  outline: 0;
  border-width: 0 0 2px;
  border-color: rgb(89, 12, 48);
}

.bottom-border:focus {
  /*border-color: green*//**/
}

.h-60 {
  height: 59% !important;
}

.h-40 {
  height: 40% !important;
}

.h-85 {
  height: 85% !important;
}

.h-70 {
  height: 70% !important;
}

.h-30 {
  height: 30% !important;
}

.h-90 {
  height: 90% !important;
}

.h-10 {
  height: 10% !important;
}
.container-fluid-custom {margin: 0; height: 100vh}

/* Full-screen layout styles */
.container-fluid {
  height: 100vh;
  overflow: hidden;
}

.theme-color {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 10px 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-section {
  min-height: 60px;
}

.header-section h2 {
  color: white;
  font-weight: 600;
}

.select-item {
  color: white;
  transition: all 0.3s ease;
}

.select-item:hover {
  color: #ffd700;
  transform: scale(1.1);
}

.content-section {
  height: calc(100vh - 80px);
  padding: 0;
}

.content-section .row {
  height: 100%;
  margin: 0;
}

.content-section .col-md-2,
.content-section .col-md-7,
.content-section .col-md-3 {
  height: 100%;
  padding: 15px;
}

/* Category and item cards */
.card {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
}

.card:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .content-section .col-md-2,
  .content-section .col-md-7,
  .content-section .col-md-3 {
    height: auto;
    min-height: 300px;
  }

  .header-section h2 {
    font-size: 1.5rem;
  }
}
