import {Component, OnInit} from '@angular/core';
import {Table} from "../../model/table";
import {NotificationService} from "../../../../core/service/notification.service";
import {TableService} from "../../service/table.service";

@Component({
  selector: 'app-tables',
  templateUrl: './tables.component.html',
  styleUrls: ['./tables.component.css']
})
export class TablesComponent implements OnInit {
  keyTableNo: string;
  tables: Table[];
  table: Table;
  selectedRow: number;
  collectionSize;
  page;
  pageSize;
  isActive: boolean;
  isAvailable: boolean;

  constructor(private tableService: TableService,
              private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 8;
    this.table = new Table();
    this.isActive = true;
    this.isAvailable = true;
    this.findAllTables();
  }

  findAllTables() {
    this.tableService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.tables = data.content;
      this.collectionSize = data.totalPages * 8;
    });
  }

  loadTables() {
    this.tableService.findByTableNoLike(this.keyTableNo).subscribe((data: any) => {
      this.tables = data;
    });
  }

  setSelectedTable() {

  }

  tableDetail(tbl: Table, i: number) {
    this.table = tbl;
    this.isActive = this.table.active;
    this.selectedRow = i;
  }

  pageChanged(event: any) {
    this.page = event.page;
  }

  saveTable() {
    this.table.active = this.isActive
    this.table.available = this.isAvailable;
    this.tableService.save(this.table).subscribe((res: any) => {
      if (res.code === 200) {
        this.notificationService.showSuccess(res.message);
        this.findAllTables();
      } else {
        this.notificationService.showError(res.message);
      }
    });
  }

  clear() {
    this.table = new Table();
  }
}