<div class="container">
  <div class="text-center mt-2">
    Delivery Charges
  </div>
  <div class="row m-2">
    <div class="col-md-12">
      <input type="text"
             class="form-control"
             [(ngModel)]="keyDestination"
             [typeahead]="destinations"
             (typeaheadLoading)="loadDestinations()"
             (typeaheadOnSelect)="setSelectedDestination($event)"
             [typeaheadOptionsLimit]="7"
             typeaheadWaitMs="1000"
             typeaheadOptionField="itemName"
             placeholder="Search Delivery Charges"
             name="destination"
             autocomplete="off"
             size="16">
    </div>
    <div class="mt-2 col-md-12" *ngIf="deliveryDetails">
      <div class="row">
        <label class="col-6">{{destination.itemName}}</label>
        <label class="col-3">{{destination.sellingPrice}}</label>
        <button class="btn btn-primary col-3" (click)="addDestination()">Ok</button>
      </div>
    </div>
  </div>
</div>

