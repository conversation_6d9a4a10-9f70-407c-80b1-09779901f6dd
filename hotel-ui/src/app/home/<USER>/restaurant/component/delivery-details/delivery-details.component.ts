import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {Item} from "../../../inventory/model/item";
import {ItemService} from "../../../inventory/service/item.service";
import {BsModalRef} from "ngx-bootstrap/modal";

@Component({
  selector: 'app-delivery-details',
  templateUrl: './delivery-details.component.html',
  styleUrls: ['./delivery-details.component.css']
})
export class DeliveryDetailsComponent implements OnInit {

  keyDestination: string;
  destination: Item;
  destinations: Array<Item>;
  modalRef: BsModalRef;
  deliveryDetails: boolean;

  @Output() passEntry: EventEmitter<any> = new EventEmitter();

  constructor(private itemService: ItemService) {
  }

  ngOnInit(): void {
    this.destination = new Item();
    this.deliveryDetails = false;
  }

  loadDestinations() {
    this.itemService.findAllByItemNameLikeAndItemType(this.keyDestination, "Delivery").subscribe((data: Array<Item>) => {
      this.destinations = data;
    });
  }

  //findAllByItemNameLikeAndItemType

  setSelectedDestination(event) {
    this.destination = event.item;
    this.deliveryDetails = true;
  }

  addDestination() {
    this.passEntry.emit(this.destination);
    this.modalRef.hide();
  }
}
