<div id="print-section" style="padding: 20px; font-family: Verdana; width: 78mm; height: auto">

  <div *ngIf="kotRecords.length > 0">
    <address style="text-align: center; padding-top: 0px">
      <p style="font-weight: bold; font-size: 0.8rem; padding: 0px;
    margin-bottom: 10px; font-family: Verdana;  font-style: normal;">{{company.name}}</p>
      <p style="font-weight: bold; font-size: 0.8rem; padding: 0px;
    margin-bottom: 11px; font-family: Verdana;  font-style: normal;">KOT</p>
    </address>

    <div style="width: 100%">
      <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 10px; margin-bottom: 10px; ">
      <table style="width: 100%">
        <tr>
          <td style="font-size: 0.7rem; text-align: left">Usr : {{user}}</td>
        </tr>
        <tr>
          <td style="font-size: 0.8rem;text-align: left">Order Date : {{date | date:'dd/MM/yyyy, hh:mm aa'}}</td>
        </tr>
      </table>
    </div>

    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 10px; margin-bottom: 10px; ">
    <table style="width: 100%">
      <tr>
        <td style="font-size: 1rem; text-align: center; font-weight: bold">Table No
          <span style="font-size: 1rem; text-align: right" *ngIf="order.table">
        {{order.table.tableNo}}</span>
        </td>
      </tr>
    </table>

    <div>
      <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 10px; margin-top: 0px;">
      <table style="width: 100%; margin: 0.3rem; font-size: 0.8rem; font-family: Verdana  ">
        <thead>
        <tr>
          <th style="padding: 0.4rem">#</th>
          <th style="padding: 0.4rem">Item</th>
          <th style="padding: 0.4rem">Qty</th>
        </tr>
        </thead>
        <tbody style="font-size: 0.85rem">
        <tr *ngFor="let rec of kotRecords; index as i">
          <td style="text-align: left">{{i + 1}}</td>
          <td style="text-align: left">{{rec.itemName}}</td>
          <td style="padding: 0.4rem;text-align: left">{{rec.displayQuantity}}</td>
        </tr>
        </tbody>
      </table>
    </div>
    <hr style="background-color: #fff; border-top: 2px dashed #000000;">
  </div>

  <div *ngIf="botRecords.length > 0" style="margin-top: 10px">
    <address style="text-align: center; padding-top: 0px">
      <p style="font-weight: bold; font-size: 0.8rem; padding: 0px;
    margin-bottom: 10px; font-family: Verdana;  font-style: normal;">{{company.name}}</p>
      <p style="font-weight: bold; font-size: 0.8rem; padding: 0px;
    margin-bottom: 11px; font-family: Verdana;  font-style: normal;">BOT</p>
    </address>

    <div style="width: 100%">
      <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 10px; margin-bottom: 10px; ">
      <table style="width: 100%">
        <tr>
          <td style="font-size: 0.7rem; text-align: left">Usr : {{user}}</td>
        </tr>
        <tr>
          <td style="font-size: 0.8rem;text-align: left">Order Date : {{date | date:'dd/MM/yyyy, hh:mm aa'}}</td>
        </tr>
      </table>
    </div>

    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 10px; margin-bottom: 10px; ">
    <table style="width: 100%">
      <tr>
        <td style="font-size: 1rem; text-align: center; font-weight: bold">Table No
          <span style="font-size: 1rem; text-align: right" *ngIf="order.table">
        {{order.table.tableNo}}</span>
        </td>
      </tr>
    </table>

    <div>
      <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 10px; margin-top: 0px;">
      <table style="width: 100%; margin: 0.3rem; font-size: 0.8rem; font-family: Verdana  ">
        <thead>
        <tr>
          <th style="padding: 0.4rem">#</th>
          <th style="padding: 0.4rem">Item</th>
          <th style="padding: 0.4rem">Qty</th>
        </tr>
        </thead>
        <tbody style="font-size: 0.85rem">
        <tr *ngFor="let rec of botRecords; index as i">
          <td style="text-align: left">{{i + 1}}</td>
          <td style="text-align: left">{{rec.itemName}}</td>
          <td style="padding: 0.4rem;text-align: left">{{rec.displayQuantity}}</td>
        </tr>
        </tbody>
      </table>
    </div>
    <hr style="background-color: #fff; border-top: 2px dashed #000000;">
  </div>
</div>

<div>
  <button class="btn btn-primary pull-right m-4" printSectionId="print-section" #printBtn
          ngxPrint="useExistingCss">
    Print
  </button>
</div>
