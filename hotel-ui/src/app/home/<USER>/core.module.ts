import {APP_INITIALIZER, NgModule} from '@angular/core';
import {CommonModule, DatePipe} from '@angular/common';
import {HTTP_INTERCEPTORS, HttpClientModule} from '@angular/common/http';
import {JwtInterceptor} from '../../helper/jwt.interceptor';
import {ErrorInterceptor} from '../../helper/error.interceptor';
import {RouterModule} from '@angular/router';
import {routeParams} from './core-routing.module';
import {SharedModule} from '../shared/shared.module';
import {TranslateService} from '../../translate.service';

export function setupTranslateFactory(
  service: TranslateService) {
  return () => {
    // Use the language from localStorage or default to English
    const lang = localStorage.getItem('lang') || 'en';
    console.log(`Initializing application with language: ${lang}`);

    // Load the translations
    return service.use(lang).then(() => {
      console.log('Translations loaded successfully');
      // Disable translation warnings by default (false parameter)
      service.debugTranslations(false);
    }).catch(error => {
      console.error('Error loading translations:', error);
    });
  };
}

@NgModule({
  declarations: [routeParams],
  imports: [
    CommonModule,
    HttpClientModule,
    RouterModule,
    SharedModule
  ],
  exports: [
    RouterModule,
    CommonModule,
    routeParams,
    SharedModule
  ],
  providers: [{provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true},
    {provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true},
    TranslateService, DatePipe,
    {
      provide: APP_INITIALIZER,
      useFactory: setupTranslateFactory,
      deps: [TranslateService],
      multi: true
    }
  ]
})

export class CoreModule {
}
