import {Designation} from './designation';
import {SalaryScale} from './salary-scale';
import {Department} from './department';

export class Employee {
  public id: string;
  public name: string;
  public nic: string;
  public addressLine1: string;
  public addressLine2: string;
  public addressLine3: string;
  public email: string;
  public telephone1: string;
  public telephone2: string;
  public gender: string;
  public joinedDate: Date;
  public epfNo: string;
  public department: Department;
  public salaryScale: SalaryScale;
  public designation: Designation;
  public reportingManager: Employee;  // for ease of displaying. not available in java model
  public reportingManagerId: string;
  public active: boolean;
}
