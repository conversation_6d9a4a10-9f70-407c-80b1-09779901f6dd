import {Component, OnInit} from '@angular/core';
import {Department} from '../../model/department';
import {DepartmentService} from '../../service/department.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {NgForm} from '@angular/forms';

@Component({
  selector: 'app-department',
  templateUrl: './department.component.html',
  styleUrls: ['./department.component.css']
})

export class DepartmentComponent implements OnInit {
  departments: any = [];
  department = new Department();
  setClickedRow: Function;
  selectedRow: number;
  collectionSize;
  page;
  pageSize;
  invalidDepartment: boolean;
  modalRef: BsModalRef;


  constructor(private departmentService: DepartmentService, private notificationSevice: NotificationService, private modalService: BsModalService) {

  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.findAll();
    this.department = new Department();
    this.department.active = true;
    this.invalidDepartment = false;
  }

  departmentDetail(dep) {
    this.department = dep;
  }

  findAll() {
    this.departmentService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.departments = data.content;
      this.collectionSize = data.totalPages * 10;
      console.log(data);
    });
  }

  save(form: NgForm) {
    this.departmentService.save(this.department).subscribe(result => {
      this.notificationSevice.showSuccess(result);
      this.findAll(); // Only refresh the data, don't call ngOnInit()
      this.department = new Department(); // Reset the form object
      form.reset();
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  Clear() {
    this.department = new Department();
  }

  checkValidDepartment() {
    this.departmentService.findByDepartmentName(this.department.departmentName).subscribe((res: boolean) => {
      this.invalidDepartment = res;
    });
  }
}

