<div class="container-fluid px-0">
  <h2 class="component-title">Leave Request</h2>
    <form #leaveRequestForm="ngForm">
      <div class="row">
        <div class="form-group col-md-4">
          <label>Leave From </label>
          <div class="input-group">
      <span class="input-group-prepend">
      <span class="input-group-text">
      <i class="fa fa-calendar"></i>
      </span>
      </span>
            <input type="text" required #from="ngModel" name="from" id="from"
                   [class.is-invalid]="from.invalid && from.touched" [(ngModel)]="leaveRequest.from"
                   class="form-control" autocomplete="off" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }">
          </div>
          <small class="text-danger" [class.d-none]="from.valid || from.untouched">*From Date is required
          </small>
        </div>  <div class="form-group col-md-4">
          <label>Leave To </label>
          <div class="input-group">
      <span class="input-group-prepend">
      <span class="input-group-text">
      <i class="fa fa-calendar"></i>
      </span>
      </span>
            <input type="text" required #to="ngModel" name="to" id="to"
                   [class.is-invalid]="to.invalid && to.touched" [(ngModel)]="leaveRequest.to"
                   [minDate]="leaveRequest.from"
                   class="form-control" autocomplete="off" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }">
          </div>
          <small class="text-danger" [class.d-none]="to.valid || to.untouched">*To Date is required
          </small>
        </div>  <div class="col-md-4">
          <label> Employee</label>
          <div class="input-group">
            <input [(ngModel)]="keyEmployee"
                   [typeahead]="employees"
                   (typeaheadLoading)="loadEmployees()"
                   (typeaheadOnSelect)="setSelectedEmployee($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   placeholder="Search Employee"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="emp" required>
          </div>
        </div>
      </div>  <div class="row">
        <div class="form-group col-md-4">
          <label>EPF No</label>
          <input type="number" required #epfNo="ngModel"
                 [class.is-invalid]="epfNo.invalid && epfNo.touched"
                 class="form-control" id="epfNo" placeholder="Enter EPF No" name="epf"
                 [(ngModel)]="leaveRequest.employee.epfNo">
          <small class="text-danger" [class.d-none]="epfNo.valid ||epfNo.untouched">*EPF No is
            Required
          </small>
        </div>  <div class="form-group col-md-4">
          <label>Leave Type</label>
          <select required #leaveType="ngModel" [class.is-invalid]="leaveType.invalid && leaveType.touched"
                  class="form-control" name="leaveType" (change)="onChangeType($event)" [(ngModel)]="typeId">
            <option value="">Select</option>
            <option *ngFor="let leave of leaveTypes" [value]="leave.id">{{leave.leaveType}}</option>
          </select>
          <small class="text-danger" [class.d-none]="leaveType.valid || leaveType.untouched">*Leave Type is
            required
          </small>
        </div>  <div class="form-group col-md-4">
          <label>Reason</label>
          <textarea [(ngModel)]="leaveRequest.reason" type="text" rows="2" cols="10"
                    class="form-control" id="reason1" name="reason"
                    placeholder="Enter Reason"></textarea>
        </div>
      </div>  <div class="row">
        <div class="col-md-4">
          <label>Covering Employee</label>
          <div class="input-group">
            <input [(ngModel)]="coverEmployee"
                   [typeahead]="employees"
                   (typeaheadLoading)="loadCoverEmployees()"
                   (typeaheadOnSelect)="setSelectedCoveringEmployee($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   placeholder="Search Employee"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="cvrEmp" required>
          </div>
        </div>
      </div>  <div class="row text-right">
        <div class="col-md-12">
          <button type="button" class="btn btn-warning mr-1" [disabled]="!leaveRequestForm.reset">Clear
          </button>
          <button type="submit" (click)="save(leaveRequestForm);" [disabled]="!leaveRequestForm.form.valid"
                  class="btn btn-primary">Save
          </button>
        </div>
      </div>
    </form>
</div>


