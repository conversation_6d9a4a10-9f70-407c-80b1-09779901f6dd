<div class="container-fluid px-0">
  <h2 class="component-title">Manage Leaves</h2>
    <div class="row">
      <div class="col-md-4">
        <div class="form-group ">
          <input [(ngModel)]="keyEpf"
                 [typeahead]="leaves"
                 (typeaheadLoading)="loadEpf()"
                 (typeaheadOnSelect)="setSelectedEpf($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="epf"
                 placeholder="Search By Epf No"
                 autocomplete="off"
                 size="16"
                 required
                 class="form-control" name="epf">
        </div>
      </div>
    </div>
    <table class="table table-bordered table-striped table-sm">
      <thead>
      <tr style="text-align: center">
        <th scope="col">Employee Name</th>
        <th scope="col">Epf No</th>
        <th scope="col">From</th>
        <th scope="col">To</th>
        <th scope="col">Action</th>
      </tr>
      </thead>
      <tbody>
      <tr style="text-align: center" *ngFor="let leave of leaves,let i = index"
          (click)="leaveRequestDetail(leave,i)"
          [class.active]="i === selectedRow">
        <td>{{leave.employee.name}}</td>
        <td>{{leave.employee.epfNo}}</td>
        <td>{{leave.from | date: '+530'}}</td>
        <td>{{leave.to | date : '+530'}}</td>
        <td>
          <button (click)="confirm()">confirm</button>
          <button (click)="notConfirm()">not confirm</button>
        </td>
      </tr>
      </tbody>
    </table>
    <pagination class="pagination-sm justify-content-center"
                [totalItems]="collectionSize"
                [(ngModel)]="page"
                (pageChanged)="pageChanged($event)">
    </pagination>
    <div class="row text-right">
      <div class="col-md-12">
        <button class="btn btn-primary " type="button" (click)="openModal(template)">View More</button>
      </div>
    </div>
</div>

<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left"></h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>  <div class="modal-body">
    <div class="details-container">
      <h4 class="mb-3"><strong>View </strong><small>Details</small></h4>
        <table class="table table-bordered table-striped table-sm">
          <thead>
          <tr style="text-align: center">
            <th scope="col">Leave Type</th>
            <th scope="col">Reason</th>
            <th scope="col">Covering Employee</th>
          </tr>
          </thead>
          <tbody>
          <tr style="text-align: center" *ngFor="let le of leaves,let i = index">
            <td>{{le.type.leaveType}}</td>
            <td>{{le.reason}}</td>
            <td>{{le.coveringEmp.name}}</td>
          </tr>
          </tbody>
        </table>
    </div>
  </div>
</ng-template>



