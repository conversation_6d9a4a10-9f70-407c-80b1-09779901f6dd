import {Component, OnInit, TemplateRef} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {LeaveRequestService} from '../../service/leave-request.service';
import {LeaveRequest} from '../../model/leave-request';
import {NotificationService} from '../../../../core/service/notification.service';
import {MetaDataService} from '../../../../core/service/metaData.service';
import {MetaData} from '../../../../core/model/metaData';


@Component({
  selector: 'app-manage-leaves',
  templateUrl: './manage-leaves.component.html',
  styleUrls: ['./manage-leaves.component.css']
})
export class ManageLeavesComponent implements OnInit {
  modalRef: BsModalRef;
  leaves: Array<LeaveRequest> = [];
  collectionSize;
  page;
  pageSize;
  selectedRow: number;
  setClickedRow: Function;
  LeaveRequest = new LeaveRequest();
  keyEpf: string;
  leave = new LeaveRequest();
  type: MetaData;

  constructor(private modalService: BsModalService, private leaveRequestService: LeaveRequestService,
              public notificationService:NotificationService, public metaDataService:MetaDataService) {
  }

  ngOnInit() {
    this.LeaveRequest = new LeaveRequest();
    this.leave = new LeaveRequest();
    this.page = 1;
    this.pageSize = 10;
    this.findAll();
  }

  findAll() {
    this.leaveRequestService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.leaves = data.content;
      this.collectionSize = data.totalPages * 10;
      console.log(data);
    });
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  leaveRequestDetail(m, index) {
    this.LeaveRequest = m;
    this.selectedRow = index;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  loadEpf() {
    this.leaveRequestService.findByEpf(this.keyEpf).subscribe((data: Array<LeaveRequest>) => {
      return this.leaves = data;
    });
  }

  setSelectedEpf(event) {
    this.leave = event.item;
  }

  confirm(){
    if (confirm('Are you sure you want to Approve this?')) {
      this.metaDataService.findByValueAndCategory('Approve', 'LeaveStatus').subscribe((data: MetaData) => {
        this.LeaveRequest.leaveStatus = data;
        this.leaveRequestService.save(this.LeaveRequest).subscribe(result => {
          this.notificationService.showSuccess(result);
          this.findAll();
        });
      });
    }
  }

  notConfirm() {
    if (confirm('Are you sure you want to Not Approve this?')) {
      this.metaDataService.findByValueAndCategory('Not Approve', 'LeaveStatus').subscribe((data: MetaData) => {
        this.LeaveRequest.leaveStatus = data;
        this.leaveRequestService.save(this.LeaveRequest).subscribe(result => {
          this.notificationService.showSuccess(result);
          this.findAll();

        });
      });
    }
  }
}








