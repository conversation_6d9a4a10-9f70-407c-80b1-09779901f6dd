import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {HrApiConstants} from '../hr-constants';

@Injectable({
  providedIn: 'root'
})
export class LeaveService {

  constructor(private http:HttpClient) { }

  public  findAllPagination (page, pageSize) {
    return this.http.get(HrApiConstants.GET_ADD_LEAVES,{params: {page: page, pageSize: pageSize}});
  }
  save (addLeave) {
    return this.http.post<any>(HrApiConstants.SAVE_ADD_LEAVE, addLeave);

  }
  findById(id: string) {
    return this.http.get(HrApiConstants.FIND_BY_LEAVE_ID, {params: {id: id}});
  }
  getAll() {
    return this.http.get(HrApiConstants.FIND_LEAVE_TYPES);
  }

  findByLeaveTypeName(leaveType: string) {
    return this.http.get(HrApiConstants.FIND_BY_LEAVE_NAME, {params: {leaveType: leaveType}});
  }

}
