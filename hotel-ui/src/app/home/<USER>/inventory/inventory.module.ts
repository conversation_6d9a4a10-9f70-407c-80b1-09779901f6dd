import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {InventoryRoutingModule, invRouteParams} from './inventory-routing.module';
import {CoreModule} from '../../core/core.module';
import {NgxLoadingModule} from "ngx-loading";
import {ReactiveFormsModule, FormsModule} from "@angular/forms";

@NgModule({
  declarations: [invRouteParams],
    imports: [
        CommonModule,
        InventoryRoutingModule,
        CoreModule,
        NgxLoadingModule,
        ReactiveFormsModule,
        FormsModule
    ],
    exports: [
        invRouteParams
    ]
})
export class InventoryModule {
}
