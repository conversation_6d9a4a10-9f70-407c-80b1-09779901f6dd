export interface TableColumn {
  key: string;       // Property name in the data object
  header: string;    // Display name for the column header
  visible: boolean;  // Whether the column is visible
  sortable?: boolean; // Whether the column can be sorted
  width?: string;    // Optional width for the column
  class?: string;    // Optional CSS class for the column
  mobile?: boolean;  // Whether to show on mobile devices
}
