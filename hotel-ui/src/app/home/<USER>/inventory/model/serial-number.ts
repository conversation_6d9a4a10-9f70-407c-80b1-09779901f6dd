export class SerialNumber {
  public id: string;
  public serialNumber: string;
  public itemCode: string;
  public status: string; // AVAILABLE, SOLD, RETURNED, DAMAGED
  public dateAdded: Date;
  public dateSold: Date;
  public warrantyExpiryDate: Date;
  public purchaseInvoiceId: string;
  public salesInvoiceId: string;
  public returnInvoiceId: string;
  public purchasePrice: number;
  public sellingPrice: number;
  public notes: string;
  public warehouseCode: number;
  public customerId: string;
  public customerName: string;
  public customerNumber: string;
  public createdDate: Date;
  public createdBy: string;
  public lastModifiedDate: Date;
  public lastModifiedBy: string;

  constructor() {
    this.status = 'AVAILABLE';
    this.dateAdded = new Date();
  }

  // Utility methods
  public isAvailable(): boolean {
    return this.status === 'AVAILABLE';
  }

  public isSold(): boolean {
    return this.status === 'SOLD';
  }

  public isReturned(): boolean {
    return this.status === 'RETURNED';
  }

  public isDamaged(): boolean {
    return this.status === 'DAMAGED';
  }

  public isWarrantyValid(): boolean {
    if (!this.warrantyExpiryDate) {
      return false;
    }
    return new Date(this.warrantyExpiryDate) > new Date();
  }

  public getStatusDisplayName(): string {
    switch (this.status) {
      case 'AVAILABLE':
        return 'Available';
      case 'SOLD':
        return 'Sold';
      case 'RETURNED':
        return 'Returned';
      case 'DAMAGED':
        return 'Damaged';
      default:
        return this.status;
    }
  }

  public getStatusClass(): string {
    switch (this.status) {
      case 'AVAILABLE':
        return 'badge-success';
      case 'SOLD':
        return 'badge-primary';
      case 'RETURNED':
        return 'badge-warning';
      case 'DAMAGED':
        return 'badge-danger';
      default:
        return 'badge-secondary';
    }
  }
}

export class SerialNumberStats {
  public totalCount: number;
  public availableCount: number;
  public soldCount: number;
  public returnedCount: number;
  public damagedCount: number;

  constructor() {
    this.totalCount = 0;
    this.availableCount = 0;
    this.soldCount = 0;
    this.returnedCount = 0;
    this.damagedCount = 0;
  }
}

export class SerialNumberRequest {
  public itemCode: string;
  public serialNumbers: string[];
  public purchaseInvoiceId?: string;
  public salesInvoiceId?: string;
  public returnInvoiceId?: string;
  public purchasePrice?: number;
  public sellingPrice?: number;
  public warehouseCode?: number;
  public warrantyExpiryDate?: Date;
  public quantity?: number;
  public notes?: string;

  constructor() {
    this.serialNumbers = [];
  }
}
