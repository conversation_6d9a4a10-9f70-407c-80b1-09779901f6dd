import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SupplierReturn } from '../../model/supplier-return';
import { SupplierReturnService } from '../../service/supplier-return.service';
import { StockService } from '../../service/stock.service';
import { SupplierService } from '../../../trade/service/supplier.service';
import { ItemService } from '../../service/item.service';
import { NotificationService } from '../../../../core/service/notification.service';
import { Stock } from '../../model/stock';
import { Supplier } from '../../../trade/model/supplier';
import { Item } from '../../model/item';
import { WarehouseService } from '../../service/warehouse.service';
import { Warehouse } from '../../model/warehouse';
import {environment} from "../../../../../../environments/environment";

@Component({
  selector: 'app-supplier-return',
  templateUrl: './supplier-return.component.html',
  styleUrls: ['./supplier-return.component.css']
})
export class SupplierReturnComponent implements OnInit {

  supplierReturnForm: FormGroup;
  supplierReturns: SupplierReturn[] = [];
  selectedSupplierReturn: SupplierReturn;

  // Search fields
  keyBarcode: string;
  keyItemName: string;
  keySupplier: string;

  // Search results for typeahead
  itemSearchResults: any[] = [];
  itemNameSearchResults: any[] = [];
  supplierSearchResults: Supplier[] = [];

  // Selected supplier
  selectedSupplier: Supplier = null;

  // Return items array
  returnItems: any[] = [];

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;

  // Dropdown data
  suppliers: Supplier[] = [];
  warehouses: Warehouse[] = [];

  // Item selection properties
  selectedItem: Item = null;
  availablePrices: any[] = [];
  selectedPrice: number = null;
  returnQuantity: number = 1;
  selectedReason: string = '';
  currentStock: number = 0;
  currentItemCost: number = 0;

  // Reasons for return
  returnReasons = [
    'Physically Damaged',
    'Expired',
    'Not Working',
    'Wrong Item',
    'Quality Issue',
    'Overstock',
    'Customer Return',
    'Manufacturing Defect',
    'Packaging Damaged',
    'Wrong Specification',
    'Supplier Error',
    'Transportation Damage',
    'Other'
  ];

  // Loading flag
  loading = false;

  // Selected stock
  selectedStock: Stock;

  constructor(
    private fb: FormBuilder,
    private supplierReturnService: SupplierReturnService,
    private stockService: StockService,
    private supplierService: SupplierService,
    private itemService: ItemService,
    private warehouseService: WarehouseService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadSupplierReturns();
    this.loadSuppliers();
    this.loadWarehouses();
  }

  initForm(): void {
    this.supplierReturnForm = this.fb.group({
      id: [null],
      barcode: ['', Validators.required],
      itemCode: ['', Validators.required],
      itemName: ['', Validators.required],
      supplierCode: ['', Validators.required],
      supplierName: ['', Validators.required],
      quantity: [1, [Validators.required, Validators.min(0.01)]],
      sellingPrice: [0, Validators.required],
      itemCost: [0, Validators.required],
      reason: ['', Validators.required],
      warehouseCode: [environment.warehouseCode, Validators.required], // Default to main warehouse
      warehouseName: ['', Validators.required]
    });
  }

  loadSupplierReturns(): void {
    this.loading = true;
    this.supplierReturnService.findAll(this.page - 1, this.pageSize).subscribe(
      (data: any) => {
        if (data && data.content) {
          this.supplierReturns = data.content;
          this.collectionSize = data.totalElements;
        }
        this.loading = false;
      },
      error => {
        console.error('Error loading supplier returns:', error);
        this.notificationService.showError('Failed to load supplier returns');
        this.loading = false;
      }
    );
  }



  loadWarehouses(): void {
    this.warehouseService.findAll().subscribe(
      (data: any) => {
        this.warehouses = data;
      },
      error => {
        console.error('Error loading warehouses:', error);
        this.notificationService.showError('Failed to load warehouses');
      }
    );
  }

  /**
   * Load items by barcode for typeahead search
   */
  loadItemsByBarcode(): void {
    if (!this.keyBarcode || this.keyBarcode.length < 2) {
      this.itemSearchResults = [];
      return;
    }

    this.itemService.findAllByBarcodeLike(this.keyBarcode).subscribe(
      (results: Item[]) => {
        this.itemSearchResults = results.slice(0, 15);
      },
      error => {
        console.error('Error searching by barcode:', error);
        this.itemSearchResults = [];
      }
    );
  }

  /**
   * Load items by name for typeahead search
   */
  loadItemsByName(): void {
    if (!this.keyItemName || this.keyItemName.length < 2) {
      this.itemNameSearchResults = [];
      return;
    }

    this.itemService.findAllActiveByNameLike(this.keyItemName).subscribe(
      (results: Item[]) => {
        this.itemNameSearchResults = results.slice(0, 15);
      },
      error => {
        console.error('Error searching by name:', error);
        this.itemNameSearchResults = [];
      }
    );
  }

  /**
   * Load suppliers for typeahead search
   */
  loadSuppliers(): void {
    if (!this.keySupplier || this.keySupplier.length < 2) {
      this.supplierSearchResults = [];
      return;
    }

    this.supplierService.findByNameLike(this.keySupplier).subscribe(
      (results: Supplier[]) => {
        this.supplierSearchResults = results.slice(0, 15);
      },
      error => {
        console.error('Error searching suppliers:', error);
        this.supplierSearchResults = [];
      }
    );
  }

  /**
   * Select item from typeahead (doesn't add to table yet)
   * Following the sales invoice pattern for price retrieval
   */
  selectItem(event: any): void {
    this.selectedItem = event.item;
    if (!this.selectedItem) return;

    // Clear previous selections
    this.availablePrices = [];
    this.selectedPrice = null;
    this.returnQuantity = 1;
    this.selectedReason = '';
    this.currentStock = 0;
    this.currentItemCost = 0;

    // Set initial selling price from item (like sales invoice does)
    if (this.selectedItem.sellingPrice) {
      this.selectedPrice = this.selectedItem.sellingPrice;
    }

    // If item manages stock, get all available prices from stock
    if (this.selectedItem.manageStock) {
      this.loading = true;
      const warehouseCode = environment.warehouseCode; // Default to main warehouse (like sales invoice uses 0)

      this.stockService.findPricesByBarcodeAndWarehouse(this.selectedItem.barcode, warehouseCode).subscribe(
        (pricesData: any) => {
          if (pricesData && pricesData.length > 0) {
            // Transform prices data for dropdown (quantity, sellingPrice format)
            this.availablePrices = pricesData.map(([quantity, sellingPrice]) => ({
              quantity: parseFloat(quantity),
              sellingPrice: parseFloat(sellingPrice)
            }));

            // Set the first price as default (highest quantity stock)
            this.selectedPrice = this.availablePrices[0].sellingPrice;
            this.currentStock = this.availablePrices[0].quantity;

            // Get the item cost for the selected price
            this.onPriceChange();

            this.notificationService.showSuccess(`Found ${this.availablePrices.length} price option(s) for ${this.selectedItem.itemName}`);
          } else {
            this.notificationService.showWarning('No stock found for this item');
            this.availablePrices = [];
          }
          this.loading = false;
        },
        error => {
          console.error('Error finding prices for item:', error);
          this.notificationService.showError('Error finding stock information');
          this.availablePrices = [];
          this.loading = false;
        }
      );
    } else {
      // For non-stock items, use the item's default selling price
      this.currentStock = 0;
      this.currentItemCost = this.selectedItem.itemCost || 0;
    }

    // Clear search fields
    this.keyBarcode = '';
    this.keyItemName = '';
  }

  /**
   * Handle price selection change
   */
  onPriceChange(): void {
    if (!this.selectedItem || !this.selectedPrice) {
      this.currentStock = 0;
      this.currentItemCost = 0;
      return;
    }

    // Get specific stock record for selected price
    this.loading = true;
    const warehouseCode = environment.warehouseCode; // Default to main warehouse

    this.stockService.findByItemCodeAndWarehouseAndPrice(this.selectedItem.itemCode, warehouseCode, this.selectedPrice).subscribe(
      (stock: any) => {
        if (stock) {
          this.currentStock = stock.quantity || 0;
          this.currentItemCost = stock.itemCost || 0;

          // Set max quantity to current stock
          if (this.returnQuantity > this.currentStock) {
            this.returnQuantity = this.currentStock;
          }
        } else {
          this.notificationService.showWarning('Stock record not found for selected price');
          this.currentStock = 0;
          this.currentItemCost = 0;
        }
        this.loading = false;
      },
      error => {
        console.error('Error finding stock record:', error);
        this.notificationService.showError('Error finding stock record');
        this.currentStock = 0;
        this.currentItemCost = 0;
        this.loading = false;
      }
    );
  }

  /**
   * Check if item can be added to return list
   */
  canAddItem(): boolean {
    return this.selectedItem !== null &&
           this.selectedPrice !== null &&
           this.returnQuantity > 0 &&
           this.returnQuantity <= this.currentStock &&
           this.selectedReason !== '';
  }

  /**
   * Add item to return list with selected parameters
   */
  addItemToReturnList(): void {
    if (!this.canAddItem()) {
      this.notificationService.showWarning('Please fill all required fields and ensure valid quantity');
      return;
    }

    // Check if item with same price already exists in return list
    const existingIndex = this.returnItems.findIndex(item =>
      item.itemCode === this.selectedItem.itemCode &&
      item.sellingPrice === this.selectedPrice
    );

    if (existingIndex !== -1) {
      this.notificationService.showWarning('Item with this price already added to return list');
      return;
    }

    // Add item to return list
    const returnItem = {
      itemCode: this.selectedItem.itemCode,
      itemName: this.selectedItem.itemName,
      barcode: this.selectedItem.barcode,
      currentStock: this.currentStock,
      sellingPrice: this.selectedPrice,
      returnQuantity: this.returnQuantity,
      itemCost: this.currentItemCost,
      totalCost: this.returnQuantity * this.currentItemCost,
      reason: this.selectedReason,
      stockId: null, // Will be set by backend
      warehouseCode: environment.warehouseCode, // Default warehouse
      warehouseName: 'Main Warehouse'
    };

    this.returnItems.push(returnItem);
    this.notificationService.showSuccess(`${this.selectedItem.itemName} added to return list`);

    // Clear selection
    this.clearSelection();
  }

  /**
   * Clear current item selection
   */
  clearSelection(): void {
    this.selectedItem = null;
    this.availablePrices = [];
    this.selectedPrice = null;
    this.returnQuantity = 1;
    this.selectedReason = '';
    this.currentStock = 0;
    this.currentItemCost = 0;
    this.keyBarcode = '';
    this.keyItemName = '';
  }

  /**
   * Set selected supplier from typeahead
   */
  setSelectedSupplier(event: any): void {
    this.selectedSupplier = event.item;
    this.keySupplier = '';
    this.notificationService.showSuccess(`Supplier selected: ${this.selectedSupplier.name}`);
  }

  /**
   * Clear selected supplier
   */
  clearSelectedSupplier(): void {
    this.selectedSupplier = null;
    this.keySupplier = '';
  }

  /**
   * Update item total cost when quantity changes
   */
  updateItemTotal(index: number): void {
    const item = this.returnItems[index];
    if (item) {
      item.totalCost = (item.returnQuantity || 0) * (item.itemCost || 0);
    }
  }

  /**
   * Remove item from return list
   */
  removeItemFromReturn(index: number): void {
    if (index >= 0 && index < this.returnItems.length) {
      const item = this.returnItems[index];
      this.returnItems.splice(index, 1);
      this.notificationService.showInfo(`${item.itemName} removed from return list`);
    }
  }

  /**
   * Clear all items from return list
   */
  clearAllItems(): void {
    this.returnItems = [];
    this.notificationService.showInfo('All items cleared from return list');
  }

  /**
   * Get total quantity of all return items
   */
  getTotalQuantity(): number {
    return this.returnItems.reduce((total, item) => total + (item.returnQuantity || 0), 0);
  }

  /**
   * Get total cost of all return items
   */
  getTotalCost(): number {
    return this.returnItems.reduce((total, item) => total + (item.totalCost || 0), 0);
  }

  /**
   * Check if return is valid
   */
  isValidReturn(): boolean {
    if (this.returnItems.length === 0) return false;

    return this.returnItems.every(item =>
      item.returnQuantity > 0 &&
      item.reason &&
      item.reason.trim() !== ''
    );
  }

  /**
   * Process the return
   */
  processReturn(): void {
    if (!this.selectedSupplier) {
      this.notificationService.showWarning('Please select a supplier');
      return;
    }

    if (!this.isValidReturn()) {
      this.notificationService.showWarning('Please ensure all items have valid quantities and reasons');
      return;
    }

    this.loading = true;

    // Process each return item
    const returnPromises = this.returnItems.map(item => {
      const supplierReturn = new SupplierReturn();
      supplierReturn.itemCode = item.itemCode;
      supplierReturn.itemName = item.itemName;
      supplierReturn.barcode = item.barcode;
      supplierReturn.quantity = item.returnQuantity;
      supplierReturn.itemCost = item.itemCost;
      supplierReturn.sellingPrice = item.sellingPrice; // Now using the actual selling price
      supplierReturn.reason = item.reason;
      supplierReturn.supplierCode = this.selectedSupplier.id;
      supplierReturn.supplierName = this.selectedSupplier.name;
      supplierReturn.warehouseCode = item.warehouseCode;
      supplierReturn.warehouseName = item.warehouseName;
      supplierReturn.returnDate = new Date();
      supplierReturn.returnNo = this.generateReturnNumber();

      return this.supplierReturnService.save(supplierReturn).toPromise();
    });

    Promise.all(returnPromises)
      .then(() => {
        this.notificationService.showSuccess('All items returned successfully');
        this.clearAllItems();
        this.clearSelectedSupplier();
        this.loading = false;
      })
      .catch(error => {
        console.error('Error processing returns:', error);
        this.notificationService.showError('Error processing some returns');
        this.loading = false;
      });
  }

  /**
   * Generate return number
   */
  generateReturnNumber(): string {
    const timestamp = new Date().getTime();
    return `RET-${timestamp}`;
  }

  populateFormWithStock(stock: Stock): void {
    // Find the supplier
    const supplier = this.suppliers.find(s => s.id === stock.supplierCode);

    this.supplierReturnForm.patchValue({
      barcode: stock.barcode,
      itemCode: stock.itemCode,
      itemName: stock.itemName,
      supplierCode: stock.supplierCode,
      supplierName: supplier ? supplier.name : '',
      sellingPrice: stock.sellingPrice,
      itemCost: stock.itemCost,
      warehouseCode: stock.warehouseCode,
      warehouseName: stock.warehouseName
    });
  }

  onSupplierSelect(event: any): void {
    if (event && event.item) {
      this.supplierReturnForm.patchValue({
        supplierCode: event.item.id,
        supplierName: event.item.name
      });
    }
  }

  onWarehouseSelect(event: any): void {
    if (event && event.item) {
      this.supplierReturnForm.patchValue({
        warehouseCode: event.item.code,
        warehouseName: event.item.name
      });
    }
  }

  saveSupplierReturn(): void {
    if (this.supplierReturnForm.invalid) {
      this.notificationService.showWarning('Please fill all required fields');
      return;
    }

    const supplierReturn = this.supplierReturnForm.value as SupplierReturn;

    this.loading = true;
    this.supplierReturnService.save(supplierReturn).subscribe(
      (response: any) => {
        if (response && response.success) {
          this.notificationService.showSuccess('Supplier return saved successfully');
          this.resetForm();
          this.loadSupplierReturns();
        } else {
          this.notificationService.showError(response.message || 'Failed to save supplier return');
        }
        this.loading = false;
      },
      error => {
        console.error('Error saving supplier return:', error);
        this.notificationService.showError('Failed to save supplier return');
        this.loading = false;
      }
    );
  }

  resetForm(): void {
    this.supplierReturnForm.reset();
    this.selectedStock = null;
    this.initForm();
  }

  viewSupplierReturn(supplierReturn: SupplierReturn): void {
    this.selectedSupplierReturn = supplierReturn;
    this.supplierReturnForm.patchValue(supplierReturn);
  }

  pageChanged(event: any): void {
    this.page = event.page;
    this.loadSupplierReturns();
  }
}
