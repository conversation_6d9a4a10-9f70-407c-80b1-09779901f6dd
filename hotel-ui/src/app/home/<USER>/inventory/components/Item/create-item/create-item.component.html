<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="mb-0 text-primary fw-bold border-bottom pb-2">CREATE ITEM</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <form (ngSubmit)="saveItem(createItemForm)" #createItemForm="ngForm" class="needs-validation">
    <div class="row g-3 mb-3">
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Item Type</label>
        <div class="input-group">
          <select class="form-control form-control-lg" (change)="setSelectedItemType($event)" name="itemTypeSelect"
                  [(ngModel)]="itemTypeId" required #itemTypeSelect="ngModel"
                  [class.is-invalid]="itemTypeSelect.invalid && itemTypeSelect.touched">
            <option>-Select-</option>
            <option *ngFor="let ity of itemTypes" [value]="ity.id">
              {{ ity.name }}
            </option>
          </select>
        </div>
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Barcode</label>
        <div class="input-group">
          <input [disabled]="isEditing" type="text" required #barcode="ngModel"
                 [class.is-invalid]="barcode.invalid && barcode.touched"
                 class="form-control form-control-lg" id="barcode" [(ngModel)]="item.barcode" name="barcode"
                 placeholder="Barcode" (ngModelChange)="checkItemAvailable()" autocomplete="off">
          <div class="input-group-append">
            <button class="btn btn-primary btn-lg" type="button" (click)="openBarcodeScanner()">
              <i class="fa fa-barcode"></i>
            </button>
          </div>
        </div>
        <small class="text-danger fw-bold" [class.d-none]="!itemAvailable">This item is already available</small>
      </div>
      <div class="form-group col-12 col-md-6">
        <label class="form-label fw-bold">Item Name</label>
        <input type="text" required #itemName="ngModel" [class.is-invalid]="itemName.invalid && itemName.touched"
               class="form-control form-control-lg" id="itemName" [(ngModel)]="item.itemName" name="itemName"
               placeholder="item Name" autocomplete="off">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Description</label>
        <input type="text" #description="ngModel" [class.is-invalid]="description.invalid && description.touched"
               class="form-control form-control-lg" id="description" [(ngModel)]="item.description" name="description"
               placeholder="Description" autocomplete="off">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Item Category</label>
        <div class="input-group">
          <input [(ngModel)]="keyItemCategory"
                 [typeahead]="categories"
                 (typeaheadLoading)="loadItemCategories()"
                 (typeaheadOnSelect)="setSelectedItemCategory($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="categoryName"
                 placeholder="Search Item Categories"
                 autocomplete="off"
                 #category="ngModel"
                 [class.is-invalid]="category.touched && category.invalid"
                 class="form-control form-control-lg" name="category">
          <span class="input-group-append">
                  <button class="btn btn-primary btn-lg fa fa-plus" (click)="openModalCategory()"
                          type="button"></button>
                  </span>
        </div>
        <tag-input [(ngModel)]="selectedItemCategory"
                   [identifyBy]="'id'"
                   [displayBy]="'categoryName'" name="catList" [hideForm]="true"
                   [placeholder]="''" [inputClass]="'form-control-lg'" [ripple]="false"
                   [animationDuration]="{enter: '0ms', leave: '0ms'}"></tag-input>
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Brand</label>
        <div class="input-group">
          <input [(ngModel)]="keyBrand"
                 [typeahead]="brands"
                 (typeaheadLoading)="loadBrands()"
                 (typeaheadOnSelect)="setSelectedBrand($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="Search Brand"
                 autocomplete="off"
                 id="appendedInputButtons"
                 class="form-control form-control-lg" name="brand">
          <span class="input-group-append">
                  <button class="btn btn-primary btn-lg fa fa-plus" (click)="openModalBrand()"
                          type="button"></button>
                  </span>
        </div>
        <tag-input [(ngModel)]="selectedBrand"
                   [identifyBy]="'id'"
                   [displayBy]="'name'" name="brandList" [hideForm]="true"
                   [placeholder]="''" [inputClass]="'form-control-lg'" [ripple]="false"
                   [animationDuration]="{enter: '0ms', leave: '0ms'}"></tag-input>
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Sub Category</label>
        <div class="input-group">
          <input [(ngModel)]="keySubCategory"
                 [typeahead]="subCategories"
                 (typeaheadLoading)="loadSubCategories()"
                 (typeaheadOnSelect)="setSelectedSubCategory($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="subCategoryName"
                 placeholder="Search Sub Category"
                 autocomplete="off"
                 class="form-control form-control-lg" name="subCategory">
          <span class="input-group-append">
                  <button class="btn btn-primary btn-lg fa fa-plus" (click)="openModalSubCategory()"
                          type="button"></button>
                  </span>
        </div>
        <tag-input [(ngModel)]="selectedSubCategory"
                   [identifyBy]="'id'"
                   [displayBy]="'subCategoryName'" name="subCategoryList" [hideForm]="true"
                   [placeholder]="''" [inputClass]="'form-control-lg'" [ripple]="false"
                   [animationDuration]="{enter: '0ms', leave: '0ms'}"></tag-input>
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Unit</label>
        <div class="input-group">
          <input [(ngModel)]="keyUOM"
                 [typeahead]="uoms"
                 (typeaheadLoading)="loadUoms()"
                 (typeaheadOnSelect)="setSelectedUom($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="Search Unit"
                 autocomplete="off"
                 class="form-control form-control-lg" name="uom">
          <span class="input-group-append">
                  <button class="btn btn-primary btn-lg fa fa-plus" (click)="openModalUom()"
                          type="button"></button>
                  </span>
        </div>
        <tag-input [(ngModel)]="selectedUom"
                   [identifyBy]="'id'"
                   [displayBy]="'name'" name="uomList" [hideForm]="true"
                   [placeholder]="''" [inputClass]="'form-control-lg'" [ripple]="false"
                   [animationDuration]="{enter: '0ms', leave: '0ms'}"></tag-input>
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Rack</label>
        <div class="input-group">
          <input [(ngModel)]="keyRack"
                 [typeahead]="racks"
                 (typeaheadLoading)="loadRacks()"
                 (typeaheadOnSelect)="setSelectedRack($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="rackNo"
                 placeholder="Search Rack"
                 autocomplete="off"
                 class="form-control form-control-lg" name="rack">
          <span class="input-group-append">
                  <button class="btn btn-primary btn-lg fa fa-plus" (click)="openModalRack()"
                          type="button"></button>
                  </span>
        </div>
        <tag-input [(ngModel)]="selectedRack"
                   [identifyBy]="'id'"
                   [displayBy]="'rackNo'" name="rackList" [hideForm]="true"
                   [placeholder]="''" [inputClass]="'form-control-lg'" [ripple]="false"
                   [animationDuration]="{enter: '0ms', leave: '0ms'}"></tag-input>
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3" *ngIf="!isService">
        <label class="form-label fw-bold">Item Cost</label>
        <input type="number" #itemCost="ngModel" class="form-control form-control-lg" id="itemCost"
               [(ngModel)]="item.itemCost"
               name="itemCost" (ngModelChange)="setSellingPrice()" required
               placeholder="Selling Price" [class.is-invalid]="itemCost.invalid && itemCost.touched">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3" *ngIf="!isService">
        <label class="form-label fw-bold">Supplier Discount</label>
        <input type="number" #itemCostPercentage="ngModel"
               class="form-control form-control-lg" id="itemCostPercentage" [(ngModel)]="discount"
               (ngModelChange)="setSellingPrice()"
               name="itemCostPercentage">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3"><label class="form-label fw-bold">Selling Price</label>
        <input type="number" #sellingPrice="ngModel" required
               class="form-control form-control-lg" id="sellingPrice" [(ngModel)]="item.sellingPrice"
               name="sellingPrice"
               placeholder="Selling Price" [class.is-invalid]="sellingPrice.invalid && sellingPrice.touched">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Customer Discount (% or -)</label>
        <input type="number" #customerDiscount="ngModel"
               class="form-control form-control-lg" id="customerDiscount" [(ngModel)]="item.retailDiscount"
               name="customerDiscount">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Initial Quantity</label>
        <input type="number" #stockInHand="ngModel"
               class="form-control form-control-lg" id="stockInHand" [(ngModel)]="item.quantity" name="Quantity"
               placeholder="Available Quantity">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Dead Stock Level</label>
        <input type="number" #dStockLevel="ngModel" (keyup.enter)="saveItem(createItemForm)"
               class="form-control form-control-lg" id="dStockLevel" [(ngModel)]="item.deadStockLevel"
               name="dStockLevel"
               placeholder="Dead Stock Level">
      </div>
      <div class="form-group col-12 col-sm-6 col-md-3">
        <label class="form-label fw-bold">Supplier</label>
        <div class="input-group">
          <input [(ngModel)]="keySupplier"
                 [typeahead]="suppliers"
                 (typeaheadLoading)="loadSuppliers()"
                 (typeaheadOnSelect)="setSelectedSupplier($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="Search Supplier"
                 autocomplete="off"
                 class="form-control form-control-lg" name="name">
          <span class="input-group-append">
                  <button class="btn btn-primary btn-lg fa fa-plus" (click)="openModalSupplier()"
                          type="button"></button>
                  </span>
        </div>
        <tag-input [(ngModel)]="selectedSupplier"
                   [identifyBy]="'id'"
                   [displayBy]="'name'" name="supList" [hideForm]="true"
                   [placeholder]="''" [inputClass]="'form-control-lg'" [ripple]="false"
                   [animationDuration]="{enter: '0ms', leave: '0ms'}"></tag-input>
      </div>
      <div class="form-group col-12 checkbox-container">
        <label class="form-label fw-bold">Options</label>
        <div class="d-flex flex-wrap">
          <div class="form-check form-check-inline checkbox me-4 mb-3">
            <input class="form-check-input form-check-input-lg" id="check2" name="check2" type="checkbox"
                   [(ngModel)]="isManageStock">
            <label class="form-check-label fs-5" for="check2">Manage Stock</label>
          </div>
          <div class="form-check form-check-inline checkbox me-4 mb-3">
            <input class="form-check-input form-check-input-lg" id="check3" name="check3" type="checkbox"
                   [(ngModel)]="isActive">
            <label class="form-check-label fs-5" for="check3">Active</label>
          </div>
          <div class="form-check form-check-inline checkbox me-4 mb-3">
            <input class="form-check-input form-check-input-lg" id="check4" name="check4" type="checkbox"
                   [(ngModel)]="isWholesale">
            <label class="form-check-label fs-5" for="check4">Wholesale</label>
          </div>
          <div class="form-check form-check-inline checkbox me-4 mb-3">
            <input class="form-check-input form-check-input-lg" id="check5" name="check5" type="checkbox"
                   [(ngModel)]="isRetail">
            <label class="form-check-label fs-5" for="check5">Retail</label>
          </div>
          <!-- Manage Serial Checkbox -->
          <div class="form-check form-check-inline checkbox me-4 mb-3">
            <input class="form-check-input form-check-input-lg" id="manageSerial" name="manageSerial" type="checkbox"
                   [(ngModel)]="item.manageSerial">
            <label class="form-check-label fs-5" for="manageSerial">Manage Serials</label>
          </div>
        </div>
      </div>

    </div>
    <div class="row mt-4 form-buttons">
      <div class="col-12 col-md-2 mb-3 mb-md-0">
        <button class="btn btn-warning h-100 w-100" type="button" (click)="openModal(templateDiscountCal)">
          Calc Discounts
        </button>
      </div>
      <div class="col-12 col-md-10 d-flex flex-column flex-sm-row justify-content-end">
        <button class="btn btn-warning me-2 mb-2 mb-sm-0" type="button" (click)="clearForm()">Clear</button>
        <button class="btn btn-primary me-2 mb-2 mb-sm-0" [disabled]="(!createItemForm.form.valid || itemAvailable)"
                type="button" (click)="saveAndBarcode(createItemForm)">Save and Barcode</button>
        <button class="btn btn-primary" [disabled]="( !createItemForm.form.valid || itemAvailable )">Save</button>
      </div>
    </div>
  </form>
</div>

<ng-template #templateDiscountCal>
  <div class="modal-header">
    <h5 class="modal-title font-weight-bold">Discount Calculator</h5>
    <button type="button" class="close pull-right" aria-label="Close" (click)="discountModalRef?.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body p-3 p-sm-2">
    <div class="form-group mb-3">
      <label for="sellingPriceDisc" class="form-label fw-bold">Selling Price</label>
      <input type="number" #sellingPrice="ngModel"
             class="form-control form-control-lg" id="sellingPriceDisc" [(ngModel)]="item.sellingPrice"
             name="sellingPrice"
             placeholder="Enter Selling Price">
    </div>
    <div class="form-group mb-3">
      <label for="level1Disc" class="form-label fw-bold">Discount Level 1</label>
      <input type="number" #discLevel1="ngModel" (input)="discCal()"
             class="form-control form-control-lg" id="level1Disc" [(ngModel)]="level1Disc" name="level1Disc"
             placeholder="Enter Level 1 Discount">
    </div>
    <div class="form-group mb-3">
      <label for="level2Disc" class="form-label fw-bold">Discount Level 2</label>
      <input type="number" #discLevel2="ngModel" (input)="discCal()"
             class="form-control form-control-lg" id="level2Disc" [(ngModel)]="level2Disc" name="level2Disc"
             placeholder="Enter Level 2 Discount">
    </div>
    <div class="form-group mb-3">
      <label for="level3Disc" class="form-label fw-bold">Discount Level 3</label>
      <input type="number" #discLevel3="ngModel" (input)="discCal()"
             class="form-control form-control-lg" id="level3Disc" [(ngModel)]="level3Disc" name="level3Disc"
             placeholder="Enter Level 3 Discount">
    </div>
    <div class="form-group mb-4">
      <label for="itemCostDisc" class="form-label fw-bold">Item Cost</label>
      <input type="number" #itemCost="ngModel"
             class="form-control form-control-lg" id="itemCostDisc" [(ngModel)]="item.itemCost" name="itemCost"
             placeholder="Enter Item Cost">
    </div>
    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
      <button class="btn btn-primary btn-lg px-4" (click)="setDiscount()">Set Discount</button>
    </div>
  </div>
</ng-template>
