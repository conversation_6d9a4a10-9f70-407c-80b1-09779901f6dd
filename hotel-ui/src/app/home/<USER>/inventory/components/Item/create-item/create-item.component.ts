import {Component, OnInit, TemplateRef} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {UomService} from '../../../service/uom.service';
import {SubCategory} from '../../../model/sub-category';
import {ItemType} from '../../../model/item-type';
import {ItemService} from '../../../service/item.service';
import {ItemCategory} from '../../../model/item-category';
import {Brand} from '../../../model/brand';
import {Item} from '../../../model/item';
import {Response} from '../../../../../core/model/response';
import {BrandService} from '../../../service/brand.service';
import {ItemTypeService} from '../../../service/item-type.service';
import {SubCategoryService} from '../../../service/sub-category.service';
import {ItemCategoryService} from '../../../service/item-category.service';
import {ActivatedRoute} from '@angular/router';
import {RackService} from '../../../service/rack.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {NgForm} from '@angular/forms';
import {BarcodeComponent} from '../../barcode/barcode.component';

import {UOM} from "../../../model/uom";
import {UOMComponent} from "../../uom/uom.component";
import {Rack} from "../../../model/rack";
import {Supplier} from "../../../../trade/model/supplier";
import {BrandComponent} from "../../brand/brand.component";
import {SubCategoryComponent} from "../../sub-category/sub-category.component";
import {SupplierComponent} from "../../../../trade/component/supplier/supplier.component";
import {RackComponent} from "../../rack/rack.component";
import {SupplierService} from "../../../../trade/service/supplier.service";
import {ItemCategoryComponent} from "../item-category/item-category.component";
import {BarcodeScannerComponent} from "../../barcode-scanner/barcode-scanner.component";

@Component({
  selector: 'app-create-item',
  templateUrl: './create-item.component.html',
  styleUrls: ['./create-item.component.css']
})

export class CreateItemComponent implements OnInit {

  keyUOM: string;
  keyBrand: string;
  keyItemSearch: string;
  keySubCategory: string;
  keyItemCategory: string;
  keyRack: string;
  keySupplier: string;

  itemModalRef: BsModalRef;
  barcodeModalRef: BsModalRef;
  discountModalRef: BsModalRef;
  item: Item = new Item();
  itemSearched: Array<Item> = [];

  selectedItemCategory: Array<ItemCategory> = [];
  selectedBrand: Array<Brand> = [];
  selectedSubCategory: Array<SubCategory> = [];
  selectedUom: Array<UOM> = [];
  selectedRack: Array<Rack> = [];
  selectedSupplier: Array<Supplier> = [];

  itemTypes: Array<ItemType> = [];
  categories: Array<ItemCategory> = [];
  subCategories: Array<SubCategory> = [];
  items: Array<Item> = [];
  brands: Array<Brand> = [];
  uoms: Array<UOM> = [];
  racks: Array<Rack> = [];
  suppliers: Array<Supplier> = [];

  itemCategory: ItemCategory;
  code: string;
  rackId: string;
  itemTypeId: string;
  isService: boolean;
  itemAvailable: boolean;
  isEditing = false;
  discount: number;
  isActive: boolean;
  isManageStock: boolean;
  isWholesale: boolean;
  isRetail: boolean;

  level1Disc: number;
  level2Disc: number;
  level3Disc: number;



  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  constructor(private uomService: UomService,
              private itemService: ItemService,
              private brandService: BrandService,
              private modalService: BsModalService,
              private itemTypeService: ItemTypeService,
              private activatedRoute: ActivatedRoute,
              private subCategoryService: SubCategoryService,
              private notificationService: NotificationService,
              private itemCategoryService: ItemCategoryService,
              private rackService: RackService,
              private supplierService: SupplierService) {
  }

  ngOnInit() {
    this.item = new Item();
    this.item.itemType = new ItemType();
    this.item.sellingPrice = 0;
    this.item.itemCost = 0;
    this.itemCategory = new ItemCategory();
    this.isActive = true;
    this.isManageStock = true;
    this.isWholesale = false;
    this.isRetail = true;

    // Initialize manage serial for phone shops
    this.item.manageSerial = false;

    this.loadItemTypes();

    // Note: isModal is set via initialState when opened as a modal
    // We don't need to set it here as it's already set by the component that opens this modal
  }

  openModalUom() {
    const initialState = {
      isModal: true
    };
    const uomModalRef = this.modalService.show(UOMComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: initialState
    });
    // Pass the modalRef to the UOM component so it can close itself
    if (uomModalRef && uomModalRef.content) {
      uomModalRef.content.setModalRef(uomModalRef);
    }
  }

  openModalRack() {
    const initialState = {
      isModal: true
    };
    const rackModalRef = this.modalService.show(RackComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: initialState
    });
    // Pass the modalRef to the Rack component so it can close itself
    if (rackModalRef && rackModalRef.content) {
      rackModalRef.content.setModalRef(rackModalRef);
    }
  }

  openModalBrand() {
    const initialState = {
      isModal: true
    };
    const brandModalRef = this.modalService.show(BrandComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: initialState
    });
    // Pass the modalRef to the brand component so it can close itself
    if (brandModalRef && brandModalRef.content) {
      brandModalRef.content.setModalRef(brandModalRef);
    }
  }

  openModalSubCategory() {
    const initialState = {
      isModal: true
    };
    const subCategoryModalRef = this.modalService.show(SubCategoryComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: initialState
    });
    // Pass the modalRef to the SubCategory component so it can close itself
    if (subCategoryModalRef && subCategoryModalRef.content) {
      subCategoryModalRef.content.setModalRef(subCategoryModalRef);
    }
  }

  openModalSupplier() {
    const initialState = {
      isModal: true
    };
    const supplierModalRef = this.modalService.show(SupplierComponent, <ModalOptions>{
      class: 'modal-xl',
      initialState: initialState
    });
    // Pass the modalRef to the Supplier component so it can close itself
    if (supplierModalRef && supplierModalRef.content) {
      supplierModalRef.content.setModalRef(supplierModalRef);
    }
  }

  openModalCategory() {
    const initialState = {
      isModal: true
    };
    const categoryModalRef = this.modalService.show(ItemCategoryComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: initialState
    });
    // Pass the modalRef to the ItemCategory component so it can close itself
    if (categoryModalRef && categoryModalRef.content) {
      categoryModalRef.content.setModalRef(categoryModalRef);
    }
  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      this.brands = data;
    });
  }

  loadSubCategories() {
    if (this.keySubCategory && this.keySubCategory.trim().length > 0) {
      this.subCategoryService.findByName(this.keySubCategory).subscribe((data: Array<SubCategory>) => {
        this.subCategories = data || [];
      }, error => {
        console.error('Error loading subcategories:', error);
        this.subCategories = [];
      });
    } else {
      this.subCategories = [];
    }
  }

  loadUoms() {
    this.uomService.findByName(this.keyUOM).subscribe((data: Array<UOM>) => {
      this.uoms = data;
    });
  }

  loadRacks() {
    this.rackService.findByRackNo(this.keyRack).subscribe((data: Array<Rack>) => {
      this.racks = data;
    });
  }

  loadSuppliers() {
    this.supplierService.findByNameLike(this.keySupplier).subscribe((data: Array<Supplier>) => {
      this.suppliers = data;
    });
  }

  loadItemCategories() {
    this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      this.categories = data;
    });
  }

  setSelectedBrand(event) {
    this.selectedBrand = [event.item];
  }

  setSelectedSubCategory(event) {
    this.selectedSubCategory = [event.item];
  }

  setSelectedUom(event) {
    this.selectedUom = [event.item];
  }

  setSelectedRack(event) {
    this.selectedRack = [event.item];
  }

  setSelectedSupplier(event) {
    this.selectedSupplier = [event.item];
  }

  setSelectedItemCategory(event) {
    this.selectedItemCategory = [event.item];
  }

  //  Item Type
  loadItemTypes() {
    this.itemTypeService.findAllForSelect().subscribe((data: Array<ItemType>) => {
      this.itemTypes = data;
    });
  }

  setSelectedItemType(event) {
    this.item.itemType = new ItemType();
    this.item.itemType.id = event.target.value;
    let serviceId = '';
    for (const index in this.itemTypes) {
      if (this.itemTypes[index].name === 'Service') {
        serviceId = this.itemTypes[index].id;
        break;
      }
    }
    if (serviceId === event.target.value) {
      this.isService = true;
    } else {
      this.isService = false;
    }
  }

  //  Item
  saveItem(form: NgForm) {
    try {
      if (this.selectedBrand.length > 0) {
        this.item.brand = new Brand();
        this.item.brand.id = this.selectedBrand[0].id;
        this.item.brand.code = this.selectedBrand[0].code;
      }
      if (this.selectedItemCategory.length > 0) {
        this.item.itemCategory = new ItemCategory();
        this.item.itemCategory.id = this.selectedItemCategory[0].id;
        this.item.itemCategory.code = this.selectedItemCategory[0].code;
      }
      if (this.selectedSupplier.length > 0) {
        this.item.supplier = new Supplier();
        this.item.supplier.id = this.selectedSupplier[0].id;
      }
      if (this.selectedSubCategory.length > 0) {
        this.item.subCategory = new SubCategory();
        this.item.subCategory.id = this.selectedSubCategory[0].id;
      }
      if (this.selectedUom.length > 0) {
        this.item.uom = new UOM();
        this.item.uom.id = this.selectedUom[0].id;
      }
      if (this.selectedRack.length > 0) {
        this.item.rack = new Rack();
        this.item.rack.id = this.selectedRack[0].id;
      }

      this.item.active = this.isActive;
      this.item.manageStock = this.isManageStock;
      this.item.wholesale = this.isWholesale;
      this.item.retail = this.isRetail;
      this.item.sellingPrice = Math.round(this.item.sellingPrice * 100) / 100;
      this.item.itemCost = Math.round(this.item.itemCost * 100) / 100;
      this.itemService.save(this.item).subscribe((result: Response) => {
        if (result.code === 200) {
          this.notificationService.showSuccess(result.message);
          if (this.isEditing) {
            this.itemModalRef.hide();
          }
          form.resetForm();
          this.clearForm();
        } else {
          this.notificationService.showError(result.message);
        }
      });
    } catch (error) {
      this.notificationService.showError('Please enter required data');
    }
  }

  saveAndBarcode(form: NgForm) {
    const tempItemCode = this.item.barcode;
    this.saveItem(form);
    this.barcodeModalRef = this.modalService.show(BarcodeComponent, {class: 'modal-md'} as ModalOptions);
    this.barcodeModalRef.content.barcode = tempItemCode;
    this.barcodeModalRef.content.itemName = this.item.itemName;
    this.barcodeModalRef.content.itemCode = this.item.itemCode;
    this.barcodeModalRef.content.price = this.item.sellingPrice;
    this.barcodeModalRef.content.itemCost = this.item.itemCost || 0; // Pass item cost for cost code conversion
  }

  checkItemAvailable() {
    this.itemService.checkAvailabilityByBarcode(this.item.barcode).subscribe((data: boolean) => {
      this.itemAvailable = data;
    });
  }

  clearForm() {
    this.selectedBrand = [];
    this.selectedItemCategory = [];
    this.selectedSubCategory = [];
    this.selectedUom = [];
    this.selectedRack = [];
    this.selectedSupplier = [];
    this.keyBrand = '';
    this.keySubCategory = '';
    this.keyUOM = '';
    this.keyRack = '';
    this.keySupplier = '';
    this.itemTypeId = '';
    this.keyItemSearch = '';
    this.keyItemCategory = '';
    this.itemSearched = [];
    this.itemAvailable = false;
    this.isEditing = false;
    this.subCategories = [];
    this.brands = [];
    this.uoms = [];
    this.racks = [];
    this.suppliers = [];
    this.ngOnInit();
  }

  //  confirmation functions --------------------------------------------------------

  confirmItemUpdate() {
    if (this.isEditing) {
      this.itemModalRef.hide();
      this.itemService.save(this.item).subscribe((result) => {
        if (result.status === 200) {
          this.notificationService.showSuccess(result.message);
          this.clearForm();
        } else {
          this.notificationService.showError(result.message);

        }
        this.ngOnInit();
        this.clearForm();
      });
    }
  }

  setSellingPrice() {
    if (this.discount) {
      this.item.sellingPrice = this.item.itemCost + (this.item.itemCost * (this.discount / 100));
    }
  }

  /**
   * Open barcode scanner modal
   */
  openBarcodeScanner() {
    const modalOptions: ModalOptions = {
      class: 'modal-md',
    };
    const modalRef = this.modalService.show(BarcodeScannerComponent, modalOptions);

    // Subscribe to the barcodeScanned event from the modal component
    modalRef.content.barcodeScanned.subscribe((barcode: string) => {
      if (barcode) {
        this.item.barcode = barcode;
        // Check if the item with this barcode already exists
        this.checkItemAvailable();
      }
    });
  }

  openModal(template: TemplateRef<any>) {
    this.discountModalRef = this.modalService.show(template, {class: 'modal-md'});
    // This is a template modal, not a component modal, so no need to set isModal
  }

  discCal() {
    if (this.level1Disc > 0) {
      this.item.itemCost = this.item.sellingPrice - (this.item.sellingPrice * this.level1Disc / 100);
    }
    if (this.level2Disc > 0) {
      this.item.itemCost = this.item.itemCost - (this.item.itemCost * this.level2Disc / 100);
    }
    if (this.level3Disc > 0) {
      this.item.itemCost = this.item.itemCost - (this.item.itemCost * this.level3Disc / 100);
    }
  }

  setDiscount() {
    this.discountModalRef.hide();
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    // First try using the itemModalRef (set when opened from ViewAllItemsComponent)
    if (this.itemModalRef) {
      this.itemModalRef.hide();
    }
  }

}
