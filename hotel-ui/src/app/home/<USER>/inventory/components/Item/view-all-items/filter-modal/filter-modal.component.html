<div class="modal-header">
  <h4 class="modal-title pull-left">Filter & Sort Items</h4>
  <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form [formGroup]="filterForm">
    <!-- Loading indicator -->
    <div *ngIf="loading" class="text-center mb-3">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <p class="mt-2">Loading data...</p>
    </div>

    <h5>Filter Options</h5>

    <!-- Model Filter -->
    <div class="form-group">
      <label for="modelId">Model</label>
      <input id="modelId"
             type="text"
             class="form-control"
             [(ngModel)]="keyModel"
             [typeahead]="models"
             (typeaheadLoading)="loadModels()"
             (typeaheadOnSelect)="setSelectedModel($event)"
             [typeaheadOptionsLimit]="7"
             typeaheadWaitMs="500"
             typeaheadOptionField="name"
             placeholder="Select Model"
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>

    <!-- Supplier Filter -->
    <div class="form-group">
      <label for="supplierId">Supplier</label>
      <input id="supplierId"
             type="text"
             class="form-control"
             [(ngModel)]="keySupplier"
             [typeahead]="suppliers"
             (typeaheadLoading)="loadSuppliers()"
             (typeaheadOnSelect)="setSelectedSupplier($event)"
             [typeaheadOptionsLimit]="7"
             typeaheadWaitMs="500"
             typeaheadOptionField="name"
             placeholder="Select Supplier"
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>

    <h5 class="mt-4">Item Properties</h5>

    <!-- Item Properties Filters -->
    <div class="row">
      <!-- Wholesale Filter -->
      <div class="col-6">
        <label for="wholesale">Wholesale</label>
        <select class="form-control" id="wholesale" formControlName="wholesale">
          <option [ngValue]="null">-- Any --</option>
          <option [ngValue]="true">Yes</option>
          <option [ngValue]="false">No</option>
        </select>
      </div>

      <!-- Retail Filter -->
      <div class="col-6">
        <label for="retail">Retail</label>
        <select class="form-control" id="retail" formControlName="retail">
          <option [ngValue]="null">-- Any --</option>
          <option [ngValue]="true">Yes</option>
          <option [ngValue]="false">No</option>
        </select>
      </div>

      <!-- Manage Stock Filter -->
      <div class="col-6 mt-3">
        <label for="manageStock">Manage Stock</label>
        <select class="form-control" id="manageStock" formControlName="manageStock">
          <option [ngValue]="null">-- Any --</option>
          <option [ngValue]="true">Yes</option>
          <option [ngValue]="false">No</option>
        </select>
      </div>

      <!-- Active Filter -->
      <div class="col-6 mt-3">
        <label for="active">Active</label>
        <select class="form-control" id="active" formControlName="active">
          <option [ngValue]="null">-- Any --</option>
          <option [ngValue]="true">Yes</option>
          <option [ngValue]="false">No</option>
        </select>
      </div>
    </div>

    <h5 class="mt-4">Sort & Group Options</h5>

    <!-- Sort By -->
    <div class="form-group">
      <label for="sortBy">Sort By</label>
      <select class="form-control" id="sortBy" formControlName="sortBy">
        <option *ngFor="let option of sortOptions" [value]="option.value">{{ option.label }}</option>
      </select>
    </div>

    <!-- Sort Direction -->
    <div class="form-group">
      <label for="sortDirection">Sort Direction</label>
      <select class="form-control" id="sortDirection" formControlName="sortDirection">
        <option *ngFor="let direction of sortDirections" [value]="direction.value">{{ direction.label }}</option>
      </select>
    </div>

    <!-- Group By -->
    <div class="form-group">
      <label for="groupBy">Group By</label>
      <select class="form-control" id="groupBy" formControlName="groupBy">
        <option *ngFor="let option of groupByOptions" [value]="option.value">{{ option.label }}</option>
      </select>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-outline-secondary" (click)="clearFilters()">Clear</button>
  <button type="button" class="btn btn-primary ml-2" (click)="applyFilters()">Apply Filters</button>
</div>
