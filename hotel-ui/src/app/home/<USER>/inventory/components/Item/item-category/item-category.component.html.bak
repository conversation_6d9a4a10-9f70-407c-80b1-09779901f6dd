<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">{{ 'ITEM_CATEGORY.TITLE' | translate }}</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>  <div class="row">
        <div class="col-md-6">
          <div class="form-group">
          <div class="input-group mb-2">

            <input [(ngModel)]="keyItemCategory"
                   [typeahead]="itemCategoriesSearched"
                   (typeaheadLoading)="loadItemCategories()"
                   (typeaheadOnSelect)="setSelectedItemCategory($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="categoryName"
                   placeholder="{{ 'ITEM_CATEGORY.SEARCH_CATEGORY' | translate }}"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="category">
          </div>
          </div>

          <table class="table table-hover">
            <thead>
            <tr>
              <th>{{ 'ITEM_CATEGORY.CATEGORY_NAME' | translate }}</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let itemCat of itemCategories"
                (click)="onSelect(itemCat)" [class.active]="itemCat === selectedCategory">
              <td>{{itemCat.categoryName}}</td>
            </tr>
            </tbody>
          </table>

          <div class="row">
            <div class="col-xs-12 col-12">
              <pagination class="pagination-sm justify-content-center"
                [totalItems]="collectionSize"
                [(ngModel)]="page"
                [boundaryLinks]="true"
                [maxSize]="10"
                (pageChanged)="pageChanged($event)">
              </pagination>
            </div>
          </div>


        </div>  <div class="col-md-6">
          <form (ngSubmit)="save()" #ManageItemCetogoryForm="ngForm">
            <label>{{ 'ITEM_CATEGORY.CATEGORY_NAME' | translate }}</label>
            <div class="form-group">
            <input type="text" required #itemCategoryName="ngModel"
                   class="form-control" id="itemCategoryName" [(ngModel)]="itemCategory.categoryName"
                   name="categoryName"
                   placeholder="{{ 'ITEM_CATEGORY.CATEGORY_NAME' | translate }}">
            <div *ngIf="itemCategoryName.errors && (itemCategoryName.invalid || itemCategoryName.touched)">
              <small class="text-danger" [class.d-none]="itemCategoryName.valid || itemCategoryName.untouched">{{ 'ITEM_CATEGORY.CATEGORY_NAME_REQUIRED' | translate }}
              </small>
            </div>
            </div>

            <!--<label>Description</label>
            <div class="form-group">
            <textarea [(ngModel)]="itemCategory.description"
                      class="form-control" id="Description"  name="description"
                      placeholder="Description ">

              </textarea>
            <div *ngIf="Description.errors && (Description.invalid || Description.touched)">
              <small class="text-danger" [class.d-none]="Description.valid || Description.untouched">*Description is
                required
              </small>
            </div>
            </div>-->

            <div class="form-check checkbox mr-2">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="itemCategory.active" (change)="isCheckActivate($event)">
              <label class="form-check-label" for="check3">{{ 'ITEM_CATEGORY.ACTIVE' | translate }}</label>
            </div>  <div class="text-right">
              <button class="btn btn-theme mr-3" [disabled]="selectedCategory!=null||!ManageItemCetogoryForm.form.valid">
                {{ 'ITEM_CATEGORY.SAVE' | translate }}
              </button>
              <button type="button" class="btn btn-primary mr-3"
                      [disabled]="selectedCategory===null"
                      (click)="openModal(templateUpdate)">{{ 'ITEM_CATEGORY.UPDATE' | translate }}
              </button>
              <button type="button" (click)="clear()" class="btn btn-warning">{{ 'ITEM_CATEGORY.CLEAR' | translate }}</button>
            </div>
          </form>
        </div>
      </div>
</div>

<ng-template #templateUpdate>
  <div class="modal-body text-center ">
    <p>Do you want to confirm?</p>
    <button type="button" class="btn btn-default" (click)="confirmUpdate()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>



