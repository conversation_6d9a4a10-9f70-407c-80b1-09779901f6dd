import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ItemCategory} from '../../../model/item-category';
import {ItemCategoryService} from '../../../service/item-category.service';
import {NotificationService} from '../../../../../core/service/notification.service';

@Component({
  selector: 'app-item-category',
  templateUrl: './item-category.component.html',
  styleUrls: ['./item-category.component.css']
})

export class ItemCategoryComponent implements OnInit {

  currentUser = JSON.parse(localStorage.getItem('currentUser'));
  itemCategory: ItemCategory;
  keyItemCategory: string;
  itemCategories: Array<ItemCategory> = [];
  collectionSize;
  page;
  pageSize;
  selectedCategory: ItemCategory;
  modalRef: BsModalRef;
  itemCategoriesSearched: Array<ItemCategory> = [];

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  constructor(private itemCategoryService: ItemCategoryService, private notification: NotificationService,
              private modalService: BsModalService) {
  }

  openModal(template: TemplateRef<any>) {
    if (this.selectedCategory) {
      this.modalRef = this.modalService.show(template, {class: 'modal-sm'});
    } else {
      this.notification.showError('Please select a row!');
    }
  }


  confirmUpdate() {

    if (this.selectedCategory) {
      this.modalRef.hide();
      this.itemCategoryService.save(this.itemCategory).subscribe((result) => {
        this.notification.handleResponse(result, 'Item category saved successfully', 'Failed to save item category');
        this.ngOnInit();
      }, error => {
        this.notification.showError('Failed to save item category: ' + (error.message || 'Unknown error'));
        this.ngOnInit();
      });
    } else {
      this.notification.showError('Please select a row');
    }

  }

  decline(): void {
    this.modalRef.hide();
    this.ngOnInit();
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.selectedCategory = null;
    this.itemCategory = new ItemCategory();
    this.itemCategory.active = true;
    this.findAll();

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  save() {
    this.itemCategoryService.save(this.itemCategory).subscribe(result => {
      this.notification.showSuccess(result);
      this.ngOnInit();
      this.clear();
    });
  }

  findAll() {
    this.itemCategoryService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.itemCategories = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  clear() {
    this.ngOnInit();
    this.keyItemCategory = null;
  }

  onSelect(cat: ItemCategory) {
    this.selectedCategory = cat;
    this.itemCategory = this.selectedCategory;
  }

  loadItemCategories() {
    return this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.itemCategoriesSearched = data;
    });
  }

  setSelectedItemCategory(event) {
    this.itemCategory = event.item;
  }

  isCheckActivate(event){

  }

  /**
   * Closes the modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Sets the modalRef and updates the isModal flag
   * @param ref The modal reference
   */
  setModalRef(ref: BsModalRef) {
    this.modalRef = ref;
    this.isModal = true;
  }
}
