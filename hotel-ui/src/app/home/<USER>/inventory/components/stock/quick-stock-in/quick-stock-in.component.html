<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <h2 class="component-title">Quick Stock In</h2>
  <form #manualAddItemsForm="ngForm">
      <div class="row">
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-6">
              <label>Item</label>
              <div class="input-group">
                <input [(ngModel)]="itemName"
                       [typeahead]="items"
                       (typeaheadLoading)="loadItems()"
                       (typeaheadOnSelect)="setSelectedItem($event)"
                       [typeaheadOptionsLimit]="7"
                       typeaheadWaitMs="1000"
                       typeaheadOptionField="itemName"
                       placeholder="Search Item"
                       autocomplete="off"
                       size="16"
                       required
                       #name="ngModel"
                       [class.is-invalid]="name.invalid && name.touched"
                       class="form-control" name="name">
                <span class="input-group-append">
                  <button class="btn btn-primary fa fa-plus" (click)="itemModal.show()" type="button"></button>
                </span>
              </div>
            </div>

            <div class="col-md-6">
              <label>Search By Barcode</label>
              <div class="form-group">
                <div class="input-group">
                  <input [(ngModel)]="keyBarcode"
                         [typeahead]="items"
                         (typeaheadLoading)="loadItemByCode()"
                         (typeaheadOnSelect)="setSelectedItem($event)"
                         [typeaheadOptionsLimit]="7"
                         typeaheadWaitMs="1000"
                         typeaheadOptionField="barcode"
                         autocomplete="off"
                         placeholder="Select the Item"
                         type="text" required #code="ngModel" [class.is-invalid]="code.invalid && code.touched"
                         class="form-control" name="code" id="code">
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label>Buying Price</label>
                <input type="number" required #buyingPr="ngModel" [class.is-invalid]="buyingPr.invalid && buyingPr.touched"
                       class="form-control" name="buyingPr" id="buyingPr" [(ngModel)]="piRecord.itemCost">
                <small class="text-danger" [class.d-none]="buyingPr.valid || buyingPr.untouched">*Buying Price Required
                </small>
              </div>
            </div>

            <div class="col-md-6">
              <label>Percentage</label>
              <input type="text" #percentage="ngModel" (keyup)="setSellingPrice()"
                     [class.is-invalid]="percentage.invalid && percentage.touched"
                     class="form-control" name="percentage" id="percentage" [(ngModel)]="percentageValue">
              <small class="text-danger" [class.d-none]="percentage.valid || percentage.untouched">*Percentage Required
              </small>
            </div>

            <div class="col-md-6">
              <label>Selling Price</label>
              <input type="number" required #sprice="ngModel" (keyup)="setItemCost()"
                     [class.is-invalid]="sprice.invalid && sprice.touched"
                     class="form-control" id="sprice" name="sprice" [(ngModel)]="piRecord.sellingPrice">
              <small class="text-danger" [class.d-none]="sprice.valid || sprice.untouched">*Selling Price is Required
              </small>
            </div>

            <div class="col-md-6">
              <label>Quantity</label>
              <input type="number" required #quantity="ngModel"
                     [class.is-invalid]="quantity.invalid && quantity.touched"
                     class="form-control" name="quantity" id="quantity" [(ngModel)]="piRecord.quantity">
              <small class="text-danger" [class.d-none]="quantity.valid || quantity.untouched">*Quantity is Required
              </small>
            </div>

            <div class="col-md-6 form-group">
              <label>Warehouse</label>
              <select class="form-control" required #selectedWh="ngModel"
                      [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                      [(ngModel)]="selectedWarehouse">
                <option [ngValue]="">Select Warehouse</option>
                <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
              </select>
            </div>

          </div>

          <div class="col-md-12 mt-3 pr-0 text-right">
            <button type="button" (click)="addEntry()"
                    [disabled]="!manualAddItemsForm.form.valid" class="btn btn-primary"> Add Record
            </button>
            <button type="button" (click)="manualAddItemsForm.reset()" class="btn btn-warning ml-1">Clear
            </button>
          </div>
        </div>

        <div class="col-md-6 border-left">
          <div class="row ml-1 table-height">
            <table class="table table-striped">
              <thead>
              <tr>
                <th scope="col">Barcode</th>
                <th scope="col">Name</th>
                <th scope="col">Selling Price</th>
                <th scope="col">Quantity</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let entry of piRecords,let i=index"
                  (click)="selectEntry(entry,i)"
                  [class.active]="i === selectedRow">
                <td>{{entry.barcode}}</td>
                <td>{{entry.itemName}}</td>
                <td>{{entry.sellingPrice}}</td>
                <td>{{entry.quantity}}</td>
              </tr>
              </tbody>
            </table>
          </div>
          <div class="col-md-12 text-right">
            <button type="button" (click)="removeStockRecord();manualAddItemsForm.reset()"
                    class="btn btn-danger ml-2">Remove
            </button>
            <button type="button" (click)="saveStock(manualAddItemsForm)" class="btn btn-primary ml-1">Save
            </button>
          </div>
        </div>
      </div>
    </form>
</div>

<div bsModal #itemModal="bs-modal" class="modal">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title pull-left">Add Item</h4>
        <button type="button" class="close text-right" aria-label="Close" (click)="itemModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <app-create-item></app-create-item>
      </div>
  </div>
