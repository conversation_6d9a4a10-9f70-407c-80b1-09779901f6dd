<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">{{ 'UOM.TITLE' | translate }}</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <form (ngSubmit)="saveUOM(); UOMForm.reset()" #UOMForm="ngForm" >
    <div class="row">
      <div class="col-md-6">
        <input type="text" class="form-control" id="uom" name="uom" placeholder="{{ 'UOM.SEARCH_UNIT' | translate }}">
            <br>
            <table class="table">
              <thead>
              <tr style="text-align: center">
                <th scope="col">{{ 'UOM.UNIT_NAME' | translate }}</th>
                <th scope="col">{{ 'UOM.SYMBOL' | translate }}</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let uom of units,let i=index" (click)="uomDetail(uom,i) "  [class.active]="i === selectedRow">
                <td>{{uom.name}}</td>
                <td>{{uom.symbol}}</td>
              </tr>
              </tbody>
            </table>
            <div class="row">
              <div class="col-xs-12 col-12 ">
                <pagination class="pagination-sm justify-content-center"
                  [totalItems]="collectionSize"
                  [(ngModel)]="page"
                  [boundaryLinks]="true"
                  [maxSize]="10"
                  (pageChanged)="pageChanged($event)"
                  [ngModelOptions]="{standalone: true}">
                </pagination>
              </div>
            </div>

          </div>
          <div class="col-md-6">
            <div class="form-group">
            <label>{{ 'UOM.UNIT_NAME' | translate }}</label>
            <input type="text" required #UserName="ngModel" [class.is-invalid]="UserName.invalid && UserName.touched"
                   class="form-control" id="bName" [(ngModel)]="uom.name" name="bName"
                   placeholder="{{ 'UOM.UNIT_NAME' | translate }}">
            <div *ngIf="UserName.errors && (UserName.invalid || UserName.touched)">
              <small class="text-danger" [class.d-none]="UserName.valid || UserName.untouched">{{ 'UOM.UNIT_NAME_REQUIRED' | translate }}
              </small>
            </div>
            </div>

            <label>{{ 'UOM.SYMBOL' | translate }}</label>
            <div class="form-group">
            <input type="text" required #Symbol="ngModel" [class.is-invalid]="Symbol.invalid && Symbol.touched"
                   class="form-control" id="Symbol" [(ngModel)]="uom.symbol" name="uom" placeholder="{{ 'UOM.SYMBOL' | translate }}">
            <div *ngIf="Symbol.errors && (Symbol.invalid || Symbol.touched)">
              <small class="text-danger" [class.d-none]="Symbol.valid || Symbol.untouched">{{ 'UOM.SYMBOL_REQUIRED' | translate }}
              </small>
            </div>
            </div>

            <div class="form-check checkbox mr-2">
              <input class="form-check-input" id="check" name="check" type="checkbox" value=""
                     [(ngModel)]="uom.isWholeNumber">
              <label class="form-check-label" for="check">{{ 'UOM.IS_WHOLE_NUMBER' | translate }}</label>
            </div>
            <br>
            <div class="form-check checkbox mr-2">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="uom.active">
              <label class="form-check-label" for="check3">{{ 'UOM.ACTIVE' | translate }}</label>
            </div>
            <br>

            <div class="row text-right">
              <div class="col-md-12">
                  <button type="button" class="btn btn-warning mr-1" [disabled]="!UOMForm.form.valid"
                          (click)="clearAll()">{{ 'UOM.CLEAR' | translate }}</button>
                <button type="submit" class="btn btn-theme" [disabled]="!UOMForm.form.valid">{{ 'UOM.SAVE' | translate }}</button>
              </div>
            </div>
          </div>
        </div>
      </form>
</div>
