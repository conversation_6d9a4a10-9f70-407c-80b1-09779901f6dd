.manage-serial-numbers {
  position: relative;
}

.card-title {
  color: #495057;
  font-weight: 600;
}

.form-label {
  font-weight: 500;
  color: #495057;
}

.header-actions {
  display: flex;
  align-items: center;
}

.alert-info {
  background-color: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.badge-success {
  background-color: #28a745;
}

.badge-primary {
  background-color: #007bff;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-secondary {
  background-color: #6c757d;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-muted {
  color: #6c757d !important;
}

.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.btn-group .dropdown-menu {
  min-width: 180px;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: transparent;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.btn-outline-primary {
  color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning:hover {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.table-responsive {
  border-radius: 0.375rem;
}

.input-group-append .btn {
  border-left: 0;
}

.input-group .form-control:not(:last-child) {
  border-right: 0;
}

.input-group .form-control:focus {
  z-index: 3;
}

/* Status badges */
.badge {
  font-size: 0.75em;
  padding: 0.375em 0.5em;
  border-radius: 0.25rem;
}

/* Pagination styling */
.pagination {
  margin-bottom: 0;
}

.page-link {
  color: #007bff;
  border-color: #dee2e6;
}

.page-link:hover {
  color: #0056b3;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions .btn {
    margin-bottom: 0.5rem;
    margin-right: 0 !important;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
  
  .row > [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .btn-group {
    display: block;
    width: 100%;
  }
  
  .btn-group .btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .table th,
  .table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.8rem;
  }
  
  .badge {
    font-size: 0.7em;
  }
}
