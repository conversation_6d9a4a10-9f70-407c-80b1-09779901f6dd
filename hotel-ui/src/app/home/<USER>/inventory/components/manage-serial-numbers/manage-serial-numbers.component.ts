import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { SerialNumber } from '../../model/serial-number';
import { SerialNumberService } from '../../service/serial-number.service';
import { Item } from '../../model/item';
import { ItemService } from '../../service/item.service';

@Component({
  selector: 'app-manage-serial-numbers',
  templateUrl: './manage-serial-numbers.component.html',
  styleUrls: ['./manage-serial-numbers.component.css']
})
export class ManageSerialNumbersComponent implements OnInit {

  // Component state
  loading: boolean = false;
  serialNumberList: SerialNumber[] = [];
  filteredSerialNumbers: SerialNumber[] = [];
  
  // Search data
  keyItemSearch: string = '';
  keyBarcodeSearch: string = '';
  serialSearchTerm: string = '';
  itemSearched: Item[] = [];
  barcodeSearched: Item[] = [];
  searchedSerial: SerialNumber = null;
  
  // Filters
  selectedStatus: string = 'ALL';
  searchTerm: string = '';
  
  // Pagination
  currentPage: number = 0;
  pageSize: number = 20;
  totalElements: number = 0;
  
  // Status options
  statusOptions = [
    { value: 'ALL', label: 'All Status' },
    { value: 'AVAILABLE', label: 'Available' },
    { value: 'SOLD', label: 'Sold' },
    { value: 'RETURNED', label: 'Returned' },
    { value: 'DAMAGED', label: 'Damaged' }
  ];

  constructor(
    private serialNumberService: SerialNumberService,
    private toastr: ToastrService,
    private itemService: ItemService
  ) { }

  ngOnInit(): void {
    this.loadAllSerialNumbers();
  }

  /**
   * Load all serial numbers with pagination
   */
  loadAllSerialNumbers(): void {
    this.loading = true;
    this.serialNumberService.findAllPaginated(this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.serialNumberList = response.content || [];
        this.totalElements = response.totalElements || 0;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading serial numbers:', error);
        this.toastr.error('Failed to load serial numbers');
        this.loading = false;
      }
    });
  }

  /**
   * Load items for search by name
   */
  loadItemsByName(): void {
    if (!this.keyItemSearch || this.keyItemSearch.trim() === '') {
      return;
    }
    
    this.itemService.findActiveByNameLikeForSerialManagement(this.keyItemSearch).subscribe({
      next: (items: Item[]) => {
        this.itemSearched = items;
      },
      error: (error) => {
        console.error('Error loading items by name:', error);
      }
    });
  }

  /**
   * Load items for search by barcode
   */
  loadItemsByBarcode(): void {
    if (!this.keyBarcodeSearch || this.keyBarcodeSearch.trim() === '') {
      return;
    }
    
    this.itemService.findActiveByBarcodeLikeForSerialManagement(this.keyBarcodeSearch).subscribe({
      next: (items: Item[]) => {
        this.barcodeSearched = items;
      },
      error: (error) => {
        console.error('Error loading items by barcode:', error);
      }
    });
  }

  /**
   * Handle item selection from name search
   */
  setSelectedItemFromName(event: any): void {
    this.loadSerialNumbersByItem(event.item.itemCode);
    this.keyItemSearch = '';
    this.keyBarcodeSearch = '';
  }

  /**
   * Handle item selection from barcode search
   */
  setSelectedItemFromBarcode(event: any): void {
    this.loadSerialNumbersByItem(event.item.itemCode);
    this.keyItemSearch = '';
    this.keyBarcodeSearch = '';
  }

  /**
   * Load serial numbers by item code
   */
  loadSerialNumbersByItem(itemCode: string): void {
    this.loading = true;
    this.serialNumberService.findByItemCodePaginated(itemCode, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.serialNumberList = response.content || [];
        this.totalElements = response.totalElements || 0;
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading serial numbers by item:', error);
        this.toastr.error('Failed to load serial numbers');
        this.loading = false;
      }
    });
  }

  /**
   * Search for serial number
   */
  searchSerial(): void {
    if (!this.serialSearchTerm || this.serialSearchTerm.trim() === '') {
      this.toastr.warning('Please enter a serial number to search');
      return;
    }

    this.loading = true;
    this.serialNumberService.findBySerialNumber(this.serialSearchTerm.trim()).subscribe({
      next: (serial: SerialNumber) => {
        this.searchedSerial = serial;
        this.loading = false;
        
        if (serial) {
          this.toastr.success(`Serial number found - Status: ${serial.status}`);
          // Show only this serial number
          this.serialNumberList = [serial];
          this.filteredSerialNumbers = [serial];
          this.totalElements = 1;
        } else {
          this.toastr.info('Serial number not found in system');
          this.searchedSerial = null;
        }
      },
      error: (error) => {
        console.error('Error searching serial:', error);
        this.toastr.error('Failed to search serial number');
        this.loading = false;
        this.searchedSerial = null;
      }
    });
  }

  /**
   * Clear serial search
   */
  clearSerialSearch(): void {
    this.serialSearchTerm = '';
    this.searchedSerial = null;
    this.loadAllSerialNumbers();
  }

  /**
   * Apply filters to serial numbers
   */
  applyFilters(): void {
    let filtered = [...this.serialNumberList];

    // Filter by status
    if (this.selectedStatus !== 'ALL') {
      filtered = filtered.filter(sn => sn.status === this.selectedStatus);
    }

    // Filter by search term
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(sn => 
        sn.serialNumber.toLowerCase().includes(term) ||
        sn.itemCode.toLowerCase().includes(term) ||
        (sn.notes && sn.notes.toLowerCase().includes(term)) ||
        (sn.customerName && sn.customerName.toLowerCase().includes(term)) ||
        (sn.customerNumber && sn.customerNumber.toLowerCase().includes(term))
      );
    }

    this.filteredSerialNumbers = filtered;
  }

  /**
   * Handle search input change
   */
  onSearchChange(): void {
    this.applyFilters();
  }

  /**
   * Handle status filter change
   */
  onStatusFilterChange(): void {
    this.applyFilters();
  }

  /**
   * Handle page change
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadAllSerialNumbers();
  }

  /**
   * Update serial number status
   */
  updateStatus(serialNumber: SerialNumber, newStatus: string): void {
    this.loading = true;
    this.serialNumberService.updateStatus(serialNumber.serialNumber, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastr.success('Status updated successfully');
          this.loadAllSerialNumbers();
        } else {
          this.toastr.error(response.message || 'Failed to update status');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error updating status:', error);
        this.toastr.error('Failed to update status');
        this.loading = false;
      }
    });
  }

  /**
   * Delete serial number
   */
  deleteSerialNumber(serialNumber: SerialNumber): void {
    if (!confirm('Are you sure you want to delete this serial number?')) {
      return;
    }

    this.loading = true;
    this.serialNumberService.delete(serialNumber.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastr.success('Serial number deleted successfully');
          this.loadAllSerialNumbers();
        } else {
          this.toastr.error(response.message || 'Failed to delete serial number');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error deleting serial number:', error);
        this.toastr.error('Failed to delete serial number');
        this.loading = false;
      }
    });
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'AVAILABLE':
        return 'badge badge-success';
      case 'SOLD':
        return 'badge badge-primary';
      case 'RETURNED':
        return 'badge badge-warning';
      case 'DAMAGED':
        return 'badge badge-danger';
      default:
        return 'badge badge-secondary';
    }
  }

  /**
   * Format date for display
   */
  formatDate(date: Date): string {
    if (!date) return '-';
    return new Date(date).toLocaleDateString();
  }

  /**
   * Check if warranty is valid
   */
  isWarrantyValid(serialNumber: SerialNumber): boolean {
    if (!serialNumber.warrantyExpiryDate) return false;
    return new Date(serialNumber.warrantyExpiryDate) > new Date();
  }

  /**
   * Get warranty status
   */
  getWarrantyStatus(serialNumber: SerialNumber): string {
    if (!serialNumber.warrantyExpiryDate) return 'No Warranty';
    
    const now = new Date();
    const expiry = new Date(serialNumber.warrantyExpiryDate);
    
    if (expiry < now) return 'Expired';
    
    const daysLeft = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    if (daysLeft <= 30) return `Expires in ${daysLeft} days`;
    
    return 'Valid';
  }

  /**
   * Get warranty status class
   */
  getWarrantyStatusClass(serialNumber: SerialNumber): string {
    const status = this.getWarrantyStatus(serialNumber);
    if (status === 'Expired') return 'text-danger';
    if (status.includes('Expires in')) return 'text-warning';
    if (status === 'Valid') return 'text-success';
    return 'text-muted';
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadAllSerialNumbers();
  }

  /**
   * Clear all searches and filters
   */
  clearAllFilters(): void {
    this.keyItemSearch = '';
    this.keyBarcodeSearch = '';
    this.serialSearchTerm = '';
    this.searchTerm = '';
    this.selectedStatus = 'ALL';
    this.searchedSerial = null;
    this.itemSearched = [];
    this.barcodeSearched = [];
    this.currentPage = 0;
    this.loadAllSerialNumbers();
  }
}
