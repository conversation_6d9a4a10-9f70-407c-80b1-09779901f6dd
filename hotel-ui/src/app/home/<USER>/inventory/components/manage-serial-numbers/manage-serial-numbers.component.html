<div class="manage-serial-numbers">
  <!-- Header -->
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="mb-0">
      <i class="fas fa-list mr-2"></i>
      Manage Serial Numbers
    </h5>
    <div class="header-actions">
      <button class="btn btn-outline-secondary btn-sm mr-2" (click)="refresh()" [disabled]="loading">
        <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
        Refresh
      </button>
      <button class="btn btn-outline-warning btn-sm" (click)="clearAllFilters()">
        <i class="fas fa-times mr-1"></i>Clear All
      </button>
    </div>
  </div>

  <!-- Search and Filter Section -->
  <div class="card mb-3">
    <div class="card-body">
      <h6 class="card-title">
        <i class="fas fa-search mr-2"></i>
        Search & Filter
      </h6>
      
      <!-- Item Search Row -->
      <div class="row mb-3">
        <div class="col-md-4">
          <label class="form-label">Search by Item Name</label>
          <input 
            [(ngModel)]="keyItemSearch"
            [typeahead]="itemSearched"
            (typeaheadLoading)="loadItemsByName()"
            (typeaheadOnSelect)="setSelectedItemFromName($event)"
            [typeaheadOptionsLimit]="15"
            typeaheadWaitMs="1000"
            typeaheadOptionField="itemName"
            placeholder="Search by item name"
            autocomplete="off"
            class="form-control" 
            name="searchItem">
        </div>
        <div class="col-md-4">
          <label class="form-label">Search by Barcode</label>
          <input 
            [(ngModel)]="keyBarcodeSearch"
            [typeahead]="barcodeSearched"
            (typeaheadLoading)="loadItemsByBarcode()"
            (typeaheadOnSelect)="setSelectedItemFromBarcode($event)"
            [typeaheadOptionsLimit]="15"
            typeaheadWaitMs="1000"
            typeaheadOptionField="barcode"
            placeholder="Search by barcode"
            autocomplete="off"
            class="form-control" 
            name="searchBarcode">
        </div>
        <div class="col-md-4">
          <label class="form-label">Status Filter</label>
          <select 
            class="form-control" 
            [(ngModel)]="selectedStatus" 
            (change)="onStatusFilterChange()">
            <option *ngFor="let status of statusOptions" [value]="status.value">
              {{ status.label }}
            </option>
          </select>
        </div>
      </div>

      <!-- Serial Search Row -->
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">Search by Serial Number</label>
          <div class="input-group">
            <input 
              type="text" 
              class="form-control" 
              [(ngModel)]="serialSearchTerm"
              placeholder="Enter serial number to search"
              (keyup.enter)="searchSerial()">
            <div class="input-group-append">
              <button 
                class="btn btn-primary" 
                (click)="searchSerial()"
                [disabled]="loading || !serialSearchTerm">
                <i class="fas fa-search mr-1"></i>Search
              </button>
              <button 
                class="btn btn-secondary" 
                (click)="clearSerialSearch()"
                [disabled]="!serialSearchTerm && !searchedSerial">
                <i class="fas fa-times mr-1"></i>Clear
              </button>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <label class="form-label">General Search</label>
          <input 
            type="text" 
            class="form-control" 
            [(ngModel)]="searchTerm"
            (input)="onSearchChange()"
            placeholder="Search in serial numbers, item codes, notes, customer info...">
        </div>
      </div>

      <!-- Search Result Info -->
      <div *ngIf="searchedSerial" class="alert alert-info">
        <strong>Serial Search Result:</strong> {{ searchedSerial.serialNumber }}<br>
        <strong>Status:</strong> 
        <span [class]="getStatusBadgeClass(searchedSerial.status)">
          {{ searchedSerial.getStatusDisplayName() }}
        </span><br>
        <strong>Item:</strong> {{ searchedSerial.itemCode }}
        <span *ngIf="searchedSerial.customerName">
          <br><strong>Customer:</strong> {{ searchedSerial.customerName }}
          <span *ngIf="searchedSerial.customerNumber"> ({{ searchedSerial.customerNumber }})</span>
        </span>
      </div>
    </div>
  </div>

  <!-- Results Section -->
  <div class="card">
    <div class="card-body">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="card-title mb-0">
          <i class="fas fa-table mr-2"></i>
          Serial Numbers
          <span class="badge badge-secondary ml-2">{{ filteredSerialNumbers.length }}</span>
        </h6>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading serial numbers...</p>
      </div>

      <!-- No Results -->
      <div *ngIf="!loading && filteredSerialNumbers.length === 0" class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No Serial Numbers Found</h5>
        <p class="text-muted">Try adjusting your search criteria or filters.</p>
      </div>

      <!-- Results Table -->
      <div *ngIf="!loading && filteredSerialNumbers.length > 0" class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="thead-light">
            <tr>
              <th>Serial Number</th>
              <th>Item Code</th>
              <th>Status</th>
              <th>Customer</th>
              <th>Warranty</th>
              <th>Created Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let serial of filteredSerialNumbers">
              <td>
                <strong>{{ serial.serialNumber }}</strong>
                <div *ngIf="serial.notes" class="small text-muted">
                  <i class="fas fa-sticky-note mr-1"></i>{{ serial.notes }}
                </div>
              </td>
              <td>{{ serial.itemCode }}</td>
              <td>
                <span [class]="getStatusBadgeClass(serial.status)">
                  {{ serial.getStatusDisplayName() }}
                </span>
              </td>
              <td>
                <div *ngIf="serial.customerName; else noCustomer">
                  <strong>{{ serial.customerName }}</strong>
                  <div *ngIf="serial.customerNumber" class="small text-muted">
                    {{ serial.customerNumber }}
                  </div>
                </div>
                <ng-template #noCustomer>
                  <span class="text-muted">-</span>
                </ng-template>
              </td>
              <td>
                <span [class]="getWarrantyStatusClass(serial)">
                  {{ getWarrantyStatus(serial) }}
                </span>
                <div *ngIf="serial.warrantyExpiryDate" class="small text-muted">
                  Expires: {{ formatDate(serial.warrantyExpiryDate) }}
                </div>
              </td>
              <td>{{ formatDate(serial.createdDate) }}</td>
              <td>
                <div class="btn-group" role="group">
                  <button 
                    class="btn btn-sm btn-outline-primary dropdown-toggle" 
                    data-toggle="dropdown">
                    <i class="fas fa-cog"></i>
                  </button>
                  <div class="dropdown-menu">
                    <button 
                      class="dropdown-item" 
                      (click)="updateStatus(serial, 'AVAILABLE')"
                      [disabled]="serial.status === 'AVAILABLE'">
                      <i class="fas fa-check-circle text-success mr-2"></i>Mark Available
                    </button>
                    <button 
                      class="dropdown-item" 
                      (click)="updateStatus(serial, 'SOLD')"
                      [disabled]="serial.status === 'SOLD'">
                      <i class="fas fa-shopping-cart text-primary mr-2"></i>Mark Sold
                    </button>
                    <button 
                      class="dropdown-item" 
                      (click)="updateStatus(serial, 'RETURNED')"
                      [disabled]="serial.status === 'RETURNED'">
                      <i class="fas fa-undo text-warning mr-2"></i>Mark Returned
                    </button>
                    <button 
                      class="dropdown-item" 
                      (click)="updateStatus(serial, 'DAMAGED')"
                      [disabled]="serial.status === 'DAMAGED'">
                      <i class="fas fa-exclamation-triangle text-danger mr-2"></i>Mark Damaged
                    </button>
                    <div class="dropdown-divider"></div>
                    <button 
                      class="dropdown-item text-danger" 
                      (click)="deleteSerialNumber(serial)">
                      <i class="fas fa-trash mr-2"></i>Delete
                    </button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div *ngIf="!loading && totalElements > pageSize" class="d-flex justify-content-between align-items-center mt-3">
        <div class="text-muted">
          Showing {{ filteredSerialNumbers.length }} of {{ totalElements }} results
        </div>
        <nav>
          <ngb-pagination 
            [(page)]="currentPage" 
            [pageSize]="pageSize" 
            [collectionSize]="totalElements"
            (pageChange)="onPageChange($event)"
            [maxSize]="5"
            [rotate]="true">
          </ngb-pagination>
        </nav>
      </div>
    </div>
  </div>
</div>
