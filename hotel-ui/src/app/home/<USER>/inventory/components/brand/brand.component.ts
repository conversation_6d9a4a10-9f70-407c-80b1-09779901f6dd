import {Component, OnInit} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Brand} from '../../model/brand';
import {BrandService} from '../../service/brand.service';
import {NotificationService} from "../../../../core/service/notification.service";

@Component({
  selector: 'app-brand',
  templateUrl: './brand.component.html',
  styleUrls: ['./brand.component.css']
})


export class BrandComponent implements OnInit {

  modalRef: BsModalRef;

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  brand = new Brand();
  brands: Array<Brand> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyBrand: string;
  collectionSize;
  page;
  pageSize;


  constructor(private brandService: BrandService,
              private notificationService: NotificationService) {

  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.brand = new Brand();
    this.brand.active = true;
    this.findAll();

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  saveBrand() {
    this.brandService.save(this.brand).subscribe(result => {
      this.notificationService.handleResponse(result, 'Brand saved successfully', 'Failed to save brand');
      this.ngOnInit();
    }, error => {
      this.notificationService.showError('Failed to save brand: ' + (error.message || 'Unknown error'));
      console.log(error);
    });
  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  findAll() {
    this.brandService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.brands = data.content;
      this.collectionSize = data.totalPages * 8;
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  brandDetail(brand, index) {
    this.brand = brand;
    this.selectedRow = index;
  }

  setSelectedBrand(event) {
    this.brand = event.item;
  }

  updateBrand() {
    this.saveBrand();
  }

  clear() {
    this.brand = new Brand();
  }

  /**
   * Closes the modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Sets the modalRef and updates the isModal flag
   * @param ref The modal reference
   */
  setModalRef(ref: BsModalRef) {
    this.modalRef = ref;
    this.isModal = true;
  }
}
