import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NotificationService } from '../../../../core/service/notification.service';
import { BarcodeFormat, BrowserMultiFormatReader, DecodeHintType, Result } from '@zxing/library';

@Component({
  selector: 'app-barcode-scanner',
  templateUrl: './barcode-scanner.component.html',
  styleUrls: ['./barcode-scanner.component.css']
})
export class BarcodeScannerComponent implements OnInit, OnDestroy {
  @Output() barcodeScanned = new EventEmitter<string>();

  videoElement: HTMLVideoElement;
  codeReader: BrowserMultiFormatReader;
  selectedDeviceId: string;
  availableDevices: MediaDeviceInfo[] = [];
  hasDevices: boolean;
  hasPermission: boolean;
  isScanning: boolean = false;
  manualBarcode: string = '';
  isModal: boolean = false;

  constructor(
    public modalRef: BsModalRef,
    private notificationService: NotificationService
  ) {
    // Configure the barcode reader with hints
    const hints = new Map();
    const formats = [BarcodeFormat.CODE_128]; // Only use Code 128 as requested
    hints.set(DecodeHintType.POSSIBLE_FORMATS, formats);
    this.codeReader = new BrowserMultiFormatReader(hints);
  }

  ngOnInit(): void {
    this.initBarcodeScanner();
  }

  ngOnDestroy(): void {
    this.stopScanning();
  }

  private initBarcodeScanner(): void {
    // Check if the browser supports getUserMedia
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      this.notificationService.showError('Browser does not support camera access');
      return;
    }

    // First request camera permission
    navigator.mediaDevices.getUserMedia({ video: true })
      .then(stream => {
        // Stop the stream immediately, we just needed permission
        stream.getTracks().forEach(track => track.stop());

        // Now list available devices
        this.codeReader.listVideoInputDevices()
          .then(devices => {
            this.availableDevices = devices;
            this.hasDevices = Boolean(devices && devices.length > 0);

            if (this.hasDevices) {
              // Use the first device by default
              this.selectedDeviceId = devices[0].deviceId;
              this.startScanning();
            } else {
              this.notificationService.showWarning('No camera devices found');
            }
          })
          .catch(err => {
            this.notificationService.showError('Error listing cameras: ' + err);
          });
      })
      .catch(err => {
        this.notificationService.showError('Camera access denied or not available: ' + err);
      });
  }

  startScanning(): void {
    if (!this.selectedDeviceId) {
      // Try to get the default camera if none is selected
      if (this.availableDevices && this.availableDevices.length > 0) {
        this.selectedDeviceId = this.availableDevices[0].deviceId;
      } else {
        this.notificationService.showWarning('No camera available');
        return;
      }
    }

    this.isScanning = true;
    this.videoElement = document.getElementById('video-element') as HTMLVideoElement;

    if (!this.videoElement) {
      this.notificationService.showError('Video element not found');
      return;
    }

    try {
      this.codeReader.decodeFromVideoDevice(
        this.selectedDeviceId,
        this.videoElement,
        (result: Result) => {
          if (result) {
            // Emit the scanned barcode value
            this.barcodeScanned.emit(result.getText());
            this.notificationService.showSuccess('Barcode scanned successfully');

            // Close the modal after successful scan
            this.modalRef.hide();
          }
        }
      ).catch(err => {
        this.notificationService.showError('Error scanning: ' + err);
      });
    } catch (error) {
      this.notificationService.showError('Failed to start scanner: ' + error);
    }
  }

  stopScanning(): void {
    if (this.codeReader) {
      this.codeReader.reset();
      this.isScanning = false;
    }
  }

  onDeviceSelectChange(deviceId: string): void {
    this.selectedDeviceId = deviceId;
    this.stopScanning();
    this.startScanning();
  }

  close(): void {
    this.stopScanning();
    this.modalRef.hide();
  }

  submitManualBarcode(): void {
    if (this.manualBarcode && this.manualBarcode.trim() !== '') {
      // Emit the manually entered barcode
      this.barcodeScanned.emit(this.manualBarcode.trim());
      this.notificationService.showSuccess('Barcode submitted successfully');

      // Close the modal after submission
      this.modalRef.hide();
    } else {
      this.notificationService.showWarning('Please enter a valid barcode');
    }
  }
}
