import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {QuickStockInComponent} from './components/stock/quick-stock-in/quick-stock-in.component';
import {BrandComponent} from './components/brand/brand.component';
import {CreateItemComponent} from './components/Item/create-item/create-item.component';
import {ViewAllItemsComponent} from './components/Item/view-all-items/view-all-items.component';
import {ItemTypeComponent} from './components/Item/item-type/item-type.component';
import {ItemCategoryComponent} from './components/Item/item-category/item-category.component';
import {SubCategoryComponent} from './components/Item/sub-category/sub-category.component';
import {BarcodeComponent} from './components/barcode/barcode.component';
import {UOMComponent} from "./components/uom/uom.component";
import {RackComponent} from "./components/rack/rack.component";
import {ModelComponent} from "./components/model/model.component";
import {ViewStockComponent} from "./components/stock/view-stock/view-stock.component";
import {AdjustStockComponent} from "./components/stock/adjust-stock/adjust-stock.component";
import {TransferStockComponent} from "./components/stock/transfer-stock/transfer-stock.component";
import {WarehouseComponent} from "./components/warehouse/warehouse.component";
import {BarcodeScannerComponent} from "./components/barcode-scanner/barcode-scanner.component";
import {EditBarcodeComponent} from "./components/Item/edit-barcode/edit-barcode.component";
import {FilterModalComponent} from "./components/Item/view-all-items/filter-modal/filter-modal.component";
import {SupplierReturnComponent} from "./components/supplier-return/supplier-return.component";
import {ColumnSelectorComponent} from "./components/column-selector/column-selector.component";
import {PageSizeSelectorComponent} from "./components/page-size-selector/page-size-selector.component";
import {ManageSerialNumbersComponent} from "./components/manage-serial-numbers/manage-serial-numbers.component";
import {AddSerialNumbersComponent} from "./components/add-serial-numbers/add-serial-numbers.component";

const routes: Routes = [
  {
    path: 'quick_stock_in',
    component: QuickStockInComponent
  },
  {
    path: 'brand',
    component: BrandComponent,
  },
  {
    path: 'create_item',
    component: CreateItemComponent,
  },
  {
    path: 'item_details',
    component: ViewAllItemsComponent,
  },
  {
    path: 'view_main_stock',
    component: ViewStockComponent,
  },
  {
    path: 'item_type',
    component: ItemTypeComponent,
  },
  {
    path: 'item_category',
    component: ItemCategoryComponent,
  },
  {
    path: 'uom',
    component: UOMComponent
  },
  {
    path: 'rack',
    component: RackComponent
  },
  {
    path: 'model',
    component: ModelComponent
  },
  {
    path: 'barcode',
    component: BarcodeComponent
  },
  {
    path: 'adjust_main_stock',
    component: AdjustStockComponent
  },
  {
    path: 'warehouse',
    component: WarehouseComponent
  },
  {
    path: 'supplier_return',
    component: SupplierReturnComponent
  },
  {
    path: 'manage_serial_numbers',
    component: ManageSerialNumbersComponent
  },
  {
    path: 'add_serial_numbers',
    component: AddSerialNumbersComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class InventoryRoutingModule {
}

export const invRouteParams = [QuickStockInComponent, SubCategoryComponent, ItemCategoryComponent,
  ItemTypeComponent, ViewStockComponent, ViewAllItemsComponent, CreateItemComponent, BrandComponent,
  BarcodeComponent, AdjustStockComponent, UOMComponent, RackComponent, ModelComponent, ManageSerialNumbersComponent,
  TransferStockComponent, WarehouseComponent, BarcodeScannerComponent, EditBarcodeComponent, AddSerialNumbersComponent,
  FilterModalComponent, SupplierReturnComponent, ColumnSelectorComponent, PageSizeSelectorComponent];
