import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';

@Injectable({
  providedIn: 'root'
})
export class UomService {

  constructor (private http: HttpClient) {

  }

  save (uom) {
    return this.http.post<any>(ApiConstants.SAVE_UOM, uom);
  }

  public findAll (page, pageSize) {
    return this.http.get(ApiConstants.GET_UOM, {params: {page: page, pageSize: pageSize}});
  }

  delete (id) {
    return this.http.delete(ApiConstants.DELETE_UOM, {params: {id: id}});
  }

  public findByName (name) {
    return this.http.get(ApiConstants.SEARCH_UOM, {params: {any: name}});
  }

}
