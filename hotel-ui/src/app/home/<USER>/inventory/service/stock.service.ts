import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ApiConstants} from '../inventory-constants';
import {TransferStock} from "../model/transfer-stock";

@Injectable({
  providedIn: 'root'
})
export class StockService {

  constructor(private http: HttpClient) {
  }

  public findAll(page, pageSize) {
    return this.http.get(ApiConstants.GET_MAIN_STOCK, {params: {page: page, pageSize: pageSize}});
  }

  public findMainStockByItemCodeAndPrice(itemCode, price) {
    return this.http.get(ApiConstants.FIND_MAIN_STOCK_BY_ITEM_CODE_PRICE, {params: {itemCode, price}});
  }

  public findMainStockRecsByItemCode(itemCode) {
    return this.http.get(ApiConstants.FIND_MAIN_STOCK_RECS_BY_ITEM_CODE, {params: {itemCode}});
  }

  addStockManual(manualStock) {
    return this.http.post<any>(ApiConstants.ADD_MAIN_STOCK_MANUAL, manualStock);
  }

  public findAllByWarehouse(warehouseCode, page, pageSize) {
    return this.http.get(ApiConstants.FIND_BY_WAREHOUSE, {
      params: {
        warehouseCode: warehouseCode,
        page: page, pageSize: pageSize
      }
    });
  }

  adjustStock(actualQuantity, stockId, remark) {
    return this.http.get(ApiConstants.ADJUST_MAIN_STOCK, {params: {actualQuantity, stockId, remark}});
  }

  public searchByWhAndBarcodeLike(barcode) {
    return this.http.get(ApiConstants.FIND_ALL_BY_BARCODE_LIKE, {params: {barcode: barcode}});
  }

  public searchByWhAndNameLike(name) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_NAME_LIKE, {params: {name: name}});
  }

  transferStock(transferStock: TransferStock) {
    return this.http.post<any>(ApiConstants.TRANSFER_STOCK, transferStock);
  }

  findByBarcodeAndWarehouse(item, warehouseCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BARCODE_AND_WAREHOUSE, {
      params: {
        barcode: item,
        warehouseCode: warehouseCode
      }
    });
  }

  findPricesByBarcodeAndWarehouse(barcode, warehouseCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BARCODE_AND_WAREHOUSE, {
      params: {
        barcode: barcode,
        warehouseCode: warehouseCode
      }
    });
  }

  findByItemCodeAndWarehouseAndPrice(itemCode, warehouseCode,price) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_CODE_AND_WAREHOUSE_AND_PRICE, {
      params: {
        itemCode: itemCode,
        warehouseCode: warehouseCode,
        price: price
      }
    });
  }

  findStockByItemCategoryAndWh(catCode, whCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_CATEGORY_WH_CODE, {
      params: {
        catCode: catCode,
        whCode: whCode
      }
    })
  }

  findStockByBrandAndWh(brandCode, whCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BRAND_WH_CODE, {
      params: {
        brandCode: brandCode,
        whCode: whCode
      }
    })
  }

  findStockSummary() {
    return this.http.get(ApiConstants.FIND_STOCK_SUMMARY);
  }

  public findReorderListByWarehouse(warehouseCode, threshold) {
    return this.http.get(ApiConstants.FIND_REORDER_LIST_BY_WAREHOUSE, {
      params: {
        warehouseCode: warehouseCode,
        threshold: threshold
      }
    });
  }

  public findReorderList(threshold, catCode, brandCode) {
    return this.http.get(ApiConstants.FIND_REORDER_LIST, {
      params: {
        threshold: threshold,
        catCode: catCode,
        brandCode: brandCode
      }
    });
  }

  public getDetailReport(groupBy?: string) {
    let params = {};
    if (groupBy) {
      params = { groupBy: groupBy };
    }

    return this.http.get(ApiConstants.GET_STOCK_DETAIL_REPORT, {
      responseType: 'blob',
      observe: 'response',
      params: params
    });
  }

  /**
   * Find stock by item ID and warehouse ID
   * @param itemId Item ID
   * @param warehouseId Warehouse ID
   * @returns Observable of Stock
   */
  public findByItemAndWarehouse(itemId: string, warehouseId: number) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_AND_WAREHOUSE, {
      params: {
        itemId: itemId,
        warehouseId: warehouseId
      }
    });
  }

  /**
   * Find stock by ID
   * @param stockId Stock ID
   * @returns Observable of Stock
   */
  public findById(stockId: string) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ID, {
      params: {
        id: stockId
      }
    });
  }

  /**
   * Save stock
   * @param stock Stock to save
   * @returns Observable of saved Stock
   */
  public save(stock: any) {
    return this.http.post(ApiConstants.SAVE_STOCK, stock);
  }

  /**
   * Find stock by barcode
   * @param barcode Barcode to search for
   * @returns Observable of Stock array
   */
  public findByBarcodeLike(barcode: string) {
    return this.http.get(ApiConstants.FIND_ALL_BY_BARCODE_LIKE, {
      params: {
        barcode: barcode
      }
    });
  }

  /**
   * Find stock by supplier
   * @param supplierCode Supplier code to search for
   * @param threshold Optional threshold value for reorder level
   * @returns Observable of Stock array
   */
  public findBySupplier(supplierCode: string, threshold?: number) {
    const params: any = { supplierCode };

    if (threshold !== undefined && threshold !== null) {
      params.threshold = threshold.toString();
    }

    return this.http.get(ApiConstants.FIND_STOCK_BY_SUPPLIER, { params });
  }
}
