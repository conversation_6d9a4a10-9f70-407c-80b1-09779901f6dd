import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SupplierReturn } from '../model/supplier-return';
import { ApiConstants } from '../inventory-constants';

@Injectable({
  providedIn: 'root'
})
export class SupplierReturnService {

  constructor(private http: HttpClient) { }

  save(supplierReturn: SupplierReturn): Observable<any> {
    return this.http.post<any>(ApiConstants.SAVE_SUPPLIER_RETURN, supplierReturn);
  }

  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(ApiConstants.GET_SUPPLIER_RETURNS, {
      params: { page: page.toString(), pageSize: pageSize.toString() }
    });
  }

  findBySupplier(supplierCode: string): Observable<any> {
    return this.http.get(ApiConstants.FIND_SUPPLIER_RETURNS_BY_SUPPLIER, {
      params: { supplierCode }
    });
  }

  findByItem(itemCode: string): Observable<any> {
    return this.http.get(ApiConstants.FIND_SUPPLIER_RETURNS_BY_ITEM, {
      params: { itemCode }
    });
  }

  findByDateRange(startDate: string, endDate: string): Observable<any> {
    return this.http.get(ApiConstants.FIND_SUPPLIER_RETURNS_BY_DATE_RANGE, {
      params: { startDate, endDate }
    });
  }

  findBySupplierAndDateRange(supplierCode: string, startDate: string, endDate: string): Observable<any> {
    return this.http.get(ApiConstants.FIND_SUPPLIER_RETURNS_BY_SUPPLIER_AND_DATE_RANGE, {
      params: { supplierCode, startDate, endDate }
    });
  }

  findById(id: string): Observable<any> {
    return this.http.get(ApiConstants.FIND_SUPPLIER_RETURN_BY_ID, {
      params: { id }
    });
  }
}
