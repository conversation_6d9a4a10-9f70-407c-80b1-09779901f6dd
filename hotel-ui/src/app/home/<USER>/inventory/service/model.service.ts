import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {ApiConstants} from "../inventory-constants";

@Injectable({
  providedIn: 'root'
})
export class ModelService {

  constructor(private http: HttpClient) { }

  save (model) {
    return this.http.post<any>(ApiConstants.SAVE_MODEL, model);
  }

  public findAll (page, pageSize) {
    return this.http.get(ApiConstants.GET_MODELS, {params: {page: page, pageSize: pageSize}});

  }

  public findByName (name) {
    return this.http.get(ApiConstants.SEARCH_MODEL, {params: {any: name}});
  }

}
