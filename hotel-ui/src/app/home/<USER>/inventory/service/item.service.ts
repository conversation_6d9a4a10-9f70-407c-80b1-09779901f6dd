import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';
import {HttpClient} from '@angular/common/http';
import {Item} from '../model/item';

@Injectable({
  providedIn: 'root'
})
export class ItemService {

  constructor(private http: HttpClient) {
  }

  public findAll(page, pageSize, sortBy = 'itemName', sortDirection = 'asc') {
    return this.http.get(ApiConstants.GET_ITEM, {
      params: {
        page: page,
        pageSize: pageSize,
        sortBy: sortBy,
        sortDirection: sortDirection
      }
    });
  }

  public findAllFiltered(
    page,
    pageSize,
    categoryId = null,
    brandId = null,
    modelId = null,
    supplierId = null,
    wholesale = null,
    retail = null,
    manageStock = null,
    active = null,
    sortBy = 'itemName',
    sortDirection = 'asc',
    groupBy = ''
  ) {
    const params: any = {
      page: page,
      pageSize: pageSize,
      sortBy: sortBy,
      sortDirection: sortDirection
    };

    if (categoryId) params.categoryId = categoryId;
    if (brandId) params.brandId = brandId;
    if (modelId) params.modelId = modelId;
    if (supplierId) params.supplierId = supplierId;
    if (wholesale !== null) params.wholesale = wholesale;
    if (retail !== null) params.retail = retail;
    if (manageStock !== null) params.manageStock = manageStock;
    if (active !== null) params.active = active;
    if (groupBy && groupBy !== '') params.groupBy = groupBy;

    return this.http.get(ApiConstants.GET_ITEM_FILTERED, { params });
  }

  public findAllForStockReport() {
    return this.http.get(ApiConstants.GET_ITEM_FOR_STOCK_REPORT);
  }

  public save(item: Item) {
    return this.http.post<any>(ApiConstants.SAVE_ITEM, item);
  }

  public delete(id) {
    return this.http.delete(ApiConstants.DELETE_ITEM, {params: {id: id}});
  }

  public findAllActiveByNameLike(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_BY_NAME_LIKE, {params: {name: search}});
  }

  public findAllByBarcodeLike(search) {
    return this.http.get(ApiConstants.FIND_ALL_BY_BARCODE_LIKE, {params: {barcode: search}});
  }

  public findActiveByNameLikeForSerialManagement(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_BY_NAME_LIKE_FOR_SERIAL, {params: {name: search}});
  }

  public findActiveByBarcodeLikeForSerialManagement(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_BY_BARCODE_LIKE_FOR_SERIAL, {params: {barcode: search}});
  }

  public findOneByBarcode(barcode) {
    return this.http.get(ApiConstants.FIND_ONE_BY_BARCODE, {params: {barcode: barcode}});
  }

  public checkAvailabilityByBarcode(barcode) {
    return this.http.get(ApiConstants.CHECK_AVAILABILITY_BY_BARCODE, {params: {barcode: barcode}});
  }

  public findOneById(search) {
    return this.http.get(ApiConstants.FIND_ONE_BY_ID, {params: {id: search}});
  }

  findAllByNameLike(search) {
    return this.http.get(ApiConstants.FIND_ALL_BY_NAME_LIKE, {params: {name: search}});
  }

  public findAllByCategory(category) {
    return this.http.post<any>(ApiConstants.FIND_BY_CATEGORY, category);
  }

  public findAllBySubCategory(subCategory) {
    return this.http.post<any>(ApiConstants.FIND_BY_SUB_CATEGORY, subCategory);
  }

  public findAllByBrand(brand) {
    return this.http.post<any>(ApiConstants.FIND_BY_BRAND, brand);
  }

  /**
   * Update barcode for an item and all related records
   * @param itemId The ID of the item
   * @param oldBarcode The current barcode
   * @param newBarcode The new barcode
   * @returns Observable with the response
   */
  public updateBarcode(itemId: string, oldBarcode: string, newBarcode: string) {
    return this.http.post<any>(ApiConstants.UPDATE_BARCODE, null, {
      params: { itemId, oldBarcode, newBarcode }
    });
  }

  /**
   * Update item checkbox properties (wholesale, retail, manageStock, active)
   * @param itemId The ID of the item
   * @param wholesale Wholesale flag
   * @param retail Retail flag
   * @param manageStock Manage stock flag
   * @param active Active flag
   * @returns Observable with the response
   */
  public updateItemProperties(itemId: string, wholesale?: boolean, retail?: boolean, manageStock?: boolean, active?: boolean) {
    const params: any = { itemId };

    if (wholesale !== undefined) params.wholesale = wholesale;
    if (retail !== undefined) params.retail = retail;
    if (manageStock !== undefined) params.manageStock = manageStock;
    if (active !== undefined) params.active = active;

    return this.http.post<any>(ApiConstants.UPDATE_ITEM_PROPERTIES, null, { params });
  }


}

