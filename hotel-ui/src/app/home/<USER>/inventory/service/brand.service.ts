import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';

@Injectable({
  providedIn: 'root'
})

export class BrandService {

  constructor (private http: HttpClient) {
  }

  save (brand) {
    return this.http.post<any>(ApiConstants.SAVE_BRAND, brand);
  }

  public findAll (page, pageSize) {
    return this.http.get(ApiConstants.GET_BRANDS, {params: {page: page, pageSize: pageSize}});

  }

  public findByName (name) {
    return this.http.get(ApiConstants.SEARCH_BRAND, {params: {any: name}});
  }

}
