import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslatePipe } from '../../translate.pipe';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { TagInputModule } from 'ngx-chips';
import { ModalModule } from 'ngx-bootstrap/modal';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { ToastrModule } from 'ngx-toastr';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { NgxPrintModule } from 'ngx-print';
import { NgxBarcodeModule } from 'ngx-barcode';
import { ConfirmationPopoverModule } from 'angular-confirmation-popover';
import { NgxLoadingModule } from 'ngx-loading';

@NgModule({
  declarations: [TranslatePipe],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PaginationModule.forRoot(),
    TypeaheadModule.forRoot(),
    TagInputModule,
    ModalModule.forRoot(),
    BsDatepickerModule.forRoot(),
    ToastrModule.forRoot(),
    BsDropdownModule.forRoot(),
    TimepickerModule.forRoot(),
    NgxPrintModule,
    NgxBarcodeModule,
    ConfirmationPopoverModule.forRoot({
      confirmButtonType: 'danger',
      popoverTitle: 'Confirmation',
      popoverMessage: 'Are you sure?'
    }),
    NgxLoadingModule.forRoot({})
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslatePipe,
    PaginationModule,
    TypeaheadModule,
    TagInputModule,
    ModalModule,
    BsDatepickerModule,
    ToastrModule,
    BsDropdownModule,
    TimepickerModule,
    NgxPrintModule,
    NgxBarcodeModule,
    ConfirmationPopoverModule,
    NgxLoadingModule
  ]
})
export class SharedModule { }