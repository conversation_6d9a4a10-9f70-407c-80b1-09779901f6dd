{"&copy;": "&copy;", "&nbsp;": "&nbsp;", "&times;": "&times;", "ADD EXPENSE": "ADD EXPENSE", "ADD MANUAL STOCK": "ADD MANUAL STOCK", "ADDITIONAL": {"HISTORY": "History", "CASHIER": "Cashier", "NOT_ASSIGNED": "Not Assigned", "SELECTED_CUSTOMER_DETAILS": "Selected Customer Details", "SET_SELECTED": "Set Selected", "N_A": "N/A"}, "ADJUST_STOCK": {"TITLE": "Adjust Stock", "ITEM_CODE": "Item Code", "ITEM_NAME": "Item Name", "WAREHOUSE": "Warehouse", "CURRENT_QUANTITY": "Current Quantity", "NEW_QUANTITY": "New Quantity", "REASON": "Reason", "REASON_REQUIRED": "*Reason is required", "ADJUST": "Adjust", "CANCEL": "Cancel"}, "ALL_PERMISSIONS": {"TITLE": "All Permissions"}, "Action": "Action", "Action Report": "Action Report", "Action Type": "Action Type", "Actions": "Actions", "Active": "Active", "Active Filters:": "Active Filters:", "Active Items Only": "Active Items Only", "Actual Amount": "Actual Amount", "Actual Quantity": "Actual Quantity", "Add Cash": "Add Cash", "Add Employee": "Add Employee", "Add Item": "Add Item", "Add Item by Barcode or Name": "Add Item by Barcode or Name", "Add Leave Types": "Add Leave Types", "Add Record": "Add Record", "Add your inventory items": "Add your inventory items", "Adding Amount": "Adding Amount", "Additional Filters": "Additional Filters", "Address": "Address", "Address 1": "Address 1", "Address Line 1": "Address Line 1", "Address Line 2": "Address Line 2", "Address Line 3": "Address Line 3", "Address:": "Address:", "Adjust": "Adjust", "Adjust Stock": "Adjust Stock", "Advanced Filters": "Advanced Filters", "All Permissions": "All Permissions", "All Routes": "All Routes", "All Types": "All Types", "Allow Selling Under Cost": "Allow Selling Under Cost", "Amount": "Amount", "Amount paying": "Amount paying", "Amount to Be Add": "Amount to Be Add", "Apply": "Apply", "Apply Filters": "Apply Filters", "Are you sure ?": "Are you sure ?", "Are you sure you want to Approve this?": "Are you sure you want to Approve this?", "Are you sure you want to Not Approve this?": "Are you sure you want to Not Approve this?", "Are you sure you want to cancel this invoice? All data will be lost.": "Are you sure you want to cancel this invoice? All data will be lost.", "Are you sure you want to delete this quotation?": "Are you sure you want to delete this quotation?", "Are you sure you want to reset letter mapping to defaults?": "Are you sure you want to reset letter mapping to defaults?", "Attendance Incentive": "Attendance Incentive", "Authorized Signature": "Authorized Signature", "Available Amount": "Available Amount", "Available Qty in Source": "Available Qty in Source", "Available Qty in Target": "Available Qty in Target", "Available Quantity": "Available Quantity", "Average Invoice:": "Average Invoice:", "BRAND": {"TITLE": "MANAGE BRANDS", "SEARCH_BRAND": "Search Brand", "BRAND_NAME": "Brand Name", "BRAND_NAME_REQUIRED": "*Brand Name is required", "ACTIVE": "Active", "CLEAR": "Clear", "UPDATE": "Update", "SAVE": "Save"}, "BUSINESS": {"TITLE": "Company Detail", "COMPANY_NAME": "Company Name", "COMPANY_NAME_PLACEHOLDER": "Enter Company Name", "COMPANY_NAME_REQUIRED": "*Company Name is required", "COMPANY_SLOGAN": "Company Slogan (Optional)", "COMPANY_SLOGAN_PLACEHOLDER": "Enter Company Slogan", "CONTACT_1": "Contact 1", "CONTACT_1_PLACEHOLDER": "Enter Contact 1", "CONTACT_1_REQUIRED": "*Contact 1 is Required", "CONTACT_2": "Contact 2", "CONTACT_2_PLACEHOLDER": "Enter Contact 2", "CONTACT_3": "Contact 3", "CONTACT_3_PLACEHOLDER": "Enter Contact 3", "ADDRESS": "Address", "ADDRESS_PLACEHOLDER": "Enter Address line 2", "ADDRESS_REQUIRED": "*Address is required", "COMPANY_EMAIL": "Company Email", "COMPANY_EMAIL_PLACEHOLDER": "Enter Company Email", "COMPANY_EMAIL_REQUIRED": "*Company Email is required", "COMPANY_REG_NO": "Company Register No", "COMPANY_REG_NO_PLACEHOLDER": "Enter Register No", "COMPANY_REG_NO_REQUIRED": "*Register no is required", "COMPANY_LOGO": "Company Logo", "SAVE": "Save"}, "BUSINESS.CONTACT_1": "BUSINESS.CONTACT_1", "BUSINESS.CONTACT_1_PLACEHOLDER": "BUSINESS.CONTACT_1_PLACEHOLDER", "BUSINESS.CONTACT_1_REQUIRED": "BUSINESS.CONTACT_1_REQUIRED", "BUSINESS.CONTACT_2": "BUSINESS.CONTACT_2", "BUSINESS.CONTACT_2_PLACEHOLDER": "BUSINESS.CONTACT_2_PLACEHOLDER", "BUSINESS.CONTACT_3": "BUSINESS.CONTACT_3", "BUSINESS.CONTACT_3_PLACEHOLDER": "BUSINESS.CONTACT_3_PLACEHOLDER", "Balance": "Balance", "Balance Due": "Balance Due", "Bank": "Bank", "Bank Name": "Bank Name", "BarCode": "BarCode", "Barcode": "Barcode", "Barcode Customization": "Barcode Customization", "Barcode Print": "Barcode Print", "Barcode Printing": "Barcode Printing", "Basic Salary": "Basic Salary", "Batch return processing": "Batch return processing", "Bill Count:": "Bill Count:", "Brand": "Brand", "Brand Name": "Brand Name", "Brand:": "Brand:", "Budgetary Allowance": "Budgetary Allowance", "Buying Price": "Buying Price", "CHEQUE PAYMENT": "CHEQUE PAYMENT", "COMMON": {"SAVE": "Save", "UPDATE": "Update", "DELETE": "Delete", "CANCEL": "Cancel", "CLEAR": "Clear", "SEARCH": "Search", "LOADING": "Loading...", "CONFIRM": "Confirm", "YES": "Yes", "NO": "No", "CLOSE": "Close", "BACK": "Back", "NEXT": "Next", "SUBMIT": "Submit", "EDIT": "Edit", "VIEW": "View", "PRINT": "Print", "ACTIONS": "Actions", "ENABLED": "Enabled", "DISABLED": "Disabled", "RESET": "Reset", "DEFAULTS": "De<PERSON>ults", "SETTINGS_SAVED": "Setting<PERSON> saved successfully", "ERROR_SAVING_SETTINGS": "Error saving settings", "ERROR_LOADING_SETTINGS": "Error loading settings"}, "CREATE ITEM": "CREATE ITEM", "CREATE PURCHASE INVOICE": "CREATE PURCHASE INVOICE", "CREATE_ITEM": {"TITLE": "CREATE ITEM", "ITEM_TYPE": "Item Type", "SELECT": "-Select-", "BARCODE": "Barcode", "BARCODE_PLACEHOLDER": "Barcode", "ITEM_AVAILABLE": "This item is already available", "ITEM_NAME": "Item Name", "ITEM_NAME_PLACEHOLDER": "Item Name", "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Description", "ITEM_CATEGORY": "Item Category", "SEARCH_CATEGORIES": "Search Item Categories", "BRAND": "Brand", "SEARCH_BRAND": "Search Brand", "MODEL": "Model", "SEARCH_MODEL": "Search Model", "UNIT": "Unit", "SEARCH_UNIT": "Search Unit", "RACK": "<PERSON><PERSON>", "SEARCH_RACK": "Search Rack", "ITEM_COST": "<PERSON><PERSON>", "SUPPLIER_DISCOUNT": "Supplier Discount", "SELLING_PRICE": "Selling <PERSON>", "SELLING_PRICE_PLACEHOLDER": "Selling <PERSON>", "CUSTOMER_DISCOUNT": "Customer Discount (% or -)", "INITIAL_QUANTITY": "Initial Quantity", "QUANTITY_PLACEHOLDER": "Available Quantity", "DEAD_STOCK_LEVEL": "Dead Stock Level", "DEAD_STOCK_PLACEHOLDER": "Dead Stock Level", "SUPPLIER": "Supplier", "SEARCH_SUPPLIER": "Search Supplier", "MANAGE_STOCK": "Manage Stock", "ACTIVE": "Active", "CALC_DISCOUNTS": "Calc Discounts", "CLEAR": "Clear", "SAVE_AND_BARCODE": "Save and Barcode", "SAVE": "Save", "DISCOUNT_CALCULATOR": "Discount Calculator", "DISCOUNT_LEVEL_1": "Discount Level 1", "DISCOUNT_LEVEL_1_PLACEHOLDER": "Enter Level 1 Discount", "DISCOUNT_LEVEL_2": "Discount Level 2", "DISCOUNT_LEVEL_2_PLACEHOLDER": "Enter Level 2 Discount", "DISCOUNT_LEVEL_3": "Discount Level 3", "DISCOUNT_LEVEL_3_PLACEHOLDER": "Enter Level 3 Discount", "ITEM_COST_PLACEHOLDER": "Enter Item Cost", "SET_DISCOUNT": "Set Discount"}, "CREATE_USER": {"TITLE": "User Registration", "HELP_BUTTON": "Help", "USER_INFO_SECTION": "User Information", "FIRST_NAME": "First Name", "FIRST_NAME_PLACEHOLDER": "Enter First Name", "FIRST_NAME_ERROR": "Please enter a first name", "LAST_NAME": "Last Name", "LAST_NAME_PLACEHOLDER": "Enter Last Name", "LAST_NAME_ERROR": "Please enter a last name", "USERNAME": "Username", "USERNAME_PLACEHOLDER": "<PERSON><PERSON> Username", "USERNAME_ERROR": "Please enter a username", "USERNAME_TAKEN": "This username is already taken", "EMAIL": "Email", "EMAIL_PLACEHOLDER": "<PERSON><PERSON>", "EMAIL_ERROR": "Please enter a valid email address", "ROLE_SECTION": "Role & Permissions", "USER_ROLE": "User Role", "SELECT_ROLE": "Select a role", "ROLE_HELP": "Select one or more roles for this user", "ASSIGNED_ROLES": "Assigned Roles:", "NO_ROLES": "No roles assigned", "SECURITY_SECTION": "Security", "PASSWORD": "Password", "PASSWORD_PLACEHOLDER": "Enter 6 Characters Password", "PASSWORD_ERROR": "Password must be exactly 6 characters", "PASSWORD_HELP": "Password must be exactly 6 characters long", "CONFIRM_PASSWORD": "Confirm Password", "CONFIRM_PASSWORD_PLACEHOLDER": "Re-enter Password", "PASSWORD_MISMATCH": "Passwords do not match", "MODULE_SECTION": "Module Permissions", "MODULES": "<PERSON><PERSON><PERSON>", "SELECT_MODULE": "Select a module", "PERMISSIONS": "Permissions", "SELECT_PERMISSION": "Select a permission", "ADD_BUTTON": "Add", "ASSIGNED_PERMISSIONS": "Assigned Permissions:", "NO_PERMISSIONS": "No permissions assigned", "PERMISSIONS_HELP": "These are the specific permissions assigned to this user", "ACCOUNT_SECTION": "Account <PERSON><PERSON>", "ACCOUNT_ACTIVE": "Account Active", "ACCOUNT_HELP": "Inactive accounts cannot log in to the system", "RESET_BUTTON": "Reset Form", "CREATE_BUTTON": "Create User", "HELP_MODAL_TITLE": "User Registration Help", "HELP_MODAL_SUBTITLE1": "Creating a New User", "HELP_MODAL_TEXT1": "Fill out all required fields marked with an asterisk (*). The system will validate your entries as you type.", "HELP_MODAL_SUBTITLE2": "Roles and Permissions", "HELP_MODAL_TEXT2": "Assign appropriate roles and permissions to control what the user can access in the system.", "HELP_MODAL_SUBTITLE3": "Password Requirements", "HELP_MODAL_TEXT3": "Passwords must be exactly 6 characters long.", "HELP_MODAL_CLOSE": "Close"}, "CREATE_USER.HELP_MODAL_SUBTITLE1": "CREATE_USER.HELP_MODAL_SUBTITLE1", "CREATE_USER.HELP_MODAL_SUBTITLE2": "CREATE_USER.HELP_MODAL_SUBTITLE2", "CREATE_USER.HELP_MODAL_SUBTITLE3": "CREATE_USER.HELP_MODAL_SUBTITLE3", "CREATE_USER.HELP_MODAL_TEXT1": "CREATE_USER.HELP_MODAL_TEXT1", "CREATE_USER.HELP_MODAL_TEXT2": "CREATE_USER.HELP_MODAL_TEXT2", "CREATE_USER.HELP_MODAL_TEXT3": "CREATE_USER.HELP_MODAL_TEXT3", "CUSTOMERS": {"NEW_CUSTOMER": "New Customer", "EDIT_CUSTOMER": "Edit Customer", "NIC": "NIC/BR", "SEARCH_NIC": "Search By NIC", "NIC_REQUIRED": "* NIC No is required", "NIC_ALREADY_USED": "* NIC No already used", "SALUTATION": "Salutation", "NAME": "Name", "ENTER_NAME": "Enter Name", "NAME_REQUIRED": "* Name is required", "ADDRESS": "Address", "ENTER_ADDRESS": "Enter Address", "ADDRESS_REQUIRED": "*Address is required", "EMAIL": "Email", "ENTER_EMAIL": "<PERSON><PERSON>", "EMAIL_REQUIRED": "*Email Address is required", "PHONE": "Contact Number", "ENTER_PHONE_1": "Enter Contact Number 1", "PHONE_1_REQUIRED": "*Contact Number 1 is required", "ENTER_PHONE_2": "Enter Contact Number 2", "ROUTE": "Route", "SELECT_ROUTE": "Select Route", "SELECTED_ROUTE": "Selected Route", "ACTIVE": "Is Active", "UPDATE": "Update", "SAVE": "Save", "CLEAR": "Clear", "MANAGE_CUSTOMERS": "Manage Customers"}, "CUSTOMERS.ENTER_PHONE_1": "CUSTOMERS.ENTER_PHONE_1", "CUSTOMERS.ENTER_PHONE_2": "CUSTOMERS.ENTER_PHONE_2", "CUSTOMERS.PHONE_1_REQUIRED": "CUSTOMERS.PHONE_1_REQUIRED", "Calc Discounts": "Calc Discounts", "Cancel": "Cancel", "Card / Voucher / Cheque No": "Card / Voucher / Cheque No", "Cash Amount": "Cash Amount", "Cash Balance": "Cash Balance", "Cash Drawer": "Cash Drawer", "Cash Drawer:": "Cash Drawer:", "Cash Flow Summary": "Cash Flow Summary", "Cash In": "Cash In", "Cash In Out Report": "Cash In Out Report", "Cash Out": "Cash Out", "CashDrawer Date": "CashDrawer Date", "Cashier": "Cashier", "Cashless Amount": "Cashless Amount", "Category": "Category", "Category:": "Category:", "Change": "Change", "Change Password": "Change Password", "Chart Information:": "Chart Information:", "Cheque Amount": "Cheque Amount", "Cheque Date": "Cheque Date", "Cheque No": "Cheque No", "Cheque:": "Cheque:", "Cheques Summary": "Cheques Summary", "STARTER_NO_MODULE_SELECTED": "Choose any module card to see all available functions for that module.", "Clear": "Clear", "Clear Filters": "Clear Filters", "Click Search to apply filters": "Click Search to apply filters", "Click on any module below to view its available functions": "Click on any module below to view its available functions", "Close": "Close", "Code Type Settings": "Code Type Settings", "Columns:": "Columns:", "Complete Summary Report": "Complete Summary Report", "Confirm and Save": "Confirm and Save", "Confirmation": "Confirmation", "Contact Number": "Contact Number", "Contact Person Name": "Contact Person Name", "Contact Person Telephone": "Contact Person Telephone", "Contact Support": "Contact Support", "Contact Us": "Contact Us", "Continue": "Continue", "Cost": "Cost", "Cost Code Letter Mapping": "Cost Code Letter Mapping", "Counter": "Counter", "Covering Employee": "Covering Employee", "Create Quotation": "Create Quotation", "Create invoices": "Create invoices", "Create your first sale": "Create your first sale", "Created By": "Created By", "Created By:": "Created By:", "Created Date": "Created Date", "Credit Report": "Credit Report", "Cumulative Values": "Cumulative Values", "Currency Position": "Currency Position", "Currency Settings": "<PERSON><PERSON><PERSON><PERSON>", "Currency Symbol": "Currency Symbol", "Current Amount": "Current Amount", "Current Balance": "Current Balance", "Current Barcode": "Current Barcode", "Current Stock": "Current Stock", "Current Stock:": "Current Stock:", "Custom Invoice Footer": "Custom Invoice Footer", "Custom Range": "Custom Range", "Customer": "Customer", "Customer Name": "Customer Name", "Customer Signature": "Customer Signature", "Customer:": "Customer:", "DESKTOP_MANAGER": {"TITLE": "Desktop Manager", "INFO_TEXT": "Add icons to your home window for quick access to your most frequently used or preferred components. These shortcuts will appear on your dashboard for easy navigation.", "ENABLED_MODULES": "Enabled Modules", "ENABLED_PERMISSIONS": "Enabled Permissions", "ADD_TO_DESKTOP": "Add to Desktop", "CURRENT_DESKTOP_PERMISSIONS": "Current Desktop Permissions", "SAVE": "Save"}, "Date": "Date", "Date:": "Date:", "Day Close": "Day Close", "Day Start": "Day Start", "Dead Stock Level": "Dead Stock Level", "Dead Stock Level:": "Dead Stock Level:", "Default Currency": "<PERSON><PERSON><PERSON>", "Department": "Department", "Deposit Cheque": "Deposit Cheque", "Deposited": "Deposited", "Deposited Cheques:": "Deposited Cheques:", "Deposited Count:": "Deposited Count:", "Description": "Description", "Designation": "Designation", "Detailed Reports": "Detailed Reports", "Details": "Details", "Disc": "Disc", "Discount": "Discount", "Discount Calculator": "Discount Calculator", "Discount Level 1": "Discount Level 1", "Discount Level 2": "Discount Level 2", "Discount Level 3": "Discount Level 3", "Discount:": "Discount:", "Do you really want to update selected?": "Do you really want to update selected?", "Do you want to confirm?": "Do you want to confirm?", "Due Date": "Due Date", "Due Date:": "Due Date:", "Duration": "Duration", "E-mail": "E-mail", "EPF No": "EPF No", "EXPENSE_TYPE": {"TITLE": "MANAGE EXPENSE TYPES", "SEARCH_EXPENSE_TYPE": "Search Expense Type", "EXPENSE_TYPE_CATEGORY": "Expense Type Category", "EXPENSE_TYPE_NAME": "Expense Type Name", "EXPENSE_CATEGORY": "Expense Category", "EXPENSE_NAME": "Expense Name", "EXPENSE_CATEGORY_REQUIRED": "*Expense Category is required", "DESCRIPTION_REQUIRED": "*Description is required", "ACTIVE": "Active", "CLEAR": "Clear", "SAVE": "Save"}, "Edit": "Edit", "Edit Barcode": "Edit Barcode", "Email": "Email", "Employee": "Employee", "Employee Hierarchy": "Employee Hierarchy", "Employee Name": "Employee Name", "End Date": "End Date", "End Date:": "End Date:", "English": "English", "Enhanced Reporting": "Enhanced Reporting", "Enter Address line 1": "Enter Address line 1", "Enter Address line 2": "Enter Address line 2", "Enter Address line 3": "Enter Address line 3", "Enter Attendance Incentive": "Enter Attendance Incentive", "Enter Barcode": "Enter Barcode", "Enter Basic Salary": "Enter Basic Salary", "Enter Budgetary Allowance": "Enter Budgetary Allowance", "Enter Comment": "Enter Comment", "Enter Contact Number": "Enter Contact Number", "Enter Department": "Enter Department", "Enter Designation": "Enter Designation", "Enter EPF": "Enter EPF", "Enter EPF No": "Enter EPF No", "Enter ETF": "Enter ETF", "Enter Email": "<PERSON><PERSON>", "Enter End Date": "Enter End Date", "Enter First Name": "Enter First Name", "Enter Housing Allowance": "Enter Housing Allowance", "Enter Invoice Date": "Enter Invoice Date", "Enter Item Cost": "Enter Item Cost", "Enter Item Name": "Enter Item Name", "Enter Joined Date": "Enter Joined Date", "Enter Leave Type": "Enter Leave Type", "Enter Level 1 Discount": "Enter Level 1 Discount", "Enter Level 2 Discount": "Enter Level 2 Discount", "Enter Level 3 Discount": "Enter Level 3 Discount", "Enter Meal Allowance": "Enter Meal Allowance", "Enter NIC No": "Enter NIC No", "Enter Name": "Enter Name", "Enter No of Leaves": "Enter No of Leaves", "Enter OT Rate PerHour": "Enter OT Rate PerHour", "Enter Reason": "Enter Reason", "Enter Responsible Name": "Enter Responsible Name", "Enter Selling Price": "<PERSON><PERSON>", "Enter Special Allowance": "Enter Special Allowance", "Enter Start Date": "Enter Start Date", "Enter Vehicle Allowance": "Enter Vehicle Allowance", "Enter Warehouse Manager Name": "Enter Warehouse Manager Name", "Enter barcode manually": "Enter barcode manually", "Enter living Allowance": "Enter living Allowance", "Enter noPay Deduction Basic": "Enter noPay Deduction Basic", "Enter noPay Deduction Epf": "Enter noPay Deduction Epf", "Enter number of stickers": "Enter number of stickers", "Epf No": "Epf No", "Estimated Profit:": "Estimated Profit:", "Example:": "Example:", "Expense Category": "Expense Category", "Expense Name": "Expense Name", "Expense Report": "Expense Report", "Expense Type": "Expense Type", "Expense Type Category": "Expense Type Category", "Expense Type Name": "Expense Type Name", "Expenses:": "Expenses:", "Export to Excel": "Export to Excel", "Export to PDF": "Export to PDF", "Extra Data": "Extra Data", "FOOTER": {"COPYRIGHT": "© {{year}} <PERSON><PERSON>ana"}, "Female": "Female", "Filter & Sort Items": "Filter & Sort Items", "Filter Options": "Filter Options", "Filter by Device or User": "Filter by Device or User", "Filter by Route": "Filter by Route", "Filter by Route:": "Filter by Route:", "Filters": "Filters", "Filters:": "Filters:", "Frequently Asked Questions": "Frequently Asked Questions", "From": "From", "From Date": "From Date", "Full Name": "Full Name", "Gender": "Gender", "General Settings": "General Settings", "Generate reports": "Generate reports", "Getting Started": "Getting Started", "Given": "Given", "Go to Admin > Business Info to configure your company details.": "Go to Admin > Business Info to configure your company details.", "Grand Total": "Grand Total", "Group By": "Group By", "Group By Filters": "Group By Filters", "Group By Options": "Group By Options", "Group By:": "Group By:", "Group by Brand": "Group by Brand", "Group by Category": "Group by Category", "Group by Model": "Group by Model", "Group by Supplier": "Group by Supplier", "HEADER": {"TASKS": "Tasks", "MANAGE_DESKTOP": "Manage Desktop", "LOGOUT": "Logout", "POS": "Point of Sale", "MENU": "<PERSON><PERSON>"}, "Help & Documentation": "Help & Documentation", "Help & Support": "Help & Support", "Here's everything you can do with Viganana": "Here's everything you can do with <PERSON><PERSON><PERSON>", "Hide Barcode": "Hide Barcode", "Home": "Home", "Housing Allowance": "Housing Allowance", "How it works": "How it works", "INVENTORY": {"ITEMS": "Items", "CATEGORIES": "Categories", "BRANDS": "Brands", "STOCK": "Stock", "LOW_STOCK": "Low Stock", "ADD_ITEM": "Add Item", "EDIT_ITEM": "<PERSON>em", "BARCODE": "Barcode", "PRICE": "Price", "COST": "Cost", "QUANTITY": "Quantity", "DESCRIPTION": "Description"}, "INVOICE": {"EDIT_SALES_INVOICE": "Edit Sales Invoice", "INVOICE_NO": "Invoice No", "DATE": "Date", "CUSTOMER_NAME": "Customer Name", "DUE_DATE": "Due Date", "PAYMENT_METHOD": "Payment Method", "STATUS": "Status", "TOTAL_AMOUNT": "Total Amount", "TOTAL_DISCOUNT": "Total Discount", "CARD_VOUCHER_CHEQUE_NO": "Card / Voucher / Cheque No", "CASH_AMOUNT": "Cash Amount", "CASHLESS_AMOUNT": "Cashless Amount", "SUB_TOTAL": "Sub Total", "BALANCE": "Balance", "ADD_NEW_ITEM": "Add New Item", "SEARCH_ITEM": "Search Item", "SEARCH_PLACEHOLDER": "Type to search items...", "QUANTITY": "Quantity", "ADD_ITEM": "Add Item", "SALES_INVOICE_RECORDS": "Sales Invoice Records", "BARCODE": "Barcode", "ITEM_NAME": "Item Name", "UNIT_PRICE": "Unit Price", "ITEM_COST": "<PERSON><PERSON>", "PRICE": "Price", "ACTIONS": "Actions", "NO_ITEMS": "No items in this invoice. Use the form above to add items.", "UPDATE": "Update", "PRINT": "Print", "CLOSE": "Close"}, "ITEM REPORT": "ITEM REPORT", "ITEM_CATEGORY": {"TITLE": "MANAGE ITEM CATEGORY", "SEARCH_CATEGORY": "Search Item Categories", "CATEGORY_NAME": "Category Name", "CATEGORY_NAME_REQUIRED": "*Category Name is required", "ACTIVE": "Active", "CLEAR": "Clear", "UPDATE": "Update", "SAVE": "Save"}, "ITEM_TYPE": {"TITLE": "MANAGE ITEM TYPE", "SEARCH_TYPE": "Search Item Types", "ITEM_NAME": "Item Name", "ITEM_NAME_REQUIRED": "*Item Type Name is required", "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Description", "DESCRIPTION_REQUIRED": "*Description is required", "ACTIVE": "Active", "CLEAR": "Clear", "UPDATE": "Update", "SAVE": "Save"}, "Improved customization options": "Improved customization options", "Initial Quantity": "Initial Quantity", "Inventory Management": "Inventory Management", "Invoice Count:": "Invoice Count:", "Invoice Created Date :": "Invoice Created Date :", "Invoice Creation Mode": "Invoice Creation Mode", "Invoice Date": "Invoice Date", "Invoice Generation": "Invoice Generation", "Invoice No": "Invoice No", "Invoice No:": "Invoice No:", "Invoice Number": "Invoice Number", "Invoice Records": "Invoice Records", "Invoices:": "Invoices:", "Is Active": "Is Active", "Item": "<PERSON><PERSON>", "Item Category": "Item Category", "Item Code": "Item Code", "Item Cost": "<PERSON><PERSON>", "Item Cost:": "Item Cost:", "Item Name": "Item Name", "Item Properties": "Item Properties", "Item Report Filters": "Item Report Filters", "Item Sales Summary Report": "Item Sales Summary Report", "Item Type": "Item Type", "Item already in the invoice. continue?": "Item already in the invoice. continue?", "Items to Return": "Items to Return", "Items:": "Items:", "Joined Date": "Joined Date", "Key Features": "Key Features", "L Pri": "L Pri", "LOGIN": {"TITLE": "Viganana POS", "SUBTITLE": "Sign In to your account", "USERNAME": "Username", "PASSWORD": "Password", "LOGIN_BUTTON": "<PERSON><PERSON>", "FOOTER": "Product of", "COMPANY": "S-OUT Tech"}, "Last 30 Days": "Last 30 Days", "Last 7 Days": "Last 7 Days", "Learn the basics": "Learn the basics", "Leave": "Leave", "Leave From": "Leave From", "Leave Request": "Leave Request", "Leave To": "Leave To", "Leave Type": "Leave Type", "Letter": "Letter", "List Price": "List Price", "Living Allowance": "Living Allowance", "Load Memory": "Load Memory", "MANAGE DEPARTMENT": "MANAGE DEPARTMENT", "MANAGE DESIGNATION": "MANAGE DESIGNATION", "MANAGE EXPENSE TYPES": "MANAGE EXPENSE TYPES", "MANAGE SALARY": "MANAGE SALARY", "MANAGE SUB ITEM CATEGORY": "MANAGE SUB ITEM CATEGORY", "MANAGE SUPPLIERS": "MANAGE SUPPLIERS", "MANAGE WAREHOUSE": "MANAGE WAREHOUSE", "MESSAGES": {"SAVE_SUCCESS": "Saved successfully", "UPDATE_SUCCESS": "Updated successfully", "DELETE_SUCCESS": "Deleted successfully", "SAVE_ERROR": "Error saving data", "UPDATE_ERROR": "Error updating data", "DELETE_ERROR": "Error deleting data", "NETWORK_ERROR": "Network error. Please try again", "SESSION_EXPIRED": "Your session has expired. Please login again", "CONFIRM_DELETE": "Are you sure you want to delete this item?"}, "MODEL": {"TITLE": "MANAGE MODELS", "SEARCH_MODEL": "Search Model", "BRAND": "Brand", "SEARCH_BRAND": "Search Brand", "BRAND_REQUIRED": "Brand is required", "MODEL_NAME": "Model Name", "MODEL_NAME_REQUIRED": "*Model Name is required", "ACTIVE": "Active", "CLEAR": "Clear", "UPDATE": "Update", "SAVE": "Save"}, "Male": "Male", "Manage Cheque": "Manage Cheque", "Manage Employee": "Manage Employee", "Manage Leaves": "Manage Leaves", "Manage Purchase Invoices": "Manage Purchase Invoices", "Manage Quotations": "Manage Quotations", "Manage Sales Invoices": "Manage Sales Invoices", "Manage Serials": "Manage Serials", "Manage Stock": "Manage Stock", "Manage Stock Only": "Manage Stock Only", "Manage your items": "Manage your items", "Map Numbers to Letters": "Map Numbers to Letters", "Meal Allowance": "Meal Allowance", "Message": "Message", "Message subject": "Message subject", "Method": "Method", "Minimum Markup Percentage": "Minimum Markup Percentage", "Mobile Responsive": "Mobile Responsive", "Model": "Model", "Model Name": "Model Name", "Mon-Fri: 9:00 AM - 6:00 PM": "Mon-Fri: 9:00 AM - 6:00 PM", "Monitor your business performance with detailed reports.": "Monitor your business performance with detailed reports.", "More Filters": "More Filters", "My Settings": "My Settings", "Name": "Name", "Net Cash Flow:": "Net Cash Flow:", "New Barcode": "New Barcode", "New filters and export options": "New filters and export options", "No": "No", "No FAQs found": "No FAQs found", "No Grouping": "No Grouping", "No Permissions Found": "No Permissions Found", "No actions found matching the current filters.": "No actions found matching the current filters.", "No camera detected": "No camera detected", "No credit invoices found": "No credit invoices found", "No income transactions found for the selected period": "No income transactions found for the selected period", "No invoices found": "No invoices found", "No items found matching the criteria": "No items found matching the criteria", "No of Leaves": "No of Leaves", "No permissions are available for your account.": "No permissions are available for your account.", "No permissions available": "No permissions available", "No profit data available for the selected period": "No profit data available for the selected period", "No, prevent selling below cost": "No, prevent selling below cost", "Note:": "Note:", "Number of Columns": "Number of Columns", "Number of Stickers": "Number of Stickers", "OT Rate PerHour": "OT Rate PerHour", "Office Address": "Office Address", "Oldest Bill Age:": "Oldest Bill Age:", "Only Code 128 barcodes are supported": "Only Code 128 barcodes are supported", "Oops! Page Not Found": "Oops! Page Not Found", "Oops! Something went wrong.": "Oops! Something went wrong.", "Opening Amount": "Opening Amount", "Opening Balance": "Opening Balance", "Operator": "Operator", "Options": "Options", "Other Party": "Other Party", "Paid Amount": "<PERSON><PERSON>", "Paper Size": "Paper Size", "Paper Size:": "Paper Size:", "Parent Item Category Name": "Parent Item Category Name", "Pay": "Pay", "Pay Balance": "Pay Balance", "Payment": "Payment", "Payment Amount:": "Payment Amount:", "Payment History": "Payment History", "Payment Method": "Payment Method", "Payment Method:": "Payment Method:", "Payment Status": "Payment Status", "Pending Bills": "Pending Bills", "Pending Cheques:": "Pending Cheques:", "Pending Count:": "Pending Count:", "Percentage": "Percentage", "Personal Preferences": "Personal Preferences", "Please allow popups for this site to enable printing.": "Please allow popups for this site to enable printing.", "Position the barcode within the camera view": "Position the barcode within the camera view", "Preview": "Preview", "Pri": "Pri", "Price": "Price", "Print": "Print", "Print Barcode": "Print Barcode", "Print Layout Settings": "Print Layout Settings", "Print Report": "Print Report", "Process your first transaction in Trade > Create Sales Invoice.": "Process your first transaction in Trade > Create Sales Invoice.", "Profit": "Profit", "Profit Margin:": "Profit <PERSON>:", "Profit Report": "Profit Report", "Profit Type": "Profit Type", "Purpose": "Purpose", "Qty": "Qty", "Quantity": "Quantity", "Quick Edit": "Quick Edit", "Quick Invoice Creator": "Quick Invoice Creator", "Quick Start Guide": "Quick Start Guide", "Quotation No": "Quotation No", "RACK": {"TITLE": "MANAGE RACKS", "SEARCH_RACK": "Search Rack", "RACK_NAME": "<PERSON>ck <PERSON>", "RACK_NAME_REQUIRED": "*Rack Name is required", "ACTIVE": "Active", "CLEAR": "Clear", "UPDATE": "Update", "SAVE": "Save"}, "REORDER REPORT": "REORDER REPORT", "ROUTE_MANAGEMENT": {"TITLE": "MANAGE ROUTES", "SEARCH_ROUTES": "Search Routes", "ROUTE_NAME": "Route Name", "ROUTE_NO": "Route Number", "ROUTE_NO_AUTO": "Route Number is automatically generated", "FROM": "From", "TO": "To", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "ROUTE_NAME_REQUIRED": "*Route Name is required", "FROM_REQUIRED": "*From is required", "TO_REQUIRED": "*To is required", "IS_ACTIVE": "Active", "CLEAR": "Clear", "SAVE": "Save"}, "Rack": "<PERSON><PERSON>", "Rack No": "Rack No", "Realized Profit": "Realized Profit", "Reason": "Reason", "Received": "Received", "Record Type": "Record Type", "Reference": "Reference", "Registration Number": "Registration Number", "Remark": "Remark", "Remarks": "Remarks", "Remove": "Remove", "Remove Cheque": "Remove Cheque", "Remove Selected Item": "Remove Selected Item", "Remove item": "Remove item", "Reporting Manager": "Reporting Manager", "Reports & Analytics": "Reports & Analytics", "Reset": "Reset", "Reset Controls": "Reset Controls", "Reset Table": "Reset Table", "Responsible Person": "Responsible Person", "Responsible Person:": "Responsible Person:", "Retail": "Retail", "Retail Items": "Retail Items", "Return": "Return", "Return Cheque": "Return Cheque", "Return Qty": "Return Qty", "Return Reason": "Return Reason", "Returned": "Returned", "Route": "Route", "Route:": "Route:", "Rows per page:": "Rows per page:", "Rows:": "Rows:", "SALES": {"NEW_INVOICE": "New Invoice", "INVOICES": "Invoices", "RETURNS": "Returns", "DISCOUNTS": "Discounts", "PAYMENTS": "Payments", "CUSTOMER": "Customer", "SUBTOTAL": "Subtotal", "TOTAL": "Total", "BALANCE": "Balance", "DUE_DATE": "Due Date", "PAYMENT_METHOD": "Payment Method", "REFERENCE": "Reference"}, "SETTINGS": {"USER_SETTINGS": "User Settings", "OVERRIDE_INFO": "These settings will override the default application settings. Settings can be enabled or disabled individually.", "DEFAULT_DISCOUNT_MODE": "Default Discount Mode", "DISCOUNT_MODE_HELP": "Choose how discounts are calculated by default", "PRINTER_TEMPLATE": "Printer <PERSON><PERSON>", "PRINTER_TEMPLATE_HELP": "Select the printer template for receipts and invoices", "SILENT_PRINTING": "Silent Printing", "SILENT_PRINT_HELP": "Enable or disable silent printing (requires Chrome with --kiosk-printing flag)", "ENABLED_OPTION": "Enabled (No Print Dialog)", "DISABLED_OPTION": "Disabled (Show Print Dialog)", "APPLICATION_LANGUAGE": "Application Language", "SELECT_LANGUAGE": "Select your preferred language:", "LANGUAGE_HELP": "Changes to language will apply immediately across the application.", "HOW_SETTINGS_WORK": "How settings work:", "SETTINGS_HELP_1": "Each setting can be enabled or disabled using the toggle switch", "SETTINGS_HELP_2": "Disabled settings will use the system default values", "SETTINGS_HELP_3": "Enabled settings will use your custom values", "SETTINGS_HELP_4": "Settings are saved per user", "SERVER_URL_NOTE": "Note: Server URL cannot be changed through settings", "RESET_TO_DEFAULTS": "Reset to Defaults", "SAVE_SETTINGS": "Save Settings", "CUSTOM_FOOTER_SIZE": "Custom Footer Size"}, "SETTINGS.SETTINGS_HELP_1": "SETTINGS.SETTINGS_HELP_1", "SETTINGS.SETTINGS_HELP_2": "SETTINGS.SETTINGS_HELP_2", "SETTINGS.SETTINGS_HELP_3": "SETTINGS.SETTINGS_HELP_3", "SETTINGS.SETTINGS_HELP_4": "SETTINGS.SETTINGS_HELP_4", "SIDEBAR": {"HOME": "Home", "INVENTORY": "Inventory", "SALES": "Sales", "PURCHASES": "Purchases", "REPORTS": "Reports", "CUSTOMERS": "Customers", "SUPPLIERS": "Suppliers", "USERS": "Users", "SETTINGS": "General Settings"}, "SITE_NAME": "Viganana POS", "STARTER": {"ADD_NEW": "Add New"}, "RESTAURANT_POS": "Restaurant POS", "STOCK REPORT": "STOCK REPORT", "SUB_CATEGORY": {"TITLE": "MANAGE SUB ITEM CATEGORY", "SEARCH_SUB_CATEGORY": "Search Sub Item Categories"}, "Salary Name": "Salary Name", "Salary Scale": "Salary Scale", "Sale Price": "Sale Price", "Sales Invoice Records": "Sales Invoice Records", "Sales Invoice Report": "Sales Invoice Report", "Sales Process": "Sales Process", "Sales Summary": "Sales Summary", "Sales Tracking": "Sales Tracking", "Save": "Save", "Save & Print": "Save & Print", "Save and Barcode": "Save and Barcode", "Scan Barcode": "Scan Barcode", "Scan Barcode:": "Scan Barcode:", "Scan or Search Barcode": "Scan or Search Barcode", "Scan or enter barcode": "Scan or enter barcode", "Search": "Search", "Search Bank": "Search Bank", "Search Brand": "Search Brand", "Search By Barcode": "Search By Barcode", "Search By Epf No": "Search By Epf No", "Search By Items": "Search By Items", "Search By Name": "Search By Name", "Search By Reporting Manager": "Search By Reporting Manager", "Search Customer name": "Search Customer name", "Search Customer:": "Search Customer:", "Search Employee": "Search Employee", "Search Expense Type": "Search Expense Type", "Search Expense Types": "Search Expense Types", "Search Item": "Search Item", "Search Item Categories": "Search Item Categories", "Search Item Name": "Search Item Name", "Search Items:": "Search Items:", "Search Model": "Search Model", "Search Rack": "Search Rack", "Search Reporting Manager": "Search Reporting Manager", "Search Status": "Search Status", "Search Sub Item Categories": "Search Sub Item Categories", "Search Supplier": "Search Supplier", "Search Supplier name": "Search Supplier name", "Search Unit": "Search Unit", "Search Warehouse": "Search Warehouse", "Search by Barcode": "Search by Barcode", "Search by Item Name": "Search by Item Name", "Search by name or barcode": "Search by name or barcode", "Search by name or number": "Search by name or number", "Search by reference": "Search by reference", "Search customer": "Search customer", "Search in remarks": "Search in remarks", "Search or Select Supplier": "Search or Select Supplier", "Select": "Select", "Select Camera": "Select Camera", "Select Customer": "Select Customer", "Select Item": "Select Item", "Select Items": "Select Items", "Select Model": "Select Model", "Select Price": "Select Price", "Select Reason": "Select Reason", "Select Supplier": "Select Supplier", "Select Warehouse": "Select Warehouse", "Select a Module": "Select a Module", "Select a Type": "Select a Type", "STARTER_SELECT_MODULE": "Select a module above to view its functions", "Select one option to group items in the stock report.": "Select one option to group items in the stock report.", "Select reason": "Select reason", "Select the Item": "Select the Item", "Selected Expense Details": "Selected Expense Details", "Selected Invoice Details": "Selected Invoice Details", "Selected Item Details": "Selected Item Details", "Selected Items": "Selected Items", "Selected Movement Details": "Selected Movement Details", "Selected Record Details": "Selected Record Details", "Selected Stock Details": "Selected Stock Details", "Selected Supplier:": "Selected Supplier:", "Selected Transaction Details": "Selected Transaction Details", "Selling Price": "Selling <PERSON>", "Selling Price:": "Selling Price:", "Send Message": "Send Message", "Send us a message": "Send us a message", "Set Discount": "Set Discount", "Set up your business information": "Set up your business information", "Show All": "Show All", "Show Barcode": "Show Barcode", "Show Barcode Element": "Show Barcode Element", "Showing all invoices for today": "Showing all invoices for today", "Silent Print": "Silent Print", "Single Sticker Size": "Single Sticker Size", "Software by S-OUT solutions": "Software by S-OUT solutions", "Software by viganana.com": "Software by viganana.com", "Sort & Group Options": "Sort & Group Options", "Sort By": "Sort By", "Sort Direction": "Sort Direction", "Source Warehouse": "Source Warehouse", "Special Allowance": "Special Allowance", "Start": "Start", "Start Date": "Start Date", "Start Date:": "Start Date:", "Start by adding your products in Inventory > Create Item.": "Start by adding your products in Inventory > Create Item.", "Status": "Status", "Sticker Size:": "Stick<PERSON> Size:", "Stock Cost:": "Stock Cost:", "Stock Moving Report": "Stock Moving Report", "Stock Qty After": "Stock Qty After", "Stock Qty Before": "Stock Qty Before", "Stock Summary": "Stock Summary", "Stock Value:": "Stock Value:", "Sub Category Name": "Sub Category Name", "Sub Total": "Sub Total", "Sub Total:": "Sub Total:", "Subject": "Subject", "Subtotal:": "Subtotal:", "Supplier": "Supplier", "Supplier Discount": "Supplier Discount", "Supplier Invoice No": "Supplier Invoice No", "Supplier Name": "Supplier Name", "Supplier Return": "Supplier Return", "Supplier Return Report": "Supplier Return Report", "Supplier Returns": "Supplier Returns", "Supplier:": "Supplier:", "System Logo": "System Logo", "Table Columns": "Table Columns", "Target Warehouse": "Target Warehouse", "Technical Support": "Technical Support", "Tel:": "Tel:", "Telephone 1": "Telephone 1", "Telephone 2": "Telephone 2", "Telephone Number": "Telephone Number", "Telephone Number 1": "Telephone Number 1", "Telephone Number 2": "Telephone Number 2", "Thank You. Come Again": "Thank You. Come Again", "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.": "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.", "This Month": "This Month", "This Week": "This Week", "This comprehensive business management system helps you manage inventory, sales, purchases, and generate detailed reports.": "This comprehensive business management system helps you manage inventory, sales, purchases, and generate detailed reports.", "This item is already available": "This item is already available", "This shows how numbers will be converted to letters in cost codes": "This shows how numbers will be converted to letters in cost codes", "Threshold Val": "Threshold Val", "Threshold Value": "Threshold Value", "Threshold:": "Threshold:", "Time Period:": "Time Period:", "To": "To", "To Date": "To Date", "Today": "Today", "Total": "Total", "Total Amount": "Total Amount", "Total Amount:": "Total Amount:", "Total Cost": "Total Cost", "Total Cost:": "Total Cost:", "Total Credit Amount:": "Total Credit Amount:", "Total Discount": "Total Discount", "Total Expense:": "Total Expense:", "Total Expenses:": "Total Expenses:", "Total Income:": "Total Income:", "Total Invoices:": "Total Invoices:", "Total Items:": "Total Items:", "Total No Of Items:": "Total No Of Items:", "Total Profit:": "Total Profit:", "Total Quantity:": "Total Quantity:", "Total Records:": "Total Records:", "Total Sales:": "Total Sales:", "Total Stickers:": "Total Stickers:", "Total Stock Cost Value:": "Total Stock Cost Value:", "Total Stock Price Value:": "Total Stock Price Value:", "Total Transactions:": "Total Transactions:", "Total Value:": "Total Value:", "Total:": "Total:", "Transactions:": "Transactions:", "Transfer": "Transfer", "Transfer Stock": "Transfer Stock", "Transferring Quantity": "Transferring Quantity", "Try different search terms": "Try different search terms", "Type": "Type", "UOM": {"TITLE": "MANAGE UNIT OF MEASURE", "SEARCH_UNIT": "Search Unit", "UNIT_NAME": "Unit Name", "SYMBOL": "Symbol", "UNIT_NAME_REQUIRED": "*Unit Name is required", "SYMBOL_REQUIRED": "*Symbol is required", "IS_WHOLE_NUMBER": "Is Whole Number", "ACTIVE": "Active", "CLEAR": "Clear", "SAVE": "Save"}, "USER_MANAGEMENT": {"TITLE": "Manage User Registration", "SEARCH_PLACEHOLDER": "Enter username", "SEARCH_BUTTON": "Search", "FULL_NAME": "Full Name", "USER_NAME": "User Name", "EMAIL": "Email", "USER_ROLE": "User Role", "EDIT_USER": "Edit User", "ADD_ITEM": "Add Item"}, "USER_PERMISSIONS": {"TITLE": "User Permissions", "ENABLED_MODULES": "Enabled Modules", "ENABLED_PERMISSIONS": "Enabled Permissions", "ADD": "Add", "CURRENT_DESKTOP_PERMISSIONS": "Current Permissions", "DONE": "Done", "SAVE": "Save", "CANCEL": "Cancel"}, "USER_PERMISSIONS_PAGE": {"TITLE": "Available Permissions"}, "USER_SETTINGS": {"TITLE": "My Settings", "PERSONAL_PREFERENCES": "Personal Preferences", "APPEARANCE": "Appearance", "NOTIFICATIONS": "Notifications", "APPEARANCE_COMING_SOON": "Appearance settings will be available soon.", "NOTIFICATIONS_COMING_SOON": "Notification settings will be available soon.", "INVOICE_CREATION_MODE": "Invoice Creation Mode", "INVOICE_CREATION_MODE_HELP": "Choose which invoice creation interface to use. Standard is the regular interface, while Sales Rep is optimized for mobile use by sales representatives."}, "Unauthorized Access": "Unauthorized Access", "Unit": "Unit", "Unit Price": "Unit Price", "Unrealized Profit": "Unrealized Profit", "Update": "Update", "Update Invoice": "Update Invoice", "Update User": "Update User", "Use Cost Codes": "Use Cost Codes", "Use Cost Codes Instead of Barcodes": "Use Cost Codes Instead of Barcodes", "Use Group By to filter items by Category, Brand, Supplier or Model": "Use Group By to filter items by Category, Brand, Supplier or Model", "Use Traditional Barcodes": "Use Traditional Barcodes", "User": "User", "User Guide": "User Guide", "User Management": "User Management", "User:": "User:", "VALIDATION": {"REQUIRED": "This field is required", "INVALID_EMAIL": "Invalid email address", "INVALID_PHONE": "Invalid phone number", "PASSWORDS_NOT_MATCH": "Passwords do not match", "MIN_LENGTH": "Minimum length is {{value}} characters", "MAX_LENGTH": "Maximum length is {{value}} characters"}, "VIEW ALL ITEMS": "VIEW ALL ITEMS", "VIEW_STOCK": {"TITLE": "VIEW STOCK", "WAREHOUSE": "Warehouse", "SELECT_WAREHOUSE": "Select Warehouse", "SEARCH_ITEM": "Search By Name", "SEARCH_BARCODE": "Search By Barcode", "BARCODE": "Barcode", "ITEM_NAME": "Item Name", "SELLING_PRICE": "Selling <PERSON>", "DEAD_STOCK_LEVEL": "Dead Stock Level", "QUANTITY": "Quantity", "SELECTED_STOCK_DETAILS": "Selected Stock Details"}, "Valid Until": "<PERSON>id <PERSON>", "Vehicle Allowance": "Vehicle Allowance", "Video Tutorials": "Video Tutorials", "View": "View", "View Invoice Details": "View Invoice Details", "View More": "View More", "View Purchase Invoice": "View Purchase Invoice", "WAREHOUSE": {"TITLE": "MANAGE WAREHOUSE", "NAME": "Name", "NAME_REQUIRED": "*Name is required", "ADDRESS": "Address", "TELEPHONE_1": "Telephone 1", "TELEPHONE_2": "Telephone 2", "EXTRA_DATA": "Extra Data", "SEARCH_WAREHOUSE": "Search Warehouse", "WAREHOUSE": "Warehouse", "SAVE": "Save", "CLEAR": "Clear"}, "WareHouse": "WareHouse", "Warehouse": "Warehouse", "Warehouse Manager": "Warehouse Manager", "Warehouse:": "Warehouse:", "Warning:": "Warning:", "Welcome to the System": "Welcome to the System", "What's New": "What's New", "When cost codes are enabled in barcode settings, numbers in cost codes will be displayed as the letters you configure here.": "When cost codes are enabled in barcode settings, numbers in cost codes will be displayed as the letters you configure here.", "Wholesale": "Wholesale", "Wholesale Items": "Wholesale Items", "Withdraw": "Withdraw", "Withdraw Amount": "Withdraw Amount", "Withdrawal": "<PERSON><PERSON><PERSON>", "Withdrawal Amount": "<PERSON><PERSON><PERSON> Amount", "Yes": "Yes", "Yes, allow selling below cost": "Yes, allow selling below cost", "Your email": "Your email", "Your message": "Your message", "Your name": "Your name", "item Name": "item Name", "noPay Deduction Basic": "noPay Deduction Basic", "noPay Deduction EPF": "noPay Deduction EPF", "not confirm": "not confirm", "search By Invoice No": "search By Invoice No", "search By Supplier": "search By Supplier", "search by date": "search by date", "set & save": "set & save", "shift works!": "shift works!", "upload your image": "upload your image"}