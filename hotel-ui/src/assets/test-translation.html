<!DOCTYPE html>
<html>
<head>
    <title>Translation Test</title>
    <script>
        function setLanguage(lang) {
            localStorage.setItem('lang', lang);
            alert('Language set to ' + lang + '. Reload the page to see changes.');
            location.reload();
        }

        function checkLanguage() {
            const lang = localStorage.getItem('lang') || 'en';
            document.getElementById('currentLang').textContent = lang;
        }
    </script>
</head>
<body onload="checkLanguage()">
    <h1>Translation Test</h1>
    <p>Current language: <span id="currentLang"></span></p>
    <button onclick="setLanguage('en')">English</button>
    <button onclick="setLanguage('sn')">Sinhala</button>
</body>
</html>
